"""
Backup System - Manual dan Otomatis Backup Database
Menggunakan WIB timezone untuk scheduling dan naming (ISOLATED dari sistem UTC lainnya)
"""

import os
import zipfile
import asyncio
import pytz
from datetime import datetime
from typing import List, Tuple, Optional
from aiogram import Router, types, F

from .auth_admin import is_admin, log_admin_activity
from .utils import safe_answer_callback

router = Router()

# Backup configuration
BACKUP_HOURS = [0, 3, 6, 9, 12, 15, 18, 21]  # WIB hours untuk auto backup
JAKARTA_TZ = pytz.timezone('Asia/Jakarta')

class BackupSystem:
    """Sistem backup dengan WIB timezone (isolated dari sistem UTC lainnya)"""
    
    def __init__(self):
        self.backup_base_path = "backups"
        os.makedirs(self.backup_base_path, exist_ok=True)
        
        # Database paths yang akan di-backup
        self.database_paths = {
            "payment": [
                "payment/database",
                "payment/pending_payments.txt",
                "payment/premium_users.txt", 
                "payment/payment_history.txt",
                "payment/trial_users.txt"
            ],
            "management": [
                "management/database",
                "management/user_data_file",
                "management/total_users.txt"
            ],
            "pusat_admin": [
                "pusat_admin/database",
                "pusat_admin/cache"
            ]
        }
    
    def get_wib_time(self) -> datetime:
        """Get current WIB time - HANYA untuk backup feature"""
        wib_now = datetime.now(JAKARTA_TZ)
        return wib_now
    
    def should_trigger_auto_backup(self) -> bool:
        """Check apakah waktunya auto backup - WIB logic"""
        wib_now = self.get_wib_time()
        current_hour = wib_now.hour
        current_minute = wib_now.minute

        should_backup = current_hour in BACKUP_HOURS and current_minute == 0
        return should_backup
    
    def generate_backup_filename(self, is_manual: bool = False) -> str:
        """Generate nama file backup berdasarkan WIB time"""
        wib_now = self.get_wib_time()

        if is_manual:
            filename = f"database_{wib_now.hour:02d}-{wib_now.minute:02d}.zip"
        else:
            filename = f"backup_{wib_now.day:02d}-{wib_now.month:02d}.zip"

        return filename
    
    def collect_database_files(self) -> List[Tuple[str, str]]:
        """Collect semua database files yang akan di-backup"""
        files_to_backup = []

        for folder_name, paths in self.database_paths.items():
            for path in paths:
                if os.path.exists(path):
                    if os.path.isfile(path):
                        # Single file
                        files_to_backup.append((path, f"{folder_name}/{os.path.basename(path)}"))
                    elif os.path.isdir(path):
                        # Directory - scan all files
                        for root, _, files in os.walk(path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                # Preserve directory structure
                                relative_path = os.path.relpath(file_path, path)
                                zip_path = f"{folder_name}/{os.path.basename(path)}/{relative_path}"
                                files_to_backup.append((file_path, zip_path))

        return files_to_backup
    
    async def create_backup_zip(self, filename: str) -> Tuple[bool, str, Optional[str]]:
        """Create backup ZIP file"""
        backup_path = os.path.join(self.backup_base_path, filename)

        try:
            # Collect files
            files_to_backup = self.collect_database_files()

            if not files_to_backup:
                error_msg = "No database files found to backup"
                return False, error_msg, None

            # Create ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path, zip_path in files_to_backup:
                    try:
                        zipf.write(file_path, zip_path)
                    except Exception:
                        continue

            # Verify ZIP created
            if os.path.exists(backup_path):
                return True, f"Backup berhasil dibuat: {filename}", backup_path
            else:
                error_msg = "ZIP file was not created"
                return False, error_msg, None

        except Exception as e:
            error_msg = f"Error creating backup: {str(e)}"
            return False, error_msg, None

# Global backup system instance
backup_system = BackupSystem()

@router.callback_query(F.data == "admin_backup")
async def handle_manual_backup(callback: types.CallbackQuery):
    """Handle manual backup request dari admin"""
    username = callback.from_user.username or ""

    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Log admin activity
    log_admin_activity(username, "Manual backup request")

    # Edit message to show processing (replace admin panel buttons)
    processing_text = "🗜️ **Mendownload Database...**"
    await callback.message.edit_text(processing_text, parse_mode="Markdown")
    await safe_answer_callback(callback)
    
    # Create backup
    filename = backup_system.generate_backup_filename(is_manual=True)
    success, message, backup_path = await backup_system.create_backup_zip(filename)
    
    if success and backup_path:
        try:
            # Delete processing message (edit to empty or delete)
            await callback.message.delete()

            # Send backup file
            with open(backup_path, 'rb') as backup_file:
                await callback.bot.send_document(
                    chat_id=callback.message.chat.id,
                    document=types.BufferedInputFile(
                        backup_file.read(),
                        filename=filename
                    )
                )

            # Log admin activity
            log_admin_activity(username, f"Manual backup completed: {filename}")
            
        except Exception as e:
            error_msg = f"Error sending backup file: {str(e)}"

            # Edit message to show error
            error_text = f"❌ **BACKUP ERROR**\n\n{error_msg}"
            await callback.message.edit_text(error_text, parse_mode="Markdown")
            
    else:
        # Backup creation failed
        # Edit message to show error
        error_text = f"❌ **BACKUP GAGAL**\n\n{message}"
        await callback.message.edit_text(error_text, parse_mode="Markdown")

        # Log admin activity
        log_admin_activity(username, f"Manual backup failed: {message}")


async def send_auto_backup_to_super_admins(bot, backup_path: str, filename: str):
    """Send auto backup ke semua super admin"""
    from .admin_config import SUPER_ADMIN_USERNAMES
    from .database import db

    sent_count = 0
    failed_count = 0

    # Get super admin user IDs dari total_users.txt berdasarkan username
    super_admin_ids = []

    for username in SUPER_ADMIN_USERNAMES:
        if username and username.strip():
            username = username.strip()

            # Cari user ID dari total_users.txt berdasarkan username
            try:
                total_users = db.read_lines("total_users.txt")
                for line in total_users:
                    if line.strip():
                        parts = line.split('|')
                        if len(parts) >= 2:
                            user_id = parts[0].strip()
                            user_username = parts[1].strip()

                            # Match username (case insensitive)
                            if user_username.lower() == username.lower():
                                super_admin_ids.append((user_id, username))
                                break

            except Exception:
                pass

    if not super_admin_ids:
        return sent_count, failed_count

    # Send backup ke setiap super admin
    for user_id, username in super_admin_ids:
        try:
            with open(backup_path, 'rb') as backup_file:
                await bot.send_document(
                    chat_id=int(user_id),
                    document=types.BufferedInputFile(
                        backup_file.read(),
                        filename=filename
                    )
                )

            sent_count += 1

            # Log admin activity
            log_admin_activity(username, f"Auto backup received: {filename}")

        except Exception:
            failed_count += 1

    return sent_count, failed_count


async def process_auto_backup(bot):
    """Process auto backup dan kirim ke super admin"""
    try:
        # Generate filename untuk auto backup
        filename = backup_system.generate_backup_filename(is_manual=False)

        # Create backup
        success, _, backup_path = await backup_system.create_backup_zip(filename)

        if success and backup_path:
            # Send ke super admins
            await send_auto_backup_to_super_admins(bot, backup_path, filename)

            # Cleanup backup file setelah dikirim (optional)
            try:
                os.remove(backup_path)
            except Exception:
                pass

    except Exception:
        pass


class AutoBackupService:
    """Service untuk auto backup dengan WIB scheduling"""

    def __init__(self, bot):
        self.bot = bot
        self.is_running = False
        self.task = None
        self.last_backup_hour = None  # Track last backup hour untuk prevent duplicate

    async def start(self):
        """Start auto backup service"""
        if self.is_running:
            return

        self.is_running = True
        self.task = asyncio.create_task(self._backup_scheduler())

    async def stop(self):
        """Stop auto backup service"""
        if not self.is_running:
            return

        self.is_running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass

    async def _backup_scheduler(self):
        """Main scheduler loop untuk auto backup"""
        while self.is_running:
            try:
                # Check setiap 30 detik untuk akurasi yang baik
                await asyncio.sleep(30)

                if backup_system.should_trigger_auto_backup():
                    wib_now = backup_system.get_wib_time()
                    current_hour = wib_now.hour

                    # Prevent duplicate backup dalam jam yang sama
                    if self.last_backup_hour != current_hour:
                        await process_auto_backup(self.bot)
                        self.last_backup_hour = current_hour

            except asyncio.CancelledError:
                break
            except Exception:
                # Continue running despite errors
                await asyncio.sleep(60)  # Wait longer on error


# Global auto backup service instance
auto_backup_service = None

def init_auto_backup_service(bot):
    """Initialize auto backup service"""
    global auto_backup_service
    auto_backup_service = AutoBackupService(bot)
    return auto_backup_service

def get_auto_backup_service():
    """Get auto backup service instance"""
    return auto_backup_service
