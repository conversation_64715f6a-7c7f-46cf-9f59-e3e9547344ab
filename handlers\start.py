from aiogram import Router, types, F
from aiogram.filters import Command
import logging
from aiogram.fsm.context import FSMContext
from management.membership import check_membership, send_membership_message, delete_join_message
from config import TUTORIAL_LINK

router = Router()

def log_user(message: types.Message):
    # logging.info(f"user: {message.text}")  # Disabled
    pass

def log_bot(text: str):
    # logging.info(f"bot: {text}")  # Disabled
    pass

async def try_auto_activate_trial(user_id: int):
    """
    Trial activation fallback untuk exempt commands.
    Hanya activate jika user eligible (belum pernah trial).
    """
    try:
        from payment.trial_service import TrialService
        trial_service = TrialService()

        # Cek apakah user sudah punya trial aktif
        is_trial_active, _ = trial_service.check_trial_status(user_id)
        if is_trial_active:
            return  # User sudah punya trial aktif

        # Cek apakah user pernah pakai trial sebelumnya
        has_used_trial = trial_service.has_used_trial_before(user_id)
        if has_used_trial:
            return  # User sudah pernah pakai trial

        # User eligible untuk trial - activate
        success = trial_service.activate_trial(user_id)
        if success:
            # logging.info(f"Trial activated for user {user_id} via exempt command fallback")  # Disabled - tidak perlu log
            pass
        else:
            # logging.warning(f"Failed to activate trial for user {user_id} via exempt command fallback")  # Disabled - tidak perlu log
            pass

    except Exception as e:
        logging.error(f"Error in trial activation fallback for user {user_id}: {e}")

fitur = [
    "/to_vcf      - konversi file ke .vcf",
    "/to_txt      - konversi file ke .txt",
    "/admin       - fitur admin/navy",
    "/manual      - input kontak manual",
    "/add         - tambah kontak ke .vcf",
    "/delete      - hapus kontak dari file",
    "/renamectc   - ganti nama kontak",
    "/renamefile  - ganti nama file",
    "/merge       - gabungkan file",
    "/split       - pecah file",
    "/count       - hitung jumlah kontak",
    "/nodup       - hapus kontak duplikat",
    "/getname     - extract nama file",
    "/generate    - generate nama file",
    "/setting     - menu pengaturan",
    "/status      - cek status akun",
]

keyboard = types.ReplyKeyboardMarkup(
    keyboard=[
        [
            types.KeyboardButton(text="/to_vcf"),
            types.KeyboardButton(text="/to_txt"),
            types.KeyboardButton(text="/admin"),
            types.KeyboardButton(text="/manual"),
        ],
        [
            types.KeyboardButton(text="/add"),
            types.KeyboardButton(text="/delete"),
            types.KeyboardButton(text="/renamectc"),
            types.KeyboardButton(text="/renamefile"),
        ],
        [
            types.KeyboardButton(text="/merge"),
            types.KeyboardButton(text="/split"),
            types.KeyboardButton(text="/count"),
            types.KeyboardButton(text="/nodup"),
        ],
        [
            types.KeyboardButton(text="/getname"),
            types.KeyboardButton(text="/generate"),
            types.KeyboardButton(text="/setting"),
            types.KeyboardButton(text="/status"),
        ],
        [
            types.KeyboardButton(text="/help"),
        ],
    ],
    resize_keyboard=True,
    one_time_keyboard=True
)

INFO_BOT = "*Bot milik @KazuhaID2*\n\n"

# Tutorial inline keyboard - ukuran tombol disesuaikan dengan lebar deskripsi
tutorial_keyboard = types.InlineKeyboardMarkup(
    inline_keyboard=[
        [
            types.InlineKeyboardButton(
                text="💡 Tutorial Penggunaan Bot",
                url=TUTORIAL_LINK if TUTORIAL_LINK else "https://t.me/KazuhaID2"
            )
        ]
    ]
)

# Help/Support inline keyboard - tombol ke 2 admin
help_keyboard = types.InlineKeyboardMarkup(
    inline_keyboard=[
        [
            types.InlineKeyboardButton(
                text="👑 ADMIN 1",
                url="https://t.me/KazuhaID2"
            ),
            types.InlineKeyboardButton(
                text="👑 ADMIN 2",
                url="https://t.me/KazuhaID3"
            )
        ]
    ]
)



@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)

    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)

    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_global(message: types.Message, state: FSMContext):
    await state.clear()
    await help_handler(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("status"), F.chat.type == "private")
async def status_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.status import status_handler
    await status_handler(message, state)

@router.message(Command("cancel"), F.chat.type == "private")
async def cancel_from_start(message: types.Message, state: FSMContext):
    # Cancel handler akan dihandle oleh handlers.cancel
    # Ini hanya untuk memastikan /cancel muncul di daftar fitur
    from handlers.cancel import cancel_handler
    await cancel_handler(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_handler(message: types.Message, state: FSMContext):
    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)

    user_id = message.from_user.id

    # Trial activation fallback untuk exempt command
    await try_auto_activate_trial(user_id)

    nama = message.from_user.full_name or message.from_user.username or "pengguna"

    # Pesan pertama: deskripsi dengan keyboard menu utama
    description_msg = (
        f"Hallo *{nama}*, selamat datang di bot\n"
        f"*Fitur bot:*\n"
        "```\n" +
        "\n".join(fitur) +
        "\n```\n" +
        INFO_BOT
    )
    await message.answer(description_msg, parse_mode="Markdown", reply_markup=keyboard)

    # Pesan kedua: tombol tutorial
    await message.answer("💡 Tutorial penggunaan bot:", reply_markup=tutorial_keyboard)

    log_bot(description_msg)

@router.message(Command("help"), F.chat.type == "private")
async def help_handler(message: types.Message, state: FSMContext):
    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    log_user(message)

    # Trial activation fallback untuk exempt command
    await try_auto_activate_trial(message.from_user.id)

    # Pesan bantuan dan support
    help_msg = (
        "🛠️ <b>BUTUH BANTUAN?</b>\n\n"
        "• ❓ Tidak paham cara pakai?\n"
        "• ⚠️ Ada error atau bug?\n"
        "• 💬 Pertanyaan lainnya?\n\n"
        "<b>Lapor ke admin sekarang!</b>\n\n"
        "👇 <b>HUBUNGI ADMIN:</b>"
    )
    await message.answer(help_msg, parse_mode="HTML", reply_markup=help_keyboard)

    log_bot(help_msg)