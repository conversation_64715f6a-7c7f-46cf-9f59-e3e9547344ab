import asyncio
import logging
import os
import time
from typing import List, Tu<PERSON>, Dict, Set
from .cache_manager import cache_manager
from .group_channel_config import get_group_id, get_channel_id

# Konfigurasi optimisasi
MAX_CONCURRENT_CHECKS = 10  # Maksimal 10 pengecekan bersamaan
CACHE_TTL_SECONDS = 300     # Cache 5 menit
BATCH_SIZE = 20             # Proses 20 user per batch
TIMEOUT_PER_CHECK = 3       # Timeout 3 detik per API call

class MembershipChecker:
    """Optimized membership checker dengan caching dan batching"""
    
    def __init__(self):
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_CHECKS)
        self.last_full_check = 0
        self.estimated_count = 0
    
    async def check_single_user_membership(self, bot, user_id: int) -> Tuple[bool, bool]:
        """
        Cek membership satu user dengan timeout dan caching
        
        Returns:
            Tuple[in_group, in_channel]
        """
        cache_key = f"membership_{user_id}"
        
        # Cek cache dulu
        cached_result = cache_manager.get(cache_key)
        if cached_result is not None:
            return cached_result["in_group"], cached_result["in_channel"]
        
        async with self.semaphore:
            try:
                # Cek membership dengan timeout
                from config import GROUP_ID, CHANNEL_ID
                in_group = await self._check_group_membership(bot, user_id, GROUP_ID)
                in_channel = await self._check_channel_membership(bot, user_id, CHANNEL_ID)
                
                # Simpan ke cache
                result = {"in_group": in_group, "in_channel": in_channel}
                cache_manager.set(cache_key, result, CACHE_TTL_SECONDS)
                
                return in_group, in_channel
                
            except asyncio.TimeoutError:
                # logging.warning(f"Timeout checking membership for user {user_id}")  # Disabled untuk mengurangi noise
                return False, False
            except Exception as e:
                # logging.warning(f"Error checking membership for user {user_id}: {e}")  # Disabled untuk mengurangi noise
                return False, False
    
    async def _check_group_membership(self, bot, user_id: int, group_id: int) -> bool:
        """Cek membership di grup dengan timeout"""
        try:
            member = await asyncio.wait_for(
                bot.get_chat_member(group_id, user_id),
                timeout=TIMEOUT_PER_CHECK
            )
            # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
            # User yang dibatasi (restricted) masih dianggap member yang sah
            return member.status in ("member", "administrator", "creator", "restricted")
        except:
            return False

    async def _check_channel_membership(self, bot, user_id: int, channel_id: int) -> bool:
        """Cek membership di channel dengan timeout"""
        try:
            member = await asyncio.wait_for(
                bot.get_chat_member(channel_id, user_id),
                timeout=TIMEOUT_PER_CHECK
            )
            # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
            # User yang dibatasi (restricted) masih dianggap member yang sah
            return member.status in ("member", "administrator", "creator", "restricted")
        except:
            return False
    
    async def check_batch_membership(self, bot, user_ids: List[int]) -> Dict[int, Tuple[bool, bool]]:
        """
        Cek membership untuk batch user secara concurrent
        
        Returns:
            Dict dengan user_id sebagai key dan (in_group, in_channel) sebagai value
        """
        tasks = []
        for user_id in user_ids:
            task = self.check_single_user_membership(bot, user_id)
            tasks.append((user_id, task))
        
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        for (user_id, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                logging.warning(f"Exception for user {user_id}: {result}")
                results[user_id] = (False, False)
            else:
                results[user_id] = result
        
        return results

    def is_user_active(self, user_id: int) -> bool:
        """
        Cek apakah user aktif (ada di grup DAN channel) - synchronous version
        Untuk digunakan di middleware dan tempat yang tidak async
        """
        try:
            # Cek cache dulu
            cache_key = f"membership_{user_id}"
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                is_active = cached_result["in_group"] and cached_result["in_channel"]
                logging.debug(f"User {user_id} membership from cache: group={cached_result['in_group']}, channel={cached_result['in_channel']}, active={is_active}")
                return is_active

            # Jika tidak ada cache, coba cek langsung (simplified check)
            logging.debug(f"No cache for user {user_id}, performing direct check")
            return self._direct_membership_check(user_id)

        except Exception as e:
            # logging.warning(f"Error checking if user {user_id} is active: {e}")  # Disabled untuk mengurangi noise
            return False

    def _direct_membership_check(self, user_id: int) -> bool:
        """Cek membership langsung menggunakan fungsi yang sudah ada"""
        try:
            import asyncio
            from management.membership import check_membership
            from chisa import bot

            # Gunakan fungsi check_membership yang sudah ada dan terbukti bekerja
            async def check_user():
                try:
                    in_group, in_channel = await check_membership(bot, user_id)
                    is_active = in_group and in_channel

                    # logging.info(f"Direct check user {user_id}: group={in_group}, channel={in_channel}, active={is_active}")  # Disabled untuk mengurangi noise

                    # Cache hasil untuk next time
                    cache_key = f"membership_{user_id}"
                    cache_manager.set(cache_key, {
                        "in_group": in_group,
                        "in_channel": in_channel
                    }, 300)  # Cache 5 menit

                    return is_active
                except Exception as e:
                    # logging.warning(f"Error in direct membership check for {user_id}: {e}")  # Disabled untuk mengurangi noise
                    return False

            # Jalankan dengan thread pool untuk menghindari event loop conflict
            import concurrent.futures

            def run_in_thread():
                # Buat event loop baru untuk thread ini
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(check_user())
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                try:
                    result = future.result(timeout=15)  # 15 detik timeout
                    return result
                except concurrent.futures.TimeoutError:
                    logging.warning(f"Timeout checking membership for {user_id}")
                    return False

        except Exception as e:
            logging.warning(f"Error in direct membership check for {user_id}: {e}")
            return False



    def get_user_ids_from_file(self) -> List[int]:
        """Baca user IDs dari file message.txt"""
        message_file = "pusat_admin/database/message.txt"
        if not os.path.exists(message_file):
            return []
        
        try:
            with open(message_file, "r", encoding="utf-8") as f:
                user_ids = []
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            user_ids.append(int(line))
                        except ValueError:
                            continue
                return user_ids
        except Exception as e:
            # logging.error(f"Error reading user IDs: {e}")  # Disabled untuk mengurangi noise
            return []
    
    async def get_active_members_count_fast(self, bot) -> int:
        """
        Hitung active members dengan optimisasi maksimal
        
        Strategi:
        1. Cek cache global dulu
        2. Jika tidak ada, lakukan pengecekan batch dengan timeout
        3. Jika terlalu lama, return estimasi
        """
        # Cek cache global
        cache_key = "active_members_count"
        cached_count = cache_manager.get(cache_key)
        if cached_count is not None:
            logging.info(f"Using cached active members count: {cached_count}")
            return cached_count
        
        user_ids = self.get_user_ids_from_file()
        if not user_ids:
            return 0
        
        logging.info(f"Checking membership for {len(user_ids)} users...")
        
        # Jika terlalu banyak user, gunakan sampling
        if len(user_ids) > 50:
            return await self._get_count_with_sampling(bot, user_ids)
        else:
            return await self._get_count_full_check(bot, user_ids)
    
    async def _get_count_with_sampling(self, bot, user_ids: List[int]) -> int:
        """Gunakan sampling untuk estimasi jika user terlalu banyak"""
        import random
        
        # Ambil sample 30% dari user (minimal 20, maksimal 50)
        sample_size = max(20, min(50, len(user_ids) // 3))
        sample_user_ids = random.sample(user_ids, sample_size)
        
        logging.info(f"Using sampling: checking {sample_size} out of {len(user_ids)} users")
        
        active_count = 0
        
        # Proses dalam batch
        for i in range(0, len(sample_user_ids), BATCH_SIZE):
            batch = sample_user_ids[i:i + BATCH_SIZE]
            batch_results = await self.check_batch_membership(bot, batch)
            
            for user_id, (in_group, in_channel) in batch_results.items():
                if in_group and in_channel:
                    active_count += 1
        
        # Estimasi total berdasarkan sample
        estimated_total = int((active_count / sample_size) * len(user_ids))
        
        # Cache hasil estimasi dengan TTL lebih pendek
        cache_manager.set("active_members_count", estimated_total, 180)  # 3 menit
        
        logging.info(f"Estimated active members: {estimated_total} (based on {active_count}/{sample_size} sample)")
        return estimated_total
    
    async def _get_count_full_check(self, bot, user_ids: List[int]) -> int:
        """Lakukan pengecekan lengkap untuk user yang sedikit"""
        active_count = 0
        
        # Proses dalam batch
        for i in range(0, len(user_ids), BATCH_SIZE):
            batch = user_ids[i:i + BATCH_SIZE]
            batch_results = await self.check_batch_membership(bot, batch)
            
            for user_id, (in_group, in_channel) in batch_results.items():
                if in_group and in_channel:
                    active_count += 1
            
            # Log progress
            processed = min(i + BATCH_SIZE, len(user_ids))
            logging.info(f"Processed {processed}/{len(user_ids)} users, active so far: {active_count}")
        
        # Cache hasil dengan TTL normal
        cache_manager.set("active_members_count", active_count, CACHE_TTL_SECONDS)
        
        # logging.info(f"Full check completed: {active_count} active members")  # Disabled untuk mengurangi noise
        return active_count

# Instance global
membership_checker = MembershipChecker()

async def get_active_members_count(bot) -> int:
    """
    Fungsi utama untuk mendapatkan jumlah active members
    
    Ini adalah interface utama yang dipanggil dari pusat_admin.py
    """
    try:
        return await membership_checker.get_active_members_count_fast(bot)
    except Exception as e:
        logging.error(f"Error in get_active_members_count: {e}")
        # Return estimasi terakhir jika ada error
        return membership_checker.estimated_count or 0

async def refresh_membership_cache(bot):
    """
    Refresh cache membership di background
    
    Fungsi ini bisa dipanggil secara periodik untuk update cache
    """
    try:
        # logging.info("Refreshing membership cache in background...")  # Disabled untuk mengurangi noise
        count = await membership_checker.get_active_members_count_fast(bot)
        membership_checker.estimated_count = count
        # logging.info(f"Background refresh completed: {count} active members")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.error(f"Error refreshing membership cache: {e}")

def clear_membership_cache():
    """Bersihkan cache membership"""
    try:
        # Hapus cache count
        cache_manager.delete("active_members_count")
        
        # Hapus cache individual user
        user_ids = membership_checker.get_user_ids_from_file()
        for user_id in user_ids:
            cache_manager.delete(f"membership_{user_id}")
        
        # logging.info("Membership cache cleared")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.error(f"Error clearing membership cache: {e}")
