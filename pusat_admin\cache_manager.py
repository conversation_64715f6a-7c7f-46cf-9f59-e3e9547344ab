import time
import json
import os
import logging
from typing import Any, Optional, Dict

class CacheManager:
    """Sistem caching sederhana dengan TTL (Time To Live)"""
    
    def __init__(self, cache_dir: str = "pusat_admin/cache"):
        self.cache_dir = cache_dir
        self.memory_cache: Dict[str, Dict] = {}
        self.ensure_cache_dir()
    
    def ensure_cache_dir(self):
        """Pastikan direktori cache ada"""
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_cache_file_path(self, key: str) -> str:
        """Dapatkan path file cache untuk key tertentu"""
        safe_key = key.replace("/", "_").replace(":", "_")
        return os.path.join(self.cache_dir, f"{safe_key}.json")
    
    def set(self, key: str, value: Any, ttl_seconds: int = 300):
        """
        Simpan data ke cache dengan TTL
        
        Args:
            key: Kunci cache
            value: Data yang akan disimpan
            ttl_seconds: Waktu hidup cache dalam detik (default 5 menit)
        """
        try:
            cache_data = {
                "value": value,
                "timestamp": time.time(),
                "ttl": ttl_seconds
            }
            
            # Simpan ke memory cache
            self.memory_cache[key] = cache_data
            
            # Simpan ke file cache
            cache_file = self._get_cache_file_path(key)
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
            logging.debug(f"Cache set: {key} (TTL: {ttl_seconds}s)")
            
        except Exception as e:
            logging.error(f"Error setting cache for {key}: {e}")
    
    def get(self, key: str) -> Optional[Any]:
        """
        Ambil data dari cache
        
        Args:
            key: Kunci cache
            
        Returns:
            Data dari cache atau None jika tidak ada/expired
        """
        try:
            # Cek memory cache dulu
            if key in self.memory_cache:
                cache_data = self.memory_cache[key]
                if self._is_valid(cache_data):
                    logging.debug(f"Cache hit (memory): {key}")
                    return cache_data["value"]
                else:
                    # Hapus dari memory jika expired
                    del self.memory_cache[key]
            
            # Cek file cache
            cache_file = self._get_cache_file_path(key)
            if os.path.exists(cache_file):
                with open(cache_file, "r", encoding="utf-8") as f:
                    cache_data = json.load(f)
                
                if self._is_valid(cache_data):
                    # Restore ke memory cache
                    self.memory_cache[key] = cache_data
                    logging.debug(f"Cache hit (file): {key}")
                    return cache_data["value"]
                else:
                    # Hapus file jika expired
                    os.remove(cache_file)
            
            logging.debug(f"Cache miss: {key}")
            return None
            
        except Exception as e:
            logging.error(f"Error getting cache for {key}: {e}")
            return None
    
    def _is_valid(self, cache_data: Dict) -> bool:
        """Cek apakah cache masih valid (belum expired)"""
        current_time = time.time()
        cache_time = cache_data.get("timestamp", 0)
        ttl = cache_data.get("ttl", 0)
        
        return (current_time - cache_time) < ttl
    
    def delete(self, key: str):
        """Hapus cache untuk key tertentu"""
        try:
            # Hapus dari memory
            if key in self.memory_cache:
                del self.memory_cache[key]
            
            # Hapus file
            cache_file = self._get_cache_file_path(key)
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
            logging.debug(f"Cache deleted: {key}")
            
        except Exception as e:
            logging.error(f"Error deleting cache for {key}: {e}")
    
    def clear_expired(self):
        """Bersihkan semua cache yang sudah expired"""
        try:
            # Bersihkan memory cache
            expired_keys = []
            for key, cache_data in self.memory_cache.items():
                if not self._is_valid(cache_data):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.memory_cache[key]
            
            # Bersihkan file cache
            if os.path.exists(self.cache_dir):
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(self.cache_dir, filename)
                        try:
                            with open(file_path, "r", encoding="utf-8") as f:
                                cache_data = json.load(f)
                            
                            if not self._is_valid(cache_data):
                                os.remove(file_path)
                        except:
                            # Hapus file yang corrupt
                            os.remove(file_path)
            
            logging.debug("Expired cache cleared")
            
        except Exception as e:
            logging.error(f"Error clearing expired cache: {e}")
    
    def get_cache_info(self) -> Dict:
        """Dapatkan informasi tentang cache"""
        try:
            memory_count = len(self.memory_cache)
            file_count = 0
            
            if os.path.exists(self.cache_dir):
                file_count = len([f for f in os.listdir(self.cache_dir) if f.endswith(".json")])
            
            return {
                "memory_entries": memory_count,
                "file_entries": file_count,
                "cache_dir": self.cache_dir
            }
            
        except Exception as e:
            logging.error(f"Error getting cache info: {e}")
            return {"error": str(e)}

# Instance global cache manager
cache_manager = CacheManager()
