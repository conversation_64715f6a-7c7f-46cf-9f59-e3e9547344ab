from aiogram import Router, types, F
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
import logging
from datetime import datetime, timezone, timedelta
from management.membership import check_membership, send_membership_message, delete_join_message

router = Router()

def log_user(message: types.Message):
    # logging.info(f"user: {message.text}")  # Disabled
    pass

def log_bot(text: str):
    # logging.info(f"bot: {text}")  # Disabled
    pass

async def try_auto_activate_trial(user_id: int):
    """
    Trial activation fallback untuk exempt commands.
    Hanya activate jika user eligible (belum pernah trial).
    """
    try:
        from payment.trial_service import TrialService
        trial_service = TrialService()

        # Cek apakah user sudah punya trial aktif
        is_trial_active, _ = trial_service.check_trial_status(user_id)
        if is_trial_active:
            return  # User sudah punya trial aktif

        # Cek apakah user pernah pakai trial sebelumnya
        has_used_trial = trial_service.has_used_trial_before(user_id)
        if has_used_trial:
            return  # User sudah pernah pakai trial

        # User eligible untuk trial - activate
        success = trial_service.activate_trial(user_id)
        if success:
            # logging.info(f"Trial activated for user {user_id} via exempt command fallback")  # Disabled - tidak perlu log
            pass
        else:
            # logging.warning(f"Failed to activate trial for user {user_id} via exempt command fallback")  # Disabled - tidak perlu log
            pass

    except Exception as e:
        logging.error(f"Error in trial activation fallback for user {user_id}: {e}")

def format_duration_display(remaining_seconds):
    """Format remaining time untuk display ke user sesuai format yang diminta"""
    if remaining_seconds <= 0:
        return "Sudah habis"

    days = int(remaining_seconds // 86400)
    hours = int((remaining_seconds % 86400) // 3600)
    minutes = int((remaining_seconds % 3600) // 60)

    if days > 0:
        return f"{days} hari {hours} jam {minutes} menit"
    elif hours > 0:
        return f"{hours} jam {minutes} menit"
    elif minutes > 0:
        return f"{minutes} menit"
    else:
        return f"{int(remaining_seconds)} detik"

def format_utc_to_wib_display(utc_datetime_str):
    """Format UTC datetime string ke WIB display format DD-MM-YYYY, HH:MM WIB"""
    try:
        # Parse UTC datetime string
        utc_dt = datetime.strptime(utc_datetime_str, "%Y-%m-%d %H:%M:%S")
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
        
        # Convert ke WIB (+7 jam)
        wib_dt = utc_dt + timedelta(hours=7)
        
        # Format: DD-MM-YYYY, HH:MM WIB
        return wib_dt.strftime("%d-%m-%Y, %H:%M WIB")
    except Exception as e:
        logging.error(f"Error formatting datetime: {e}")
        return "Format error"

def get_package_info(duration_seconds):
    """Get package info berdasarkan duration"""
    try:
        from payment.config_payment import PREMIUM_PACKAGES
        
        # Cari package yang sesuai dengan duration
        for package_key, package_info in PREMIUM_PACKAGES.items():
            if package_info["duration_seconds"] == duration_seconds:
                return package_info["name"], package_info["price"]
        
        # Default fallback
        return "Custom", "N/A"
    except Exception:
        return "N/A", "N/A"

def detect_premium_type(user_id, premium_info):
    """Deteksi apakah premium dari pembayaran atau gratis dari admin"""
    try:
        # Cek transaction_id pattern untuk premium gratis
        transaction_id = premium_info.get('transaction_id', '')

        if not transaction_id or transaction_id == 'N/A':
            return "Gratis dari Admin"

        # Cek pattern FREE_ untuk premium gratis
        if transaction_id.startswith('FREE_'):
            return "Gratis dari Admin"

        # Cek dari premium service langsung (lebih reliable)
        try:
            from payment.premium_service import PremiumService
            service = PremiumService()
            premium_users = service.get_all_premium_users()

            for user in premium_users:
                if user['user_id'] == user_id:
                    payment_method = user.get('payment_method', '')
                    if payment_method == 'Admin Gratis':
                        return "Gratis dari Admin"
                    else:
                        return "Berbayar"
        except Exception:
            pass

        # Fallback: cek di payment history
        try:
            from payment.config_payment import PAYMENT_HISTORY_DB
            import json

            with open(PAYMENT_HISTORY_DB, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        payment_data = json.loads(line)
                        if (payment_data.get('user_id') == user_id and
                            payment_data.get('transaction_id') == transaction_id):
                            payment_method = payment_data.get('payment_method', '')
                            if payment_method == 'Admin Gratis':
                                return "Gratis dari Admin"
                            else:
                                return "Berbayar"
        except FileNotFoundError:
            pass

        # Default: jika ada transaction_id tapi tidak ditemukan di history
        return "Berbayar"

    except Exception as e:
        logging.error(f"Error detecting premium type: {e}")
        return "N/A"

@router.message(Command("status"), F.chat.type == "private")
async def status_handler(message: types.Message, state: FSMContext):
    """Handler untuk command /status"""
    # Membership check untuk exempt command
    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)

    log_user(message)

    user_id = message.from_user.id
    user_name = message.from_user.full_name or message.from_user.username or "User"
    username = message.from_user.username or ""

    # Trial activation fallback untuk exempt command
    await try_auto_activate_trial(user_id)
    
    try:
        # 1. CEK ADMIN TERLEBIH DAHULU
        try:
            from pusat_admin.auth_admin import get_admin_level_by_id, get_admin_level
            
            # Cek berdasarkan user ID
            admin_level = get_admin_level_by_id(user_id)
            
            # Jika tidak ada, cek berdasarkan username
            if not admin_level and username:
                admin_level = get_admin_level(username)
            
            if admin_level in ["admin", "super_admin"]:
                status_text = f"""👑 ADMINISTRATOR

👤 Nama: {user_name}
🛡️ Status: {"Super Admin" if admin_level == "super_admin" else "Admin"}
🔑 Akses: Full Control"""
                
                await message.answer(status_text)
                log_bot(status_text)
                return
                
        except Exception as e:
            logging.error(f"Error checking admin status: {e}")
        
        # 2. CEK PREMIUM STATUS
        try:
            from payment.premium_service import PremiumService
            premium_service = PremiumService()
            
            is_premium, premium_info = premium_service.check_premium_status(user_id)
            
            if is_premium and premium_info:
                # User premium aktif
                expired_date_str = premium_info['expired_date']
                
                # Hitung remaining time
                expired_utc = datetime.strptime(expired_date_str, "%Y-%m-%d %H:%M:%S")
                expired_utc = expired_utc.replace(tzinfo=timezone.utc)
                current_utc = datetime.now(timezone.utc)
                remaining_seconds = (expired_utc - current_utc).total_seconds()
                
                # Format display
                wib_expiry = format_utc_to_wib_display(expired_date_str)
                duration_display = format_duration_display(remaining_seconds)
                
                # Deteksi package info dan tipe premium
                activated_date_str = premium_info.get('activated_date', '')
                if activated_date_str:
                    activated_utc = datetime.strptime(activated_date_str, "%Y-%m-%d %H:%M:%S")
                    activated_utc = activated_utc.replace(tzinfo=timezone.utc)
                    total_duration = (expired_utc - activated_utc).total_seconds()
                    package_name, package_price = get_package_info(int(total_duration))
                else:
                    package_name, package_price = "N/A", "N/A"
                
                premium_type = detect_premium_type(user_id, premium_info)
                
                # Format harga dengan aman
                if package_price == "N/A":
                    price_display = "N/A"
                else:
                    try:
                        price_display = f"{int(package_price):,}"
                    except (ValueError, TypeError):
                        price_display = str(package_price)

                status_text = f"""🌟 PREMIUM AKTIF

👤 Nama: {user_name}
💎 Status: Premium Member
⏰ Masa Aktif: {duration_display}
📅 Berakhir: {wib_expiry}
💰 Paket: {package_name} (Rp {price_display})
🎁 Tipe: {premium_type}

✨ Nikmati semua fitur tanpa batas"""
                
                await message.answer(status_text)
                log_bot(status_text)
                return
            else:
                # User tidak premium, lanjut ke trial check
                pass

        except Exception as e:
            logging.error(f"Error checking premium status: {e}")

        # 3. CEK TRIAL STATUS (hanya jika user TIDAK premium)
        try:
            from payment.trial_service import TrialService
            trial_service = TrialService()
            
            is_trial_active, trial_info = trial_service.check_trial_status(user_id)
            
            if is_trial_active and trial_info:
                # User trial aktif
                expired_date_str = trial_info['expired_date']
                
                # Hitung remaining time
                expired_utc = datetime.strptime(expired_date_str, "%Y-%m-%d %H:%M:%S")
                expired_utc = expired_utc.replace(tzinfo=timezone.utc)
                current_utc = datetime.now(timezone.utc)
                remaining_seconds = (expired_utc - current_utc).total_seconds()
                
                # Format display
                wib_expiry = format_utc_to_wib_display(expired_date_str)
                duration_display = format_duration_display(remaining_seconds)
                
                status_text = f"""🎁 TRIAL AKTIF

👤 Nama: {user_name}
🆓 Status: Trial Member
⏰ Sisa Trial: {duration_display}
📅 Trial Berakhir: {wib_expiry}
🎯 Fitur: Akses terbatas"""
                
                await message.answer(status_text)
                log_bot(status_text)
                return
            
            # Cek apakah user pernah pakai trial (expired)
            elif trial_service.has_used_trial_before(user_id):
                # User trial expired
                trial_record = trial_service._get_trial_record(user_id)
                if trial_record:
                    expired_date_str = trial_record['expired_date']
                    wib_expiry = format_utc_to_wib_display(expired_date_str)
                    
                    status_text = f"""⏰ TRIAL BERAKHIR

👤 Nama: {user_name}
❌ Status: Trial Expired
📅 Trial Berakhir: {wib_expiry}

💡 Ketik /start untuk membeli paket"""
                    
                    await message.answer(status_text)
                    log_bot(status_text)
                    return
                
        except Exception as e:
            logging.error(f"Error checking trial status: {e}")
        
        # 4. DEFAULT - User baru atau tidak ada status khusus
        status_text = f"""⏰ TRIAL BERAKHIR

👤 Nama: {user_name}
❌ Status: Trial Expired
📅 Trial Berakhir: -

💡 Ketik /start untuk membeli paket"""
        
        await message.answer(status_text)
        log_bot(status_text)
        
    except Exception as e:
        logging.error(f"Error in status handler: {e}")
        await message.answer("❌ Terjadi kesalahan saat mengecek status. Silakan coba lagi.")
