import asyncio
import logging
from datetime import datetime, timezone
from aiogram import Bo<PERSON>
from .payment_service import PaymentService
from .premium_service import PremiumService

from .config_payment import PAYMENT_CHECK_INTERVAL, PAYMENT_TIMEOUT

# Import failed payment tracker dengan error handling
try:
    from .failed_payment_tracker import FailedPaymentTracker
except ImportError:
    # Fallback jika import gagal
    FailedPaymentTracker = None
    logging.warning("Could not import FailedPaymentTracker, using fallback")

class PaymentChecker:
    def __init__(self, bot: Bot):
        self.bot = bot
        self.payment_service = PaymentService()
        self.premium_service = PremiumService()
        # Initialize failed tracker dengan error handling
        self.failed_tracker = FailedPaymentTracker() if FailedPaymentTracker else None
        self.is_running = False
        self.check_task = None
    
    async def start(self):
        """Start payment checking background task"""
        if self.is_running:
            return
        
        self.is_running = True
        self.check_task = asyncio.create_task(self._check_payments_loop())
        # logging.info("🔄 Payment checker started")  # Disabled untuk mengurangi noise
    
    async def stop(self):
        """Stop payment checking background task"""
        self.is_running = False
        if self.check_task:
            self.check_task.cancel()
            try:
                await self.check_task
            except asyncio.CancelledError:
                pass
        # logging.info("⏹️ Payment checker stopped")  # Disabled untuk mengurangi noise
    
    async def _check_payments_loop(self):
        """Main payment checking loop"""
        while self.is_running:
            try:
                await self._check_and_process_payments()
                await self._cleanup_expired_payments()
                await asyncio.sleep(PAYMENT_CHECK_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in payment checking loop: {e}")
                await asyncio.sleep(PAYMENT_CHECK_INTERVAL)
    
    async def _check_and_process_payments(self):
        """Check for new payments and process them"""
        try:
            # Get new payments from API
            new_payments = await self.payment_service.check_new_payments()
            
            if not new_payments:
                return
            
            # logging.info(f"Found {len(new_payments)} new payments")  # Disabled untuk mengurangi noise
            
            # Process each payment
            for mutasi in new_payments:
                await self._process_single_payment(mutasi)
                
        except Exception as e:
            logging.error(f"Error checking and processing payments: {e}")
    
    async def _process_single_payment(self, mutasi: dict):
        """Process a single payment"""
        try:
            # Check if this mutation has already been processed
            if self.payment_service.is_mutation_processed(mutasi):
                return  # Skip already processed mutations

            # Try to match with pending payment
            matched_payment = await self.payment_service.process_payment_match(mutasi)

            if matched_payment:
                # Payment matched! Use new payment success handler
                try:
                    from payment.payment_success_handler import handle_payment_success
                    await handle_payment_success(self.bot, matched_payment, mutasi)

                    # Remove user from failed list if exists (user berhasil bayar)
                    if self.failed_tracker:
                        self.failed_tracker.remove_failed_user(matched_payment['user_id'])

                    # Payment processed successfully

                except Exception as e:
                    logging.error(f"Error in payment success handler: {e}")
                    # Fallback to old method
                    await self._edit_qris_to_receipt(matched_payment, mutasi)

                # Mark mutation as processed (whether success or fail)
                self.payment_service.mark_mutation_processed(mutasi)
            else:
                # Log unmatched payment for admin review
                # logging.info(f"Unmatched payment: Rp {mutasi['amount']} from {mutasi['brand_name']}")  # Disabled untuk mengurangi noise

                # Mark unmatched mutation as processed too (to avoid repeated logs)
                self.payment_service.mark_mutation_processed(mutasi)

        except Exception as e:
            logging.error(f"Error processing single payment: {e}")
    
    async def _edit_qris_to_receipt(self, payment: dict, mutasi: dict):
        """Delete QRIS message and send payment receipt"""
        try:
            user_id = payment['user_id']
            qris_message_id = payment.get('qris_message_id')

            # Get premium info
            _, premium_info = self.premium_service.check_premium_status(user_id)

            # Format tanggal berakhir dengan WIB display
            expired_date_formatted = "N/A"
            if premium_info and premium_info['expired_date']:
                try:
                    expired_datetime = datetime.strptime(premium_info['expired_date'], "%Y-%m-%d %H:%M:%S")
                    # Convert ke UTC timezone untuk consistency
                    expired_utc = expired_datetime.replace(tzinfo=timezone.utc)
                    # Format WIB untuk display ke user
                    expired_date_formatted = self.premium_service.format_utc_to_wib_display(expired_utc)
                except:
                    expired_date_formatted = premium_info['expired_date']

            receipt_text = f"""✅ **Pembayaran Berhasil**

🎉 Premium: 30 hari aktif
💰 Total bayar: Rp {mutasi['amount']}
🏦 Metode: {mutasi['brand_name']}
📅 Berakhir: {expired_date_formatted}
🆔 ID Pay: `{payment['payment_id']}`

🚀 Semua fitur sudah aktif!"""

            # Delete QRIS message if exists
            if qris_message_id:
                try:
                    await self.bot.delete_message(chat_id=user_id, message_id=qris_message_id)
                    # QRIS message deleted
                except Exception as delete_error:
                    logging.warning(f"Could not delete QRIS message: {delete_error}")
            else:
                logging.warning(f"No qris_message_id found for payment {payment.get('payment_id', 'unknown')}")

            # Delete all active messages if exist (pesan "Bayar dulu, ya...")
            active_message_ids = payment.get('active_message_ids', [])
            if active_message_ids:
                deleted_count = 0
                for message_id in active_message_ids:
                    try:
                        await self.bot.delete_message(chat_id=user_id, message_id=message_id)
                        deleted_count += 1
                    except Exception as delete_error:
                        logging.warning(f"Could not delete active message {message_id}: {delete_error}")

                if deleted_count > 0:
                    # Active payment messages deleted
                    pass

            # Send receipt message
            try:
                await self.bot.send_message(
                    chat_id=user_id,
                    text=receipt_text,
                    parse_mode="Markdown"
                )
            except Exception as send_error:
                logging.error(f"Could not send receipt message: {send_error}")

        except Exception as e:
            logging.error(f"Error handling payment receipt: {e}")
    
    async def _cleanup_expired_payments(self):
        """Clean up expired pending payments"""
        try:
            pending_payments = self.payment_service.get_pending_payments()
            current_time = datetime.now()
            
            for payment in pending_payments:
                created_time = datetime.strptime(payment['created_at'], "%Y-%m-%d %H:%M:%S")
                time_diff = (current_time - created_time).total_seconds()
                
                # If payment is older than timeout, remove it
                if time_diff > PAYMENT_TIMEOUT:
                    # Add user to failed list
                    if self.failed_tracker:
                        self.failed_tracker.add_failed_user(
                            user_id=payment['user_id'],
                            payment_id=payment['payment_id'],
                            amount=payment['amount']
                        )

                    # Update payment history status to expired
                    try:
                        from payment.payment_history_service import PaymentHistoryService
                        history_service = PaymentHistoryService()
                        history_service.update_payment_status(payment['payment_id'], "expired")
                    except ImportError:
                        pass

                    # Use new timeout handler (TANPA kirim timeout message - sudah dihandle oleh qr_timeout_handler)
                    try:
                        from payment.qr_payment_handler import cleanup_qr_messages_timeout
                        await cleanup_qr_messages_timeout(self.bot, payment['user_id'])

                        # JANGAN kirim timeout message di sini - sudah dihandle oleh qr_timeout_handler
                        pass

                    except Exception as e:
                        logging.error(f"Error in new timeout handler: {e}")
                        # Fallback to old method
                        await self._edit_qris_to_timeout(payment)

                    # Remove from pending
                    self.payment_service.remove_pending_payment(payment['payment_id'])

                    # Cleanup amount lock
                    self.payment_service.cleanup_amount_lock(payment['amount'])

                    # logging.info(f"Removed expired payment: {payment['payment_id']}")  # Disabled untuk mengurangi noise
                    
        except Exception as e:
            logging.error(f"Error cleaning up expired payments: {e}")
    
    async def _edit_qris_to_timeout(self, payment: dict):
        """Delete QRIS message and send timeout message"""
        try:
            user_id = payment['user_id']
            qris_message_id = payment.get('qris_message_id')

            timeout_text = f"""
⏰ **Pembayaran di batalkan**

❌ QRIS sudah expired.
🔄 Ketik perintah untuk QRIS baru.
🆔 ID Pay: `{payment['payment_id']}`
"""

            # Delete QRIS message if exists
            if qris_message_id:
                try:
                    await self.bot.delete_message(chat_id=user_id, message_id=qris_message_id)
                    # logging.info(f"Deleted expired QRIS message for user {user_id}")  # Disabled untuk mengurangi noise
                except Exception as delete_error:
                    logging.warning(f"Could not delete QRIS message: {delete_error}")
            else:
                logging.warning(f"No qris_message_id found for payment {payment.get('payment_id', 'unknown')}")

            # Delete all active messages if exist
            active_message_ids = payment.get('active_message_ids', [])
            if active_message_ids:
                deleted_count = 0
                for message_id in active_message_ids:
                    try:
                        await self.bot.delete_message(chat_id=user_id, message_id=message_id)
                        deleted_count += 1
                    except Exception as delete_error:
                        logging.warning(f"Could not delete active message {message_id}: {delete_error}")

                if deleted_count > 0:
                    # Active payment messages deleted
                    pass

            # Send timeout message
            try:
                await self.bot.send_message(
                    chat_id=user_id,
                    text=timeout_text,
                    parse_mode="Markdown"
                )
            except Exception as send_error:
                logging.error(f"Could not send timeout message: {send_error}")

        except Exception as e:
            logging.error(f"Error handling QRIS timeout: {e}")
