"""
Middleware untuk mengecek apakah user sedang dalam sending mode.
Jika user dalam sending mode, bot tidak akan merespons perintah apapun.
"""

import logging
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery


class SendingModeMiddleware(BaseMiddleware):
    """
    Middleware untuk block semua response saat user dalam sending mode.
    """
    
    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message,
        data: Dict[str, Any]
    ) -> Any:
        """
        Check apakah user dalam sending mode.
        Jika ya, tidak jalankan handler (silent ignore).
        """
        
        # Skip jika bukan private chat
        if event.chat.type != "private":
            return await handler(event, data)
        
        user_id = event.from_user.id
        
        try:
            # Import sending mode check
            from utils.user_isolation import is_user_in_sending_mode
            
            # Check apakah user dalam sending mode
            if is_user_in_sending_mode(user_id):
                # Izinkan command /cancel saat sending mode
                if event.text and event.text.strip().lower() == "/cancel":
                    # Lanjut ke handler cancel
                    return await handler(event, data)

                # User dalam sending mode - TIDAK RESPON APAPUN (kecuali /cancel)
                logging.info(f"User {user_id} in sending mode - ignoring command: {event.text or 'file/media'}")
                return  # Silent ignore - tidak ada response
            
            # User tidak dalam sending mode - lanjut ke handler normal
            return await handler(event, data)
            
        except Exception as e:
            # Jika error, lanjut ke handler normal untuk safety
            logging.error(f"Error in sending mode middleware: {e}")
            return await handler(event, data)


class SendingModeCallbackMiddleware(BaseMiddleware):
    """
    Middleware untuk block callback query saat user dalam sending mode.
    """
    
    async def __call__(
        self,
        handler: Callable[[CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        """
        Check apakah user dalam sending mode untuk callback query.
        """
        
        user_id = event.from_user.id
        
        try:
            # Import sending mode check
            from utils.user_isolation import is_user_in_sending_mode
            
            # Check apakah user dalam sending mode
            if is_user_in_sending_mode(user_id):
                # User dalam sending mode - TIDAK RESPON APAPUN
                logging.info(f"User {user_id} in sending mode - ignoring callback: {event.data}")
                return  # Silent ignore - tidak ada response
            
            # User tidak dalam sending mode - lanjut ke handler normal
            return await handler(event, data)
            
        except Exception as e:
            # Jika error, lanjut ke handler normal untuk safety
            logging.error(f"Error in sending mode callback middleware: {e}")
            return await handler(event, data)
