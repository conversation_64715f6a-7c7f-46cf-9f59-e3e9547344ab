"""
<PERSON><PERSON>esan Module - <PERSON><PERSON> pesan ke user tertentu
"""

import logging
import asyncio
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_send_message_menu_keyboard, create_back_keyboard
from .utils import log_bot, parse_user_ids, clean_username, get_current_timestamp
from .database import db
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()


def search_user_by_username_or_name(search_term):
    """Cari user berdasarkan username atau nama dari total_users.txt"""
    # GUNAKAN DATABASE UTAMA (total_users.txt)
    user_data = db.read_dict_data("total_users.txt")
    search_term = search_term.lower().strip()
    if search_term.startswith("@"):
        search_term = search_term[1:]

    results = []
    for item in user_data:
        parts = item['parts']
        if len(parts) >= 5:
            user_id = parts[0]
            username = parts[1].lower()
            first_name = parts[2].lower()
            last_name = parts[3].lower()

            # Cari berdasarkan username atau nama (exact atau partial match)
            if (search_term in username or
                search_term in first_name or
                search_term in last_name):
                results.append({
                    "id": user_id,
                    "username": parts[1],
                    "first_name": parts[2],
                    "last_name": parts[3]
                })

    return results


def save_targeted_message_history(admin_username, target_info, media_type, sent_count, failed_count):
    """Simpan history pesan targeted"""
    timestamp = get_current_timestamp()
    history_entry = f"{timestamp}|{admin_username}|{target_info}|{media_type}|{sent_count}|{failed_count}"
    db.append_line("targeted_message_history.txt", history_entry)


@router.callback_query(F.data == "user_send_message")
async def show_send_message_menu(callback: types.CallbackQuery, state: FSMContext):
    """Menu kirim pesan"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    message_text = "💬 **KIRIM PESAN**"

    try:
        await callback.message.edit_text(
            message_text,
            reply_markup=create_send_message_menu_keyboard(),
            parse_mode="Markdown"
        )
        await callback.answer()
    except Exception as e:
        if "message is not modified" in str(e).lower():
            await callback.answer("✅ Menu sudah terbaru")
        else:
            logging.error(f"Error editing send message menu: {e}")
            await callback.answer("❌ Error loading menu", show_alert=True)


@router.callback_query(F.data == "user_message")
async def user_message_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk kirim pesan ke user tertentu"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    message_text = """📨 **KIRIM KE USER TERTENTU**
    
Masukkan username atau nama user:"""

    try:
        await callback.message.edit_text(
            message_text,
            reply_markup=create_back_keyboard("user_send_message"),
            parse_mode="Markdown"
        )
        await callback.answer()
    except Exception as e:
        if "message is not modified" in str(e).lower():
            await callback.answer("✅ Menu sudah terbaru")
        else:
            logging.error(f"Error editing user message menu: {e}")
            await callback.answer("❌ Error loading menu", show_alert=True)
            return

    await state.set_state(AdminStates.waiting_user_ids_message)


@router.message(AdminStates.waiting_user_ids_message, F.chat.type == "private")
async def process_user_ids_for_message(message: types.Message, state: FSMContext):
    """Proses user IDs untuk kirim pesan"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    # Parse input (pisah baris)
    user_inputs = [line.strip() for line in message.text.strip().split('\n') if line.strip()]

    if not user_inputs:
        await message.answer(
            "❌ **Format tidak valid**\n\nMasukkan username atau nama user",
            reply_markup=create_back_keyboard("user_send_message"),
            parse_mode="Markdown"
        )
        return

    # Resolve usernames/nama ke user IDs
    target_user_ids = []
    target_user_info = []  # Untuk tampilan konfirmasi
    not_found = []

    for user_input in user_inputs:
        user_input = user_input.strip()

        # HANYA terima username atau nama (TIDAK ID)
        if user_input.isdigit():
            not_found.append(f"{user_input} (ID tidak diterima)")
            continue

        # Cari berdasarkan username atau nama
        results = search_user_by_username_or_name(user_input)
        if results:
            # Ambil hasil pertama yang ditemukan
            user_data = results[0]
            target_user_ids.append(user_data['id'])

            # Format untuk tampilan: prioritas username, fallback ke nama
            if user_data['username']:
                display_name = f"@{user_data['username']}"
            else:
                full_name = f"{user_data['first_name']} {user_data['last_name']}".strip()
                display_name = full_name if full_name else f"User {user_data['id']}"

            target_user_info.append(display_name)
        else:
            not_found.append(user_input)

    if not target_user_ids:
        await message.answer(
            f"❌ **User tidak ditemukan**\n\n{chr(10).join(not_found)}",
            reply_markup=create_back_keyboard("user_send_message"),
            parse_mode="Markdown"
        )
        return

    # Simpan target user IDs ke state
    await state.update_data(target_user_ids=target_user_ids, not_found=not_found)

    # Info konfirmasi dengan daftar user yang dipilih
    info_text = "✅ **TARGET DITEMUKAN**\n\n"

    # Tampilkan daftar user yang dipilih
    for i, user_display in enumerate(target_user_info, 1):
        info_text += f"{i}. {user_display}\n"

    info_text += f"\n📝 Kirim pesan/media:"

    if not_found:
        info_text += f"\n\n⚠️ **Tidak ditemukan:**\n{chr(10).join(not_found)}"

    await message.answer(
        info_text,
        reply_markup=create_back_keyboard("user_send_message"),
        parse_mode="Markdown"
    )

    await state.set_state(AdminStates.waiting_message_for_users)


@router.message(AdminStates.waiting_message_for_users, F.chat.type == "private")
async def process_message_for_users(message: types.Message, state: FSMContext):
    """Proses pesan untuk dikirim ke user tertentu"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    # Ambil data dari state
    data = await state.get_data()
    target_user_ids = data.get('target_user_ids', [])
    
    if not target_user_ids:
        await message.answer("❌ Tidak ada target user")
        await state.clear()
        return

    # Tentukan jenis media
    media_type = "teks"
    if message.photo:
        media_type = "foto"
    elif message.video:
        media_type = "video"
    elif message.document:
        media_type = "dokumen"
    elif message.sticker:
        media_type = "stiker"
    elif message.animation:
        media_type = "animasi"

    # Tampilkan status awal
    status_msg = await message.answer(
        f"📤 **{media_type.upper()}**\n\n🔄 {len(target_user_ids)} user...",
        parse_mode="Markdown"
    )

    # Proses pengiriman
    sent = 0
    failed = 0
    bot = message.bot

    for user_id in target_user_ids:
        try:
            user_id_int = int(user_id)

            # Kirim sesuai jenis media
            if message.text:
                await bot.send_message(user_id_int, message.text)
            elif message.photo:
                await bot.send_photo(user_id_int, message.photo[-1].file_id, caption=message.caption)
            elif message.video:
                await bot.send_video(user_id_int, message.video.file_id, caption=message.caption)
            elif message.document:
                await bot.send_document(user_id_int, message.document.file_id, caption=message.caption)
            elif message.sticker:
                await bot.send_sticker(user_id_int, message.sticker.file_id)
            elif message.animation:
                await bot.send_animation(user_id_int, message.animation.file_id, caption=message.caption)

            sent += 1
        except Exception as e:
            logging.warning(f"Gagal kirim pesan ke {user_id}: {e}")
            failed += 1

        # Delay kecil
        await asyncio.sleep(0.1)

    # Hasil akhir - SIMPLE
    result_text = f"""✅ **SELESAI**

📤 {sent}/{len(target_user_ids)} user"""

    await status_msg.edit_text(
        result_text,
        reply_markup=create_send_message_menu_keyboard(),
        parse_mode="Markdown"
    )

    # Simpan history
    target_info = f"{len(target_user_ids)} user tertentu"
    save_targeted_message_history(username, target_info, media_type, sent, failed)

    await state.clear()
