"""
Premium Expiry Checker - Check dan notify user yang premium expired
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Set
from aiogram import Bo<PERSON>

from .premium_service import PremiumService


class PremiumExpiryChecker:
    """Service untuk check premium expiry dan kirim notifikasi"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        self.premium_service = PremiumService()
        self.is_running = False
        self.check_task = None
        
        # Track user yang sudah di-notify (prevent spam)
        self.notified_users: Set[int] = set()
        
        # Track user yang lagi dalam proses aktif (Grace Period)
        self.active_processes: Dict[int, dict] = {}
    
    async def start(self):
        """Start background expiry checker"""
        if self.is_running:
            return
        
        self.is_running = True
        self.check_task = asyncio.create_task(self._expiry_check_loop())
        # logging.error("Premium expiry checker started")  # Disabled
    
    async def stop(self):
        """Stop background expiry checker"""
        self.is_running = False
        if self.check_task:
            self.check_task.cancel()
            try:
                await self.check_task
            except asyncio.CancelledError:
                pass
        logging.error("Premium expiry checker stopped")
    
    async def _expiry_check_loop(self):
        """Main loop untuk check expired users"""
        while self.is_running:
            try:
                await self._check_expired_users()
                await asyncio.sleep(60)  # Check tiap 1 menit
            except Exception as e:
                logging.error(f"Error in expiry check loop: {e}")
                await asyncio.sleep(60)
    
    async def _check_expired_users(self):
        """Check semua user yang baru expired dan kirim notifikasi"""
        try:
            # Check premium expired
            await self._check_premium_expired()

            # Check trial expired
            await self._check_trial_expired()

        except Exception as e:
            logging.error(f"Error checking expired users: {e}")

    async def _check_premium_expired(self):
        """Check premium users yang expired"""
        try:
            # Get semua user premium dari database
            premium_users = self._get_all_premium_users()
            current_utc = datetime.now(timezone.utc)

            for user_data in premium_users:
                user_id = user_data['user_id']
                expired_date_str = user_data['expired_date']

                try:
                    # Parse expired date (format: "YYYY-MM-DD HH:MM:SS")
                    expired_datetime = datetime.strptime(expired_date_str, "%Y-%m-%d %H:%M:%S")

                    # Make timezone aware (UTC)
                    expired_datetime = expired_datetime.replace(tzinfo=timezone.utc)

                    # Check apakah sudah expired
                    if current_utc > expired_datetime:
                        # User sudah expired
                        await self._handle_expired_user(user_id)
                    else:
                        # User masih aktif - remove dari notified list jika ada
                        if user_id in self.notified_users:
                            self.notified_users.remove(user_id)

                except Exception as e:
                    # Silent handling untuk error processing user expiry
                    # logging.error(f"Error processing user {user_id} expiry: {e}")  # Disabled
                    pass

        except Exception as e:
            # Silent handling untuk error checking premium expired
            # logging.error(f"Error checking premium expired: {e}")  # Disabled
            pass

    async def _check_trial_expired(self):
        """Check trial users yang expired"""
        try:
            from .trial_notification_service import get_trial_notification_service

            trial_service = get_trial_notification_service()
            if trial_service:
                await trial_service.check_and_notify_expired_trials()

        except Exception as e:
            # Silent handling untuk error checking trial expired
            # logging.error(f"Error checking trial expired: {e}")  # Disabled
            pass
    
    async def _handle_expired_user(self, user_id: int):
        """Handle user yang sudah expired"""
        try:
            # Skip jika sudah di-notify (prevent spam)
            if user_id in self.notified_users:
                return
            
            # Check apakah user lagi dalam proses aktif (Grace Period)
            if user_id in self.active_processes:
                # User lagi proses - mark untuk notify nanti
                self.active_processes[user_id]['notify_on_complete'] = True
                return
            
            # User tidak ada proses aktif - kirim notifikasi langsung
            await self._send_expired_notification(user_id)
            
        except Exception as e:
            # Silent handling untuk error handling expired user
            # logging.error(f"Error handling expired user {user_id}: {e}")  # Disabled
            pass
    
    async def _send_expired_notification(self, user_id: int):
        """Kirim notifikasi premium expired ke user"""
        try:
            # Double check: pastikan user benar-benar expired
            is_premium, premium_info = self.premium_service.check_premium_status(user_id)
            
            if is_premium:
                # User masih premium - jangan kirim notifikasi
                return
            
            # User benar-benar expired - kirim notifikasi
            expired_message = """⏰ Premium Habis

💎 Ketik /start untuk upgrade premium."""
            
            await self.bot.send_message(
                chat_id=user_id,
                text=expired_message
            )
            
            # Mark sebagai sudah di-notify
            self.notified_users.add(user_id)
            
        except Exception as e:
            # Silent handling untuk user yang block bot atau unreachable
            # logging.error(f"Error sending expired notification to user {user_id}: {e}")  # Disabled
            pass
    
    def _get_all_premium_users(self) -> list:
        """Get semua user premium dari database"""
        try:
            import json
            from .config_payment import PREMIUM_USERS_DB
            
            premium_users = []
            
            try:
                with open(PREMIUM_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            user_data = json.loads(line)
                            premium_users.append(user_data)
            except FileNotFoundError:
                pass
            
            return premium_users
            
        except Exception as e:
            logging.error(f"Error getting premium users: {e}")
            return []
    
    # === Grace Period Methods ===
    
    def start_process(self, user_id: int, process_type: str):
        """Mark user sebagai sedang dalam proses (Grace Period)"""
        self.active_processes[user_id] = {
            'process_type': process_type,
            'started_at': datetime.now(),
            'notify_on_complete': False
        }
    
    async def complete_process(self, user_id: int):
        """Mark proses selesai dan kirim notifikasi jika perlu"""
        if user_id not in self.active_processes:
            return
        
        process_info = self.active_processes[user_id]
        
        # Check apakah perlu kirim notifikasi expired
        if process_info.get('notify_on_complete', False):
            await self._send_expired_notification(user_id)
        
        # Hapus dari active processes
        del self.active_processes[user_id]
    
    def check_premium_with_grace(self, user_id: int) -> bool:
        """Check premium dengan Grace Period untuk user yang lagi proses"""
        # Jika user lagi dalam proses aktif - allow (Grace Period)
        if user_id in self.active_processes:
            return True
        
        # Jika tidak ada proses aktif - check premium normal
        is_premium, _ = self.premium_service.check_premium_status(user_id)
        return is_premium
    
    def cleanup_stuck_processes(self):
        """Cleanup proses yang stuck (> 30 menit)"""
        try:
            current_time = datetime.now()
            stuck_users = []
            
            for user_id, process in self.active_processes.items():
                elapsed = (current_time - process['started_at']).total_seconds()
                if elapsed > 1800:  # 30 menit
                    stuck_users.append(user_id)
            
            for user_id in stuck_users:
                del self.active_processes[user_id]
                
        except Exception as e:
            logging.error(f"Error cleaning up stuck processes: {e}")


# Global instance
expiry_checker = None


def get_expiry_checker() -> PremiumExpiryChecker:
    """Get global expiry checker instance"""
    return expiry_checker


def init_expiry_checker(bot: Bot):
    """Initialize global expiry checker"""
    global expiry_checker
    expiry_checker = PremiumExpiryChecker(bot)
    return expiry_checker
