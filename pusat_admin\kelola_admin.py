"""
Kelola Admin Module - Manajemen admin
"""

import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_admin_manage_keyboard, create_back_keyboard
from .utils import log_bot, clean_username, format_time_ago, safe_edit_message, safe_answer_callback
from .database import db
from .auth_admin import (is_admin, is_super_admin, log_admin_activity,
                        get_admin_list, save_admin_to_database,
                        remove_admin_from_database, can_manage_admin, get_admin_level)

router = Router()


@router.callback_query(F.data == "admin_manage")
async def show_admin_manage_menu(callback: types.CallbackQuery, state: FSMContext):
    """Menu kelola admin"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    manage_text = """👥 **KELOLA ADMIN**

Management admin yang tersedia:"""

    await safe_edit_message(callback, manage_text, create_admin_manage_keyboard())
    await safe_answer_callback(callback)


@router.callback_query(F.data == "admin_list")
async def show_admin_list(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan daftar admin"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        admin_list = get_admin_list()
        
        if not admin_list:
            text = "❌ **Tidak ada admin**"
        else:
            text = f"👥 **DAFTAR ADMIN** ({len(admin_list)} admin)\n\n"

            for i, admin in enumerate(admin_list, 1):
                if admin['level'] == "super_admin":
                    level_display = "SUPER ADMIN"
                    emoji = "👑"
                else:
                    level_display = "ADMIN"
                    emoji = "👤"

                text += f"{i}. @{admin['username']} ({level_display})\n"

        await safe_edit_message(callback, text, create_back_keyboard("admin_manage"))

    except Exception as e:
        logging.error(f"Error showing admin list: {e}")
        error_text = "❌ **Error**\n\nGagal memuat daftar admin."
        await safe_edit_message(callback, error_text, create_back_keyboard("admin_manage"))

    await safe_answer_callback(callback)


@router.callback_query(F.data == "admin_add")
async def add_admin_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk menambah admin"""
    username = callback.from_user.username or ""
    if not is_admin(username):  # Admin biasa BISA tambah admin
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    add_text = """➕ **TAMBAH ADMIN**

Masukkan username admin baru:"""

    await safe_edit_message(callback, add_text, create_back_keyboard("admin_manage"))
    await safe_answer_callback(callback)
    await state.set_state(AdminStates.waiting_admin_username)


@router.message(AdminStates.waiting_admin_username, F.chat.type == "private")
async def process_add_admin(message: types.Message, state: FSMContext):
    """Proses penambahan admin"""
    username = message.from_user.username or ""
    if not is_admin(username):  # Admin biasa BISA tambah admin
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    input_text = message.text.strip()
    if not input_text:
        await message.answer("Username tidak boleh kosong. Masukkan username admin:")
        return

    # Parse username
    new_admin = clean_username(input_text)
    new_level = "admin"  # Semua admin yang ditambah adalah admin biasa

    # Cek apakah sudah admin
    admin_list = get_admin_list()
    if any(a["username"].lower() == new_admin.lower() for a in admin_list):
        await message.answer(f"❌ @{new_admin} sudah menjadi admin.")
        return

    # Cari user ID dari database user (opsional)
    from .kelola_user import get_all_users_paginated
    all_users = get_all_users_paginated()
    user_id = ""
    for user in all_users:
        if user['username'].lower() == new_admin.lower():
            user_id = user['id']
            break

    # Tambah admin ke database
    success = save_admin_to_database(user_id, new_admin, new_level)

    if not success:
        await message.answer(f"❌ Gagal menambah admin @{new_admin}")
        return

    # Update admin list untuk hitung total
    updated_admin_list = get_admin_list()

    success_text = f"""✅ **Admin berhasil ditambahkan**

👤 @{new_admin} (ADMIN)
📊 Total admin: {len(updated_admin_list)}"""

    await message.answer(
        success_text,
        reply_markup=create_admin_manage_keyboard(),
        parse_mode="Markdown"
    )

    await state.clear()


@router.callback_query(F.data == "admin_remove")
async def remove_admin_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk hapus admin"""
    username = callback.from_user.username or ""

    # Cek permission berdasarkan level admin
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Admin biasa BISA hapus admin (kecuali super admin)
    # Tidak perlu cek level di sini, cek nanti saat proses hapus

    remove_text = """🗑️ **HAPUS ADMIN**

Masukkan username admin yang ingin dihapus:"""

    await safe_edit_message(callback, remove_text, create_back_keyboard("admin_manage"))
    await safe_answer_callback(callback)
    await state.set_state(AdminStates.waiting_remove_admin_username)


@router.message(AdminStates.waiting_remove_admin_username, F.chat.type == "private")
async def process_remove_admin(message: types.Message, state: FSMContext):
    """Proses penghapusan admin"""
    username = message.from_user.username or ""
    if not is_admin(username):  # Admin biasa BISA hapus admin
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    input_text = message.text.strip()
    if not input_text:
        await message.answer("Username tidak boleh kosong. Masukkan username admin:")
        return

    # Parse username
    target_admin = clean_username(input_text)

    # Cek apakah target adalah super admin
    target_level = get_admin_level(target_admin)
    if target_level == "super_admin":
        await message.answer("❌ Super Admin tidak bisa dihapus!")
        return

    # Admin biasa tidak bisa hapus super admin, tapi bisa hapus admin biasa lain
    current_level = get_admin_level(username)
    if current_level != "super_admin" and target_level == "super_admin":
        await message.answer("❌ Admin biasa tidak bisa menghapus Super Admin!")
        return

    # Cek apakah target adalah admin
    if target_level != "admin":
        await message.answer(f"❌ @{target_admin} bukan admin.")
        return

    # Hapus admin dari database
    success = remove_admin_from_database(target_admin)

    if not success:
        await message.answer(f"❌ Gagal menghapus admin @{target_admin}")
        return

    # Update admin list untuk hitung total
    updated_admin_list = get_admin_list()

    success_text = f"""✅ **Admin berhasil dihapus**

👤 @{target_admin} (ADMIN)
📊 Total admin: {len(updated_admin_list)}"""

    await message.answer(
        success_text,
        reply_markup=create_admin_manage_keyboard(),
        parse_mode="Markdown"
    )

    await state.clear()
    log_bot(f"Admin dihapus: @{target_admin}")
    log_admin_activity(username, f"Hapus admin @{target_admin}")
