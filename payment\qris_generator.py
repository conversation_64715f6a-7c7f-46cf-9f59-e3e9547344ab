#!/usr/bin/env python3
"""
QRIS Dynamic Generator
Generate QR codes dengan auto-fill nominal untuk payment system
Updated untuk menggunakan API key refresh system
"""

import qrcode
import logging
import aiohttp
import asyncio
from io import BytesIO
from typing import Optional, <PERSON><PERSON>
from datetime import datetime

# Base QRIS dari decode qris_test.png
BASE_QRIS = "00020101021126670016COM.NOBUBANK.WWW01189360050300000879140214136166691008930303UMI51440014ID.CO.QRIS.WWW0215ID20254173289400303UMI5204541153033605802ID5918WAR SHOP OK25284556014LAMPUNG TENGAH61053415262070703A016304063D"

def calculate_crc16_ccitt(data: str) -> str:
    """Calculate CRC-16 CCITT untuk QRIS checksum"""
    try:
        crc = 0xFFFF
        for byte in data.encode():
            crc ^= byte << 8
            for _ in range(8):
                if crc & 0x8000:
                    crc = (crc << 1) ^ 0x1021
                else:
                    crc <<= 1
                crc &= 0xFFFF
        return f"{crc:04X}"
    except Exception as e:
        logging.error(f"CRC calculation error: {e}")
        return "0000"

def add_amount_to_qris(base_qris: str, amount: float) -> Tuple[Optional[str], str, str]:
    """
    Add amount field ke QRIS string
    
    Args:
        base_qris: Base QRIS string
        amount: Payment amount (e.g., 7123.00)
        
    Returns:
        Tuple[modified_qris, amount_field, new_crc] or (None, error, "")
    """
    try:
        # Format amount dengan 2 decimal places
        amount_str = f"{amount:.2f}"  # "7123.00"
        amount_length = len(amount_str)  # 7
        amount_field = f"54{amount_length:02d}{amount_str}"  # "54077123.00"
        
        logging.info(f"Adding amount field: {amount_field} for Rp {amount:,.2f}")
        
        # Find insertion point: sebelum "6304" (CRC section)
        crc_pos = base_qris.find("6304")
        if crc_pos == -1:
            error_msg = "CRC section (6304) not found in QRIS"
            logging.error(error_msg)
            return None, error_msg, ""
        
        # Insert amount field sebelum CRC
        qris_before_crc = base_qris[:crc_pos]
        new_qris_without_crc = qris_before_crc + amount_field
        
        # Calculate new CRC
        crc_data = new_qris_without_crc + "6304"
        new_crc = calculate_crc16_ccitt(crc_data)
        
        # Complete QRIS dengan new CRC
        final_qris = new_qris_without_crc + "6304" + new_crc
        
        # Silent QRIS generation
        return final_qris, amount_field, new_crc
        
    except Exception as e:
        error_msg = f"Error adding amount to QRIS: {e}"
        logging.error(error_msg)
        return None, error_msg, ""

def generate_qr_image(qris_text: str, size: int = 512) -> Optional[BytesIO]:
    """
    Generate QR image dari QRIS text
    
    Args:
        qris_text: Modified QRIS string dengan amount
        size: QR image size in pixels
        
    Returns:
        BytesIO buffer containing PNG image or None if error
    """
    try:
        # Create QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(qris_text)
        qr.make(fit=True)
        
        # Create image
        qr_image = qr.make_image(fill_color="black", back_color="white")
        qr_image = qr_image.resize((size, size))
        
        # Convert to BytesIO
        img_buffer = BytesIO()
        qr_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # Silent QR image generation
        return img_buffer
        
    except Exception as e:
        logging.error(f"Error generating QR image: {e}")
        return None

def generate_payment_qr(amount: float, size: int = 512) -> Optional[BytesIO]:
    """
    Generate complete payment QR dengan auto-fill amount
    
    Args:
        amount: Payment amount (e.g., 7123.00)
        size: QR image size in pixels
        
    Returns:
        BytesIO buffer containing PNG image or None if error
    """
    try:
        # Silent QR generation
        modified_qris, amount_field, new_crc = add_amount_to_qris(BASE_QRIS, amount)

        if modified_qris is None:
            logging.error(f"Failed to modify QRIS: {amount_field}")
            return None

        # Generate QR image
        qr_buffer = generate_qr_image(modified_qris, size)

        if qr_buffer is None:
            logging.error("Failed to generate QR image")
            return None
        return qr_buffer
        
    except Exception as e:
        logging.error(f"Error generating payment QR: {e}")
        return None

def validate_qris_format(qris_text: str) -> bool:
    """
    Validate QRIS format
    
    Args:
        qris_text: QRIS string to validate
        
    Returns:
        True if valid format, False otherwise
    """
    try:
        # Basic validation
        if not qris_text or len(qris_text) < 50:
            return False
        
        # Check for required fields
        required_fields = ["0002", "0101", "6304"]
        for field in required_fields:
            if field not in qris_text:
                return False
        
        # Check CRC position
        if not qris_text.endswith("6304" + qris_text[-4:]):
            return False
        
        return True
        
    except Exception:
        return False

def get_qris_info(qris_text: str) -> dict:
    """
    Extract information from QRIS string
    
    Args:
        qris_text: QRIS string
        
    Returns:
        Dictionary with QRIS information
    """
    try:
        info = {
            "length": len(qris_text),
            "has_amount": "54" in qris_text,
            "crc": qris_text[-4:] if len(qris_text) >= 4 else "",
            "valid": validate_qris_format(qris_text)
        }
        
        # Extract amount if present
        if info["has_amount"]:
            try:
                amount_pos = qris_text.find("54")
                if amount_pos != -1:
                    # Get length of amount field
                    length_str = qris_text[amount_pos+2:amount_pos+4]
                    amount_length = int(length_str)
                    # Extract amount
                    amount_str = qris_text[amount_pos+4:amount_pos+4+amount_length]
                    info["amount"] = float(amount_str)
            except Exception:
                info["amount"] = None
        
        return info
        
    except Exception as e:
        logging.error(f"Error extracting QRIS info: {e}")
        return {"error": str(e)}

# Test function untuk development
def test_qris_generation():
    """Test QRIS generation dengan sample amounts"""
    test_amounts = [5000.00, 7123.00, 12456.00, 20789.00]
    
    print("🧪 Testing QRIS Generation")
    print("=" * 50)
    
    for amount in test_amounts:
        print(f"\n💰 Testing amount: Rp {amount:,.2f}")
        
        # Generate QR
        qr_buffer = generate_payment_qr(amount)
        
        if qr_buffer:
            print(f"✅ QR generated successfully")
            print(f"📊 Buffer size: {len(qr_buffer.getvalue())} bytes")
        else:
            print(f"❌ QR generation failed")
    
    print(f"\n🎯 Test completed!")

if __name__ == "__main__":
    # Run test if executed directly
    test_qris_generation()
