import pandas as pd
import logging
import aiofiles
import re
import asyncio
import time
from utils.number_cleaner import extract_valid_numbers_from_lines
from utils.phone_converter import batch_convert_08_to_62
import chardet

# Daftar encoding yang didukung (urutan berdasarkan kemungkinan)
SUPPORTED_ENCODINGS = [
    'utf-8',
    'utf-8-sig',
    'latin-1',  # Tambahan untuk file dengan karakter extended ASCII
    'cp1252',   # Windows-1252 (umum di Windows)
    'iso-8859-1',  # ISO Latin-1
    'utf-16',
    'utf-16-le',
    'utf-16-be',
    'utf-32',
    'utf-32-le',
    'utf-32-be'
]

def detect_file_encoding(file_path, max_bytes=10000):
    """
    Deteksi encoding file dengan mencoba berbagai encoding yang didukung.
    Returns: (encoding, confidence) atau ('utf-8', 0.0) jika gagal
    """
    try:
        # Baca sample file untuk deteksi
        with open(file_path, 'rb') as f:
            raw_data = f.read(max_bytes)

        # Coba deteksi dengan chardet
        detected = chardet.detect(raw_data)
        if detected and detected.get('encoding'):
            detected_encoding = detected['encoding'].lower()
            confidence = detected.get('confidence', 0.0)

            # Mapping encoding yang terdeteksi ke encoding yang didukung
            encoding_mapping = {
                'utf-8': 'utf-8',
                'utf-8-sig': 'utf-8-sig',
                'utf-16': 'utf-16',
                'utf-16le': 'utf-16-le',
                'utf-16be': 'utf-16-be',
                'utf-32': 'utf-32',
                'utf-32le': 'utf-32-le',
                'utf-32be': 'utf-32-be',
                'ascii': 'utf-8',  # ASCII kompatibel dengan UTF-8
                'latin-1': 'latin-1',
                'iso-8859-1': 'iso-8859-1',
                'cp1252': 'cp1252',
                'windows-1252': 'cp1252',
            }

            mapped_encoding = encoding_mapping.get(detected_encoding, detected_encoding)
            if mapped_encoding in SUPPORTED_ENCODINGS:
                return mapped_encoding, confidence

        # Jika chardet gagal, coba manual dengan encoding yang didukung
        for encoding in SUPPORTED_ENCODINGS:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1000)  # Coba baca sedikit
                return encoding, 0.8  # Confidence manual
            except (UnicodeDecodeError, UnicodeError):
                continue

    except Exception as e:
        logging.warning(f"Error detecting encoding for {file_path}: {e}")

    # Default fallback
    return 'utf-8', 0.0

async def read_file_with_encoding_detection(file_path):
    """
    Baca file dengan deteksi encoding otomatis.
    Returns: (content_lines, encoding_used)
    """
    encoding, confidence = detect_file_encoding(file_path)

    for attempt_encoding in [encoding] + [enc for enc in SUPPORTED_ENCODINGS if enc != encoding]:
        try:
            async with aiofiles.open(file_path, "r", encoding=attempt_encoding) as f:
                content = await f.read()
                lines = content.splitlines()
                # logging.info(f"File {file_path} berhasil dibaca dengan encoding: {attempt_encoding}")  # Dikurangi untuk mengurangi spam log
                return lines, attempt_encoding
        except (UnicodeDecodeError, UnicodeError) as e:
            logging.warning(f"Gagal baca {file_path} dengan encoding {attempt_encoding}: {e}")
            continue
        except Exception as e:
            logging.error(f"Error membaca file {file_path}: {e}")
            break

    # Fallback terakhir: baca sebagai binary dan decode dengan errors='ignore'
    try:
        logging.warning(f"Mencoba fallback binary read untuk {file_path}")
        async with aiofiles.open(file_path, "rb") as f:
            raw_content = await f.read()

        # Coba decode dengan utf-8 dan ignore error
        try:
            content = raw_content.decode('utf-8', errors='ignore')
            lines = content.splitlines()
            # logging.info(f"File {file_path} berhasil dibaca dengan fallback utf-8 (ignore errors)")  # Dikurangi untuk mengurangi spam log
            return lines, 'utf-8'
        except:
            # Fallback terakhir: latin-1 (bisa decode semua byte)
            content = raw_content.decode('latin-1', errors='ignore')
            lines = content.splitlines()
            # logging.info(f"File {file_path} berhasil dibaca dengan fallback latin-1")  # Dikurangi untuk mengurangi spam log
            return lines, 'latin-1'
    except Exception as e:
        logging.error(f"Fallback binary read gagal untuk {file_path}: {e}")

    # Jika semua gagal
    logging.error(f"Gagal membaca file {file_path} dengan semua metode")
    return [], 'utf-8'

def extract_numbers_from_vcf(file_path, max_retry=3, delay=2):
    """Extract valid phone numbers from vcf file dengan retry dan deteksi encoding."""
    for attempt in range(1, max_retry + 1):
        try:
            numbers = []
            encoding, confidence = detect_file_encoding(file_path)

            # Coba dengan encoding yang terdeteksi, lalu fallback ke encoding lain
            success = False
            for attempt_encoding in [encoding] + [enc for enc in SUPPORTED_ENCODINGS if enc != encoding]:
                try:
                    with open(file_path, "r", encoding=attempt_encoding) as f:
                        for line in f:
                            if line.startswith("TEL"):
                                nomor = line.split(":")[-1].strip()
                                numbers.append(nomor)
                    # logging.info(f"VCF file {file_path} berhasil dibaca dengan encoding: {attempt_encoding}")  # Dikurangi untuk mengurangi spam log
                    success = True
                    break
                except (UnicodeDecodeError, UnicodeError):
                    continue

            # Fallback jika semua encoding gagal
            if not success:
                try:
                    logging.warning(f"Mencoba fallback binary read untuk VCF {file_path}")
                    with open(file_path, "rb") as f:
                        raw_content = f.read()

                    # Decode dengan ignore errors
                    try:
                        content = raw_content.decode('utf-8', errors='ignore')
                    except:
                        content = raw_content.decode('latin-1', errors='ignore')

                    for line in content.splitlines():
                        if line.startswith("TEL"):
                            nomor = line.split(":")[-1].strip()
                            numbers.append(nomor)
                    # logging.info(f"VCF file {file_path} berhasil dibaca dengan fallback binary")  # Dikurangi untuk mengurangi spam log
                except Exception as e:
                    logging.error(f"Fallback binary read gagal untuk VCF {file_path}: {e}")

            # Terapkan validasi juga pada hasil vcf
            numbers = extract_valid_numbers_from_lines(numbers)

            # ✅ KONVERSI 08→62: Convert semua nomor 08xxx ke 62xxx
            numbers, conversion_count = batch_convert_08_to_62(numbers)
            if conversion_count > 0:
                logging.info(f"VCF: Converted {conversion_count} numbers from 08->62")

            return numbers
        except Exception as e:
            logging.error(f"Error reading vcf: {e} (percobaan {attempt})")
            if attempt == max_retry:
                return []
            time.sleep(delay)

async def extract_numbers_from_txt(file_path, timeout=10, max_retry=3, delay=2):
    """Extract valid phone numbers from txt file (one per line) asynchronously, dengan timeout, retry, dan deteksi encoding."""
    for attempt in range(1, max_retry + 1):
        try:
            # Gunakan fungsi deteksi encoding yang sudah dibuat
            lines, encoding_used = await read_file_with_encoding_detection(file_path)
            numbers = extract_valid_numbers_from_lines(lines)

            # ✅ KONVERSI 08→62: Convert semua nomor 08xxx ke 62xxx
            numbers, conversion_count = batch_convert_08_to_62(numbers)
            if conversion_count > 0:
                logging.info(f"TXT: Converted {conversion_count} numbers from 08->62")

            return numbers
        except asyncio.TimeoutError:
            logging.error(f"Timeout membaca file txt: {file_path} (percobaan {attempt})")
            if attempt == max_retry:
                return []
            await asyncio.sleep(delay)
        except Exception as e:
            logging.error(f"Error reading txt: {e} (percobaan {attempt})")
            if attempt == max_retry:
                return []
            await asyncio.sleep(delay)
    return []

def extract_numbers_from_csv(file_path, max_retry=3, delay=2):
    """Extract valid phone numbers from csv file (first column) dengan retry dan deteksi encoding."""
    for attempt in range(1, max_retry + 1):
        try:
            encoding, confidence = detect_file_encoding(file_path)

            # Coba dengan encoding yang terdeteksi, lalu fallback ke encoding lain
            df = None
            for attempt_encoding in [encoding] + [enc for enc in SUPPORTED_ENCODINGS if enc != encoding]:
                try:
                    df = pd.read_csv(file_path, encoding=attempt_encoding)
                    # logging.info(f"CSV file {file_path} berhasil dibaca dengan encoding: {attempt_encoding}")  # Dikurangi untuk mengurangi spam log
                    break
                except (UnicodeDecodeError, UnicodeError):
                    continue
                except Exception as e:
                    logging.warning(f"Error membaca CSV dengan {attempt_encoding}: {e}")
                    continue

            # Fallback jika semua encoding gagal
            if df is None:
                try:
                    logging.warning(f"Mencoba fallback binary read untuk CSV {file_path}")
                    with open(file_path, "rb") as f:
                        raw_content = f.read()

                    # Decode dengan ignore errors
                    try:
                        content = raw_content.decode('utf-8', errors='ignore')
                    except:
                        content = raw_content.decode('latin-1', errors='ignore')

                    # Simpan ke file temporary dan baca dengan pandas
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
                        temp_file.write(content)
                        temp_path = temp_file.name

                    df = pd.read_csv(temp_path, encoding='utf-8')
                    import os
                    os.unlink(temp_path)  # Hapus file temporary
                    # logging.info(f"CSV file {file_path} berhasil dibaca dengan fallback binary")  # Dikurangi untuk mengurangi spam log
                except Exception as e:
                    logging.error(f"Fallback binary read gagal untuk CSV {file_path}: {e}")
                    return []

            if df is None:
                logging.error(f"Gagal membaca CSV {file_path} dengan semua metode")
                return []

            col = df.columns[0]
            numbers = df[col].astype(str).str.strip().tolist()
            numbers = extract_valid_numbers_from_lines(numbers)

            # ✅ KONVERSI 08→62: Convert semua nomor 08xxx ke 62xxx
            numbers, conversion_count = batch_convert_08_to_62(numbers)
            if conversion_count > 0:
                logging.info(f"CSV: Converted {conversion_count} numbers from 08->62")

            return numbers
        except Exception as e:
            logging.error(f"Error reading csv: {e} (percobaan {attempt})")
            if attempt == max_retry:
                return []
            time.sleep(delay)

# ...
def extract_numbers_from_xlsx(file_path, max_retry=3, delay=2):
    """Extract valid phone numbers from xlsx file dengan retry. Excel files biasanya tidak memerlukan deteksi encoding manual."""
    for attempt in range(1, max_retry + 1):
        try:
            numbers = []
            xls = pd.ExcelFile(file_path)
            # logging.info(f"XLSX file {file_path} berhasil dibaca (Excel format)")  # Dikurangi untuk mengurangi spam log
            for sheet_name in xls.sheet_names:
                df = pd.read_excel(xls, sheet_name=sheet_name)
                for col in df.columns:
                    col_data = df[col].dropna().astype(str).str.strip().tolist()
                    col_data = [x for x in col_data if x and x.lower() != "nan"]
                    valid_numbers = extract_valid_numbers_from_lines(col_data)
                    numbers.extend(valid_numbers)
            numbers = list(dict.fromkeys(numbers))

            # ✅ KONVERSI 08→62: Convert semua nomor 08xxx ke 62xxx
            numbers, conversion_count = batch_convert_08_to_62(numbers)
            if conversion_count > 0:
                logging.info(f"XLSX: Converted {conversion_count} numbers from 08->62")

            return numbers
        except Exception as e:
            logging.error(f"Error reading xlsx: {e} (percobaan {attempt})")
            if attempt == max_retry:
                return []
            time.sleep(delay)

async def extract_numbers(file_path):
    """Detect file type and extract numbers."""
    if file_path.endswith(".txt"):
        return await extract_numbers_from_txt(file_path)
    elif file_path.endswith(".csv"):
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, extract_numbers_from_csv, file_path)
    elif file_path.endswith(".xlsx") or file_path.endswith(".xls"):
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, extract_numbers_from_xlsx, file_path)
    elif file_path.endswith(".vcf"):
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, extract_numbers_from_vcf, file_path)
    else:
        logging.error("Unsupported file type")
        return []