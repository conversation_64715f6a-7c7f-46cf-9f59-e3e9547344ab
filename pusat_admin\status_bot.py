"""
Status Bot Module - Menampilkan status dan statistik bot
"""

import logging
import asyncio
import time
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .keyboards import create_status_bot_keyboard
from .utils import log_bot, format_time_ago
from .database import db
from .auth_admin import is_admin, log_admin_activity
from .status_user_aktif import get_active_users
from .kontrol_bot import get_maintenance_status

router = Router()



def get_bot_statistics():
    """Ambil statistik bot"""
    try:
        # Total users yang pernah mengakses bot (SEMUA user, tidak peduli status)
        total_users = db.get_line_count("total_users.txt")

        # Total admins (termasuk super admin dari config)
        from .auth_admin import get_admin_list
        total_admins = len(get_admin_list())

        # Total blocked users dari total_users.txt
        from .blokir_user import get_blocked_users_list
        blocked_users = len(get_blocked_users_list())
        
        # File statistics
        user_files = 0
        try:
            import os
            user_data_dir = "management/user_data_file"
            if os.path.exists(user_data_dir):
                user_files = len([f for f in os.listdir(user_data_dir) if f.endswith('.csv')])
        except:
            pass
        
        return {
            'total_users': total_users,
            'total_admins': total_admins,
            'blocked_users': blocked_users,
            'user_files': user_files
        }
    except Exception as e:
        logging.error(f"Error getting bot statistics: {e}")
        return {
            'total_users': 0,
            'total_admins': 0,
            'blocked_users': 0,
            'user_files': 0
        }


@router.callback_query(F.data == "admin_stats")
async def show_admin_stats(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan status bot"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin ke active users
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")

    # Tampilkan loading dulu dengan error handling
    try:
        await callback.message.edit_text("📊 **STATUS BOT**\n\nSedang memuat data...", parse_mode="Markdown")
    except Exception as e:
        # Jika error "message is not modified", abaikan
        if "message is not modified" not in str(e).lower():
            logging.warning(f"Error editing loading message: {e}")
        pass

    try:
        # Ambil statistik dasar
        stats = get_bot_statistics()
        
        # Hitung user aktif - SAMA DENGAN USER AKTIF MENU
        active_users_list = get_active_users()
        active_users = len(active_users_list)

        # Cek status maintenance
        is_maintenance = get_maintenance_status()
        bot_status = "🔴 Maintenance" if is_maintenance else "✅ Online"

        # Format status text - SIMPLE
        status_text = f"""📊 **STATUS BOT**

👥 {stats['total_users']} user | 🟢 {active_users} user
👨‍💼 {stats['total_admins']} admin | 🚫 {stats['blocked_users']} diblokir
🤖 Status: {bot_status}"""

        try:
            await callback.message.edit_text(
                status_text,
                reply_markup=create_status_bot_keyboard(),
                parse_mode="Markdown"
            )
        except Exception as edit_error:
            # Jika error "message is not modified", abaikan
            if "message is not modified" in str(edit_error).lower():
                await callback.answer("✅ Data sudah terbaru", show_alert=False)
            else:
                raise edit_error
        
    except Exception as e:
        logging.error(f"Error showing admin stats: {e}")
        await callback.message.edit_text(
            "❌ **Error loading status**",
            reply_markup=create_status_bot_keyboard(),
            parse_mode="Markdown"
        )

    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer()
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")


@router.callback_query(F.data == "admin_stats_refresh")
async def refresh_admin_stats(callback: types.CallbackQuery, state: FSMContext):
    """Refresh status bot"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin ke active users
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")

    # Tampilkan loading indicator dengan error handling
    try:
        await callback.message.edit_text(
            "📊 **STATUS BOT**\n\n🔄 Mengecek database...",
            parse_mode="Markdown"
        )
    except Exception as e:
        # Jika error "message is not modified", abaikan
        if "message is not modified" not in str(e).lower():
            logging.warning(f"Error editing refresh loading message: {e}")
        pass

    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer("🔄 Refresh...")
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")

    # Clear cache jika ada
    try:
        # Implementasi clear cache di sini jika diperlukan
        pass
    except:
        pass

    # Redirect ke show_admin_stats
    await show_admin_stats(callback, state)
