"""
Text Cleaner Utility untuk Management System
Membersihkan semua jenis formatting Telegram untuk deteksi yang akurat
"""

import re
import unicodedata
from typing import List, Optional, Dict
from aiogram.types import Message, MessageEntity


def _get_mathematical_alphanumeric_mapping() -> Dict[str, str]:
    """
    Generate comprehensive mapping for Mathematical Alphanumeric Symbols (U+1D400-U+1D7FF)
    Covers ALL mathematical styling: bold, italic, script, fraktur, monospace, etc.

    Returns:
        Dict[str, str]: Mapping dari Mathematical Unicode ke ASCII
    """
    mapping = {}

    # Mathematical Bold (U+1D400-U+1D433)
    # 𝐀𝐁𝐂𝐃𝐄𝐅𝐆𝐇𝐈𝐉𝐊𝐋𝐌𝐍𝐎𝐏𝐐𝐑𝐒𝐓𝐔𝐕𝐖𝐗𝐘𝐙
    bold_upper = "𝐀𝐁𝐂𝐃𝐄𝐅𝐆𝐇𝐈𝐉𝐊𝐋𝐌𝐍𝐎𝐏𝐐𝐑𝐒𝐓𝐔𝐕𝐖𝐗𝐘𝐙"
    # 𝐚𝐛𝐜𝐝𝐞𝐟𝐠𝐡𝐢𝐣𝐤𝐥𝐦𝐧𝐨𝐩𝐪𝐫𝐬𝐭𝐮𝐯𝐰𝐱𝐲𝐳
    bold_lower = "𝐚𝐛𝐜𝐝𝐞𝐟𝐠𝐡𝐢𝐣𝐤𝐥𝐦𝐧𝐨𝐩𝐪𝐫𝐬𝐭𝐮𝐯𝐰𝐱𝐲𝐳"

    # Mathematical Italic (U+1D434-U+1D467)
    # 𝐴𝐵𝐶𝐷𝐸𝐹𝐺𝐻𝐼𝐽𝐾𝐿𝑀𝑁𝑂𝑃𝑄𝑅𝑆𝑇𝑈𝑉𝑊𝑋𝑌𝑍
    italic_upper = "𝐴𝐵𝐶𝐷𝐸𝐹𝐺𝐻𝐼𝐽𝐾𝐿𝑀𝑁𝑂𝑃𝑄𝑅𝑆𝑇𝑈𝑉𝑊𝑋𝑌𝑍"
    # 𝑎𝑏𝑐𝑑𝑒𝑓𝑔ℎ𝑖𝑗𝑘𝑙𝑚𝑛𝑜𝑝𝑞𝑟𝑠𝑡𝑢𝑣𝑤𝑥𝑦𝑧
    italic_lower = "𝑎𝑏𝑐𝑑𝑒𝑓𝑔ℎ𝑖𝑗𝑘𝑙𝑚𝑛𝑜𝑝𝑞𝑟𝑠𝑡𝑢𝑣𝑤𝑥𝑦𝑧"

    # Mathematical Bold Italic (U+1D468-U+1D49B)
    # 𝑨𝑩𝑪𝑫𝑬𝑭𝑮𝑯𝑰𝑱𝑲𝑳𝑴𝑵𝑶𝑷𝑸𝑹𝑺𝑻𝑼𝑽𝑾𝑿𝒀𝒁
    bold_italic_upper = "𝑨𝑩𝑪𝑫𝑬𝑭𝑮𝑯𝑰𝑱𝑲𝑳𝑴𝑵𝑶𝑷𝑸𝑹𝑺𝑻𝑼𝑽𝑾𝑿𝒀𝒁"
    # 𝒂𝒃𝒄𝒅𝒆𝒇𝒈𝒉𝒊𝒋𝒌𝒍𝒎𝒏𝒐𝒑𝒒𝒓𝒔𝒕𝒖𝒗𝒘𝒙𝒚𝒛
    bold_italic_lower = "𝒂𝒃𝒄𝒅𝒆𝒇𝒈𝒉𝒊𝒋𝒌𝒍𝒎𝒏𝒐𝒑𝒒𝒓𝒔𝒕𝒖𝒗𝒘𝒙𝒚𝒛"

    # Mathematical Script (U+1D49C-U+1D4CF)
    # 𝒜ℬ𝒞𝒟ℰℱ𝒢ℋℐ𝒥𝒦ℒℳ𝒩𝒪𝒫𝒬ℛ𝒮𝒯𝒰𝒱𝒲𝒳𝒴𝒵
    script_upper = "𝒜ℬ𝒞𝒟ℰℱ𝒢ℋℐ𝒥𝒦ℒℳ𝒩𝒪𝒫𝒬ℛ𝒮𝒯𝒰𝒱𝒲𝒳𝒴𝒵"
    # 𝒶𝒷𝒸𝒹ℯ𝒻ℊ𝒽𝒾𝒿𝓀𝓁𝓂𝓃ℴ𝓅𝓆𝓇𝓈𝓉𝓊𝓋𝓌𝓍𝓎𝓏
    script_lower = "𝒶𝒷𝒸𝒹ℯ𝒻ℊ𝒽𝒾𝒿𝓀𝓁𝓂𝓃ℴ𝓅𝓆𝓇𝓈𝓉𝓊𝓋𝓌𝓍𝓎𝓏"

    # Mathematical Bold Script (U+1D4D0-U+1D503)
    # 𝓐𝓑𝓒𝓓𝓔𝓕𝓖𝓗𝓘𝓙𝓚𝓛𝓜𝓝𝓞𝓟𝓠𝓡𝓢𝓣𝓤𝓥𝓦𝓧𝓨𝓩
    bold_script_upper = "𝓐𝓑𝓒𝓓𝓔𝓕𝓖𝓗𝓘𝓙𝓚𝓛𝓜𝓝𝓞𝓟𝓠𝓡𝓢𝓣𝓤𝓥𝓦𝓧𝓨𝓩"
    # 𝓪𝓫𝓬𝓭𝓮𝓯𝓰𝓱𝓲𝓳𝓴𝓵𝓶𝓷𝓸𝓹𝓺𝓻𝓼𝓽𝓾𝓿𝔀𝔁𝔂𝔃
    bold_script_lower = "𝓪𝓫𝓬𝓭𝓮𝓯𝓰𝓱𝓲𝓳𝓴𝓵𝓶𝓷𝓸𝓹𝓺𝓻𝓼𝓽𝓾𝓿𝔀𝔁𝔂𝔃"

    # Mathematical Fraktur (U+1D504-U+1D537)
    # 𝔄𝔅ℭ𝔇𝔈𝔉𝔊ℌℑ𝔍𝔎𝔏𝔐𝔑𝔒𝔓𝔔ℜ𝔖𝔗𝔘𝔙𝔚𝔛𝔜ℨ
    fraktur_upper = "𝔄𝔅ℭ𝔇𝔈𝔉𝔊ℌℑ𝔍𝔎𝔏𝔐𝔑𝔒𝔓𝔔ℜ𝔖𝔗𝔘𝔙𝔚𝔛𝔜ℨ"
    # 𝔞𝔟𝔠𝔡𝔢𝔣𝔤𝔥𝔦𝔧𝔨𝔩𝔪𝔫𝔬𝔭𝔮𝔯𝔰𝔱𝔲𝔳𝔴𝔵𝔶𝔷
    fraktur_lower = "𝔞𝔟𝔠𝔡𝔢𝔣𝔤𝔥𝔦𝔧𝔨𝔩𝔪𝔫𝔬𝔭𝔮𝔯𝔰𝔱𝔲𝔳𝔴𝔵𝔶𝔷"

    # Mathematical Double-Struck (U+1D538-U+1D56B)
    # 𝔸𝔹ℂ𝔻𝔼𝔽𝔾ℍ𝕀𝕁𝕂𝕃𝕄ℕ𝕆ℙℚℝ𝕊𝕋𝕌𝕍𝕎𝕏𝕐ℤ
    double_struck_upper = "𝔸𝔹ℂ𝔻𝔼𝔽𝔾ℍ𝕀𝕁𝕂𝕃𝕄ℕ𝕆ℙℚℝ𝕊𝕋𝕌𝕍𝕎𝕏𝕐ℤ"
    # 𝕒𝕓𝕔𝕕𝕖𝕗𝕘𝕙𝕚𝕛𝕜𝕝𝕞𝕟𝕠𝕡𝕢𝕣𝕤𝕥𝕦𝕧𝕨𝕩𝕪𝕫
    double_struck_lower = "𝕒𝕓𝕔𝕕𝕖𝕗𝕘𝕙𝕚𝕛𝕜𝕝𝕞𝕟𝕠𝕡𝕢𝕣𝕤𝕥𝕦𝕧𝕨𝕩𝕪𝕫"

    # Mathematical Bold Fraktur (U+1D56C-U+1D59F)
    # 𝖆𝖇𝖈𝖉𝖊𝖋𝖌𝖍𝖎𝖏𝖐𝖑𝖒𝖓𝖔𝖕𝖖𝖗𝖘𝖙𝖚𝖛𝖜𝖝𝖞𝖟
    bold_fraktur_upper = "𝕬𝕭𝕮𝕯𝕰𝕱𝕲𝕳𝕴𝕵𝕶𝕷𝕸𝕹𝕺𝕻𝕼𝕽𝕾𝕿𝖀𝖁𝖂𝖃𝖄𝖅"
    # 𝖆𝖇𝖈𝖉𝖊𝖋𝖌𝖍𝖎𝖏𝖐𝖑𝖒𝖓𝖔𝖕𝖖𝖗𝖘𝖙𝖚𝖛𝖜𝖝𝖞𝖟
    bold_fraktur_lower = "𝖆𝖇𝖈𝖉𝖊𝖋𝖌𝖍𝖎𝖏𝖐𝖑𝖒𝖓𝖔𝖕𝖖𝖗𝖘𝖙𝖚𝖛𝖜𝖝𝖞𝖟"

    # Mathematical Sans-Serif (U+1D5A0-U+1D5D3)
    # 𝖠𝖡𝖢𝖣𝖤𝖥𝖦𝖧𝖨𝖩𝖪𝖫𝖬𝖭𝖮𝖯𝖰𝖱𝖲𝖳𝖴𝖵𝖶𝖷𝖸𝖹
    sans_serif_upper = "𝖠𝖡𝖢𝖣𝖤𝖥𝖦𝖧𝖨𝖩𝖪𝖫𝖬𝖭𝖮𝖯𝖰𝖱𝖲𝖳𝖴𝖵𝖶𝖷𝖸𝖹"
    # 𝖺𝖻𝖼𝖽𝖾𝖿𝗀𝗁𝗂𝗃𝗄𝗅𝗆𝗇𝗈𝗉𝗊𝗋𝗌𝗍𝗎𝗏𝗐𝗑𝗒𝗓
    sans_serif_lower = "𝖺𝖻𝖼𝖽𝖾𝖿𝗀𝗁𝗂𝗃𝗄𝗅𝗆𝗇𝗈𝗉𝗊𝗋𝗌𝗍𝗎𝗏𝗐𝗑𝗒𝗓"

    # Mathematical Sans-Serif Bold (U+1D5D4-U+1D607)
    # 𝗔𝗕𝗖𝗗𝗘𝗙𝗚𝗛𝗜𝗝𝗞𝗟𝗠𝗡𝗢𝗣𝗤𝗥𝗦𝗧𝗨𝗩𝗪𝗫𝗬𝗭
    sans_serif_bold_upper = "𝗔𝗕𝗖𝗗𝗘𝗙𝗚𝗛𝗜𝗝𝗞𝗟𝗠𝗡𝗢𝗣𝗤𝗥𝗦𝗧𝗨𝗩𝗪𝗫𝗬𝗭"
    # 𝗮𝗯𝗰𝗱𝗲𝗳𝗴𝗵𝗶𝗷𝗸𝗹𝗺𝗻𝗼𝗽𝗾𝗿𝘀𝘁𝘂𝘃𝘄𝘅𝘆𝘇
    sans_serif_bold_lower = "𝗮𝗯𝗰𝗱𝗲𝗳𝗴𝗵𝗶𝗷𝗸𝗹𝗺𝗻𝗼𝗽𝗾𝗿𝘀𝘁𝘂𝘃𝘄𝘅𝘆𝘇"

    # Mathematical Sans-Serif Italic (U+1D608-U+1D63B)
    # 𝘈𝘉𝘊𝘋𝘌𝘍𝘎𝘏𝘐𝘑𝘒𝘓𝘔𝘕𝘖𝘗𝘘𝘙𝘚𝘛𝘜𝘝𝘞𝘟𝘠𝘡
    sans_serif_italic_upper = "𝘈𝘉𝘊𝘋𝘌𝘍𝘎𝘏𝘐𝘑𝘒𝘓𝘔𝘕𝘖𝘗𝘘𝘙𝘚𝘛𝘜𝘝𝘞𝘟𝘠𝘡"
    # 𝘢𝘣𝘤𝘥𝘦𝘧𝘨𝘩𝘪𝘫𝘬𝘭𝘮𝘯𝘰𝘱𝘲𝘳𝘴𝘵𝘶𝘷𝘸𝘹𝘺𝘻
    sans_serif_italic_lower = "𝘢𝘣𝘤𝘥𝘦𝘧𝘨𝘩𝘪𝘫𝘬𝘭𝘮𝘯𝘰𝘱𝘲𝘳𝘴𝘵𝘶𝘷𝘸𝘹𝘺𝘻"

    # Mathematical Sans-Serif Bold Italic (U+1D63C-U+1D66F) - YANG DIGUNAKAN DI CONTOH
    # 𝘼𝘽𝘾𝘿𝙀𝙁𝙂𝙃𝙄𝙅𝙆𝙇𝙈𝙉𝙊𝙋𝙌𝙍𝙎𝙏𝙐𝙑𝙒𝙓𝙔𝙕
    sans_serif_bold_italic_upper = "𝘼𝘽𝘾𝘿𝙀𝙁𝙂𝙃𝙄𝙅𝙆𝙇𝙈𝙉𝙊𝙋𝙌𝙍𝙎𝙏𝙐𝙑𝙒𝙓𝙔𝙕"
    # 𝙖𝙗𝙘𝙙𝙚𝙛𝙜𝙝𝙞𝙟𝙠𝙡𝙢𝙣𝙤𝙥𝙦𝙧𝙨𝙩𝙪𝙫𝙬𝙭𝙮𝙯
    sans_serif_bold_italic_lower = "𝙖𝙗𝙘𝙙𝙚𝙛𝙜𝙝𝙞𝙟𝙠𝙡𝙢𝙣𝙤𝙥𝙦𝙧𝙨𝙩𝙪𝙫𝙬𝙭𝙮𝙯"

    # Mathematical Monospace (U+1D670-U+1D6A3)
    # 𝙰𝙱𝙲𝙳𝙴𝙵𝙶𝙷𝙸𝙹𝙺𝙻𝙼𝙽𝙾𝙿𝚀𝚁𝚂𝚃𝚄𝚅𝚆𝚇𝚈𝚉
    monospace_upper = "𝙰𝙱𝙲𝙳𝙴𝙵𝙶𝙷𝙸𝙹𝙺𝙻𝙼𝙽𝙾𝙿𝚀𝚁𝚂𝚃𝚄𝚅𝚆𝚇𝚈𝚉"
    # 𝚊𝚋𝚌𝚍𝚎𝚏𝚐𝚑𝚒𝚓𝚔𝚕𝚖𝚗𝚘𝚙𝚚𝚛𝚜𝚝𝚞𝚟𝚠𝚡𝚢𝚣
    monospace_lower = "𝚊𝚋𝚌𝚍𝚎𝚏𝚐𝚑𝚒𝚓𝚔𝚕𝚖𝚗𝚘𝚙𝚚𝚛𝚜𝚝𝚞𝚟𝚠𝚡𝚢𝚣"

    # ASCII reference
    ascii_upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    ascii_lower = "abcdefghijklmnopqrstuvwxyz"

    # Build mapping for all variants
    variants = [
        (bold_upper, ascii_upper), (bold_lower, ascii_lower),
        (italic_upper, ascii_upper), (italic_lower, ascii_lower),
        (bold_italic_upper, ascii_upper), (bold_italic_lower, ascii_lower),
        (script_upper, ascii_upper), (script_lower, ascii_lower),
        (bold_script_upper, ascii_upper), (bold_script_lower, ascii_lower),
        (fraktur_upper, ascii_upper), (fraktur_lower, ascii_lower),
        (double_struck_upper, ascii_upper), (double_struck_lower, ascii_lower),
        (bold_fraktur_upper, ascii_upper), (bold_fraktur_lower, ascii_lower),
        (sans_serif_upper, ascii_upper), (sans_serif_lower, ascii_lower),
        (sans_serif_bold_upper, ascii_upper), (sans_serif_bold_lower, ascii_lower),
        (sans_serif_italic_upper, ascii_upper), (sans_serif_italic_lower, ascii_lower),
        (sans_serif_bold_italic_upper, ascii_upper), (sans_serif_bold_italic_lower, ascii_lower),
        (monospace_upper, ascii_upper), (monospace_lower, ascii_lower),
    ]

    for math_chars, ascii_chars in variants:
        for math_char, ascii_char in zip(math_chars, ascii_chars):
            mapping[math_char] = ascii_char

    return mapping


def clean_all_telegram_formatting(text: str) -> str:
    """
    Hapus SEMUA jenis formatting Telegram
    
    Args:
        text: Text yang akan dibersihkan
        
    Returns:
        str: Text yang sudah dibersihkan dari formatting
    """
    if not text:
        return ""
    
    # 1. MARKDOWN PATTERNS
    markdown_patterns = [
        # Kutip (Quote/Blockquote)
        r'^>\s*(.*?)$',           # > quote
        r'^>>>\s*(.*?)$',         # >>> quote
        
        # Spoiler
        r'\|\|(.*?)\|\|',         # ||spoiler||
        
        # Tebal (Bold)
        r'\*\*(.*?)\*\*',         # **bold**
        r'__(.*?)__',             # __bold__
        
        # Miring (Italic)
        r'(?<!\*)\*([^*]+?)\*(?!\*)',  # *italic* (not part of **)
        r'(?<!_)_([^_]+?)_(?!_)',      # _italic_ (not part of __)
        
        # Mono (Code)
        r'`([^`]+?)`',            # `code`
        r'```(.*?)```',           # ```code block```
        
        # Coret (Strikethrough)
        r'~~(.*?)~~',             # ~~strikethrough~~
        
        # Buat Tautan (Text Link) - extract text part for CV detection
        r'\[([^\]]+?)\]\([^)]+?\)',  # [text](url) - capture text
    ]
    
    # 2. HTML PATTERNS
    html_patterns = [
        # Kutip (Quote/Blockquote)
        r'<blockquote[^>]*>(.*?)</blockquote>',
        
        # Spoiler
        r'<tg-spoiler[^>]*>(.*?)</tg-spoiler>',
        r'<span[^>]*class=["\']tg-spoiler["\'][^>]*>(.*?)</span>',
        
        # Tebal (Bold)
        r'<b[^>]*>(.*?)</b>',
        r'<strong[^>]*>(.*?)</strong>',
        
        # Miring (Italic)
        r'<i[^>]*>(.*?)</i>',
        r'<em[^>]*>(.*?)</em>',
        
        # Mono (Code)
        r'<code[^>]*>(.*?)</code>',
        r'<pre[^>]*>(.*?)</pre>',
        
        # Coret (Strikethrough)
        r'<s[^>]*>(.*?)</s>',
        r'<del[^>]*>(.*?)</del>',
        r'<strike[^>]*>(.*?)</strike>',
        
        # Garis Bawah (Underline)
        r'<u[^>]*>(.*?)</u>',
        
        # Buat Tautan (Text Link)
        r'<a[^>]*href=["\'][^"\']*["\'][^>]*>(.*?)</a>',
    ]
    
    cleaned = text
    
    # Apply markdown patterns
    for pattern in markdown_patterns:
        cleaned = re.sub(pattern, r'\1', cleaned, flags=re.DOTALL | re.MULTILINE)
    
    # Apply HTML patterns
    for pattern in html_patterns:
        cleaned = re.sub(pattern, r'\1', cleaned, flags=re.DOTALL | re.IGNORECASE)
    
    # 3. CLEANUP REMAINING FORMATTING CHARACTERS
    formatting_chars = ['*', '_', '`', '~', '|', '[', ']', '(', ')', '>', '<']
    for char in formatting_chars:
        cleaned = cleaned.replace(char, '')
    
    # 4. NORMALIZE WHITESPACE
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    return cleaned.strip()


def clean_text_with_telegram_entities(text: str, entities: List[MessageEntity] = None) -> str:
    """
    Clean text berdasarkan Telegram entities (lebih akurat)
    
    Args:
        text: Text yang akan dibersihkan
        entities: List of MessageEntity dari Telegram
        
    Returns:
        str: Text yang sudah dibersihkan
    """
    if not text:
        return ""
    
    if not entities:
        return clean_all_telegram_formatting(text)
    
    # Sort entities by offset (reverse untuk tidak mengacaukan index)
    sorted_entities = sorted(entities, key=lambda x: x.offset, reverse=True)
    
    cleaned_text = text
    
    for entity in sorted_entities:
        start = entity.offset
        end = entity.offset + entity.length
        
        # Extract clean text dari entity
        entity_text = text[start:end]
        
        # Handle berdasarkan entity type
        if entity.type in ['bold', 'italic', 'code', 'pre', 'strikethrough', 
                          'underline', 'spoiler', 'blockquote']:
            # Untuk formatting entities, ambil text-nya saja
            replacement = entity_text
            
        elif entity.type in ['text_link', 'url']:
            # Untuk link entities, ambil text-nya saja (bukan URL)
            replacement = entity_text
            
        else:
            # Entity lain (mention, hashtag, dll), biarkan apa adanya
            replacement = entity_text
        
        # Replace di cleaned_text
        cleaned_text = cleaned_text[:start] + replacement + cleaned_text[end:]
    
    # Apply additional cleaning untuk sisa formatting yang tidak ter-handle
    cleaned_text = clean_all_telegram_formatting(cleaned_text)
    
    return cleaned_text


def normalize_unicode_text(text: str) -> str:
    """
    Normalize unicode untuk deteksi homograph attacks

    Args:
        text: Text yang akan dinormalisasi

    Returns:
        str: Text yang sudah dinormalisasi
    """
    if not text:
        return ""

    # Remove zero-width characters
    zero_width_chars = ['\u200B', '\u200C', '\u200D', '\u2060', '\uFEFF']
    for char in zero_width_chars:
        text = text.replace(char, '')

    # Remove RTL/LTR marks
    directional_marks = ['\u202A', '\u202B', '\u202C', '\u202D', '\u202E']
    for mark in directional_marks:
        text = text.replace(mark, '')

    # Normalize unicode (NFD -> NFC)
    text = unicodedata.normalize('NFC', text)

    # Convert lookalike characters to ASCII
    text = convert_lookalikes_to_ascii(text)

    return text


def convert_lookalikes_to_ascii(text: str) -> str:
    """
    Convert common lookalike characters to ASCII including Mathematical Alphanumeric Symbols

    Args:
        text: Text yang akan dikonversi

    Returns:
        str: Text dengan lookalike characters yang sudah dikonversi
    """
    lookalikes = {
        # Cyrillic to Latin
        'а': 'a', 'е': 'e', 'о': 'o', 'р': 'p', 'с': 'c',
        'х': 'x', 'у': 'y', 'А': 'A', 'В': 'B', 'С': 'C',
        'Е': 'E', 'Н': 'H', 'К': 'K', 'М': 'M', 'О': 'O',
        'Р': 'P', 'Т': 'T', 'Х': 'X', 'У': 'Y',
        # Greek to Latin
        'α': 'a', 'ο': 'o', 'ρ': 'p', 'υ': 'u', 'χ': 'x',
        'Α': 'A', 'Β': 'B', 'Ε': 'E', 'Ζ': 'Z', 'Η': 'H',
        'Ι': 'I', 'Κ': 'K', 'Μ': 'M', 'Ν': 'N', 'Ο': 'O',
        'Ρ': 'P', 'Τ': 'T', 'Υ': 'Y', 'Χ': 'X',
    }

    # Add Mathematical Alphanumeric Symbols mapping
    mathematical_mapping = _get_mathematical_alphanumeric_mapping()
    lookalikes.update(mathematical_mapping)

    for lookalike, ascii_char in lookalikes.items():
        text = text.replace(lookalike, ascii_char)

    return text


def decode_punycode_domains(text: str) -> str:
    """
    Decode punycode domains untuk deteksi

    Args:
        text: Text yang mengandung punycode domains

    Returns:
        str: Text dengan punycode domains yang sudah di-decode
    """
    # Find punycode domains (xn--)
    punycode_pattern = r'xn--[a-zA-Z0-9-]+\.[a-zA-Z]{2,}'

    def decode_match(match):
        try:
            domain = match.group(0)
            decoded = domain.encode('ascii').decode('idna')
            return decoded
        except:
            return match.group(0)

    return re.sub(punycode_pattern, decode_match, text)


def extract_clean_text_from_message(message: Message) -> str:
    """
    Extract dan clean semua text dari message

    Args:
        message: Message object dari Telegram

    Returns:
        str: Text yang sudah dibersihkan dari semua formatting
    """
    all_clean_text = []

    # 1. Text utama dengan entities
    if message.text:
        clean_text = clean_text_with_telegram_entities(
            message.text,
            message.entities
        )
        if clean_text:
            all_clean_text.append(clean_text)

    # 2. Caption dengan caption_entities
    if message.caption:
        clean_caption = clean_text_with_telegram_entities(
            message.caption,
            message.caption_entities
        )
        if clean_caption:
            all_clean_text.append(clean_caption)

    # 3. Forward message
    if message.forward_from_message_id and message.text:
        clean_forward = clean_text_with_telegram_entities(
            message.text,
            message.entities
        )
        if clean_forward:
            all_clean_text.append(clean_forward)

    # 4. Reply message
    if message.reply_to_message:
        if message.reply_to_message.text:
            clean_reply = clean_text_with_telegram_entities(
                message.reply_to_message.text,
                message.reply_to_message.entities
            )
            if clean_reply:
                all_clean_text.append(clean_reply)

        if message.reply_to_message.caption:
            clean_reply_caption = clean_text_with_telegram_entities(
                message.reply_to_message.caption,
                message.reply_to_message.caption_entities
            )
            if clean_reply_caption:
                all_clean_text.append(clean_reply_caption)

    # 5. Poll text (jika ada)
    if message.poll:
        if message.poll.question:
            all_clean_text.append(clean_all_telegram_formatting(message.poll.question))
        for option in message.poll.options:
            if option.text:
                all_clean_text.append(clean_all_telegram_formatting(option.text))

    # Gabungkan semua text dan normalize
    combined_text = " ".join(all_clean_text)

    # Apply unicode normalization
    final_text = normalize_unicode_text(combined_text)

    return final_text.strip()


def ultimate_text_cleaner(text: str, entities: List[MessageEntity] = None) -> str:
    """
    Ultimate text cleaning pipeline

    Args:
        text: Text yang akan dibersihkan
        entities: List of MessageEntity dari Telegram

    Returns:
        str: Text yang sudah dibersihkan secara comprehensive
    """
    if not text:
        return ""

    # Step 1: Handle entities
    if entities:
        text = clean_text_with_telegram_entities(text, entities)
    else:
        # Step 2: Clean markdown/HTML formatting
        text = clean_all_telegram_formatting(text)

    # Step 3: Normalize unicode
    text = normalize_unicode_text(text)

    # Step 4: Decode punycode
    text = decode_punycode_domains(text)

    # Step 5: Final cleanup
    text = re.sub(r'\s+', ' ', text)  # Normalize whitespace

    return text.strip()


def extract_links_from_formatted_text(text: str) -> str:
    """
    Extract links dari formatted text untuk link detection
    Berbeda dari text cleaning - ini preserve URL dari markdown links

    Args:
        text: Text yang mengandung formatted links

    Returns:
        str: Text dengan URL yang di-extract dari markdown links
    """
    if not text:
        return ""

    # Extract URLs dari markdown links [text](url)
    markdown_link_pattern = r'\[([^\]]+?)\]\(([^)]+?)\)'

    def replace_markdown_link(match):
        text_part = match.group(1)
        url_part = match.group(2)
        # Return both text and URL untuk link detection
        return f"{text_part} {url_part}"

    # Replace markdown links dengan text + URL
    result = re.sub(markdown_link_pattern, replace_markdown_link, text)

    # Clean other formatting tapi preserve URLs
    other_patterns = [
        # Kutip (Quote/Blockquote)
        r'^>\s*(.*?)$',           # > quote
        r'^>>>\s*(.*?)$',         # >>> quote

        # Spoiler
        r'\|\|(.*?)\|\|',         # ||spoiler||

        # Tebal (Bold)
        r'\*\*(.*?)\*\*',         # **bold**
        r'__(.*?)__',             # __bold__

        # Miring (Italic)
        r'(?<!\*)\*([^*]+?)\*(?!\*)',  # *italic* (not part of **)
        r'(?<!_)_([^_]+?)_(?!_)',      # _italic_ (not part of __)

        # Mono (Code)
        r'`([^`]+?)`',            # `code`
        r'```(.*?)```',           # ```code block```

        # Coret (Strikethrough)
        r'~~(.*?)~~',             # ~~strikethrough~~
    ]

    # Apply other patterns
    for pattern in other_patterns:
        result = re.sub(pattern, r'\1', result, flags=re.DOTALL | re.MULTILINE)

    # Clean HTML tags tapi preserve URLs
    html_patterns = [
        r'<blockquote[^>]*>(.*?)</blockquote>',
        r'<tg-spoiler[^>]*>(.*?)</tg-spoiler>',
        r'<span[^>]*class=["\']tg-spoiler["\'][^>]*>(.*?)</span>',
        r'<b[^>]*>(.*?)</b>',
        r'<strong[^>]*>(.*?)</strong>',
        r'<i[^>]*>(.*?)</i>',
        r'<em[^>]*>(.*?)</em>',
        r'<code[^>]*>(.*?)</code>',
        r'<pre[^>]*>(.*?)</pre>',
        r'<s[^>]*>(.*?)</s>',
        r'<del[^>]*>(.*?)</del>',
        r'<strike[^>]*>(.*?)</strike>',
        r'<u[^>]*>(.*?)</u>',
    ]

    for pattern in html_patterns:
        result = re.sub(pattern, r'\1', result, flags=re.DOTALL | re.IGNORECASE)

    # Handle HTML links - extract href
    html_link_pattern = r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>'
    def replace_html_link(match):
        url_part = match.group(1)
        text_part = match.group(2)
        return f"{text_part} {url_part}"

    result = re.sub(html_link_pattern, replace_html_link, result, flags=re.IGNORECASE)

    # Normalize whitespace
    result = re.sub(r'\s+', ' ', result)

    return result.strip()
