import asyncio
import logging
import os
import time
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import FSInputFile
from aiogram.filters import Command
from aiogram.exceptions import TelegramRetryAfter
from utils import file_naming, contact_naming, file as file_utils, format as format_utils
from utils.retry_send import retry_send_document, retry_send_documents_group, send_files_with_sending_mode  # Tambahan import retry
from utils.cancel_keyboard import set_cancel_keyboard
from utils.user_settings import get_to_vcf_naming_mode, get_to_vcf_send_mode  # Import user settings
from utils.user_isolation import with_user_isolation, is_user_busy  # Import user isolation
from utils.user_directories import get_user_data_dir, get_user_file_path, cleanup_user_directory  # Import user directories
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs  # Import smart logging
from management.membership import check_membership, send_membership_message, delete_join_message
import asyncio
from management.data_file import log_file_upload

router = Router()

class ToVcfStates(StatesGroup):
    waiting_files = State()
    waiting_contactname = State()
    waiting_filename = State()
    waiting_split_choice = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

# Handler global untuk perintah lain agar bisa membatalkan proses ini

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_vcf(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

# Handler utama
@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_start(message: types.Message, state: FSMContext):

    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    bot_msg = "📥 Kirim file .txt atau .xlsx"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ToVcfStates.waiting_files)
    await state.update_data(files=[], logs=[])

@router.message(ToVcfStates.waiting_files, F.document, F.chat.type == "private")
async def to_vcf_receive_file(message: types.Message, state: FSMContext, bot):
    log_user(message)
    # === Tambahkan logging upload file ===
    await log_file_upload(message)
    # =====================================
    file = message.document
    allowed_ext = [".txt", ".xlsx", ".xls"]
    _, ext = os.path.splitext(file.file_name.lower())

    data = await state.get_data()
    # Jika sudah pernah error, abaikan SEMUA upload berikutnya (tidak kirim pesan apapun, tidak proses file apapun)
    if data.get("file_error"):
        return

    # Jika ada file salah format, set flag error, kirim error, dan JANGAN proses apapun lagi
    if ext not in allowed_ext:
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ Format file tidak didukung!\nKetik /to_vcf untuk mulai ulang."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    # File valid, proses seperti biasa (hanya jika belum pernah error)
    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        # logs.append((message.message_id, f"bot: File {file.file_name} diterima"))  # Dikurangi untuk mengurangi spam log
        await state.update_data(files=files, logs=logs)
        # Pesan /done hanya muncul jika ini file valid pertama DAN tidak ada file_error di state
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done untuk lanjut."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file error: {e}")
        await message.answer(err_msg)

@router.message(ToVcfStates.waiting_files, Command("done"), F.chat.type == "private")
async def to_vcf_done(message: types.Message, state: FSMContext):
    # Cek jika user mengetik perintah utama lain di tengah proses
    if message.text.strip().startswith("/to_txt"):
        await state.clear()
        from handlers.to_txt import to_txt_start
        await to_txt_start(message, state)
        return
    if message.text.strip().startswith("/to_vcf"):
        await state.clear()
        await to_vcf_start(message, state)
        return
    if message.text.strip().startswith("/start"):
        await state.clear()
        from handlers.start import start_handler
        await start_handler(message, state)
        return
    if message.text.strip().startswith("/help"):
        await state.clear()
        from handlers.start import help_handler
        await help_handler(message, state)
        return
    if message.text.strip().startswith("/admin"):
        await state.clear()
        from handlers.admin import admin_start
        await admin_start(message, state)
        return
    if message.text.strip().startswith("/manual"):
        await state.clear()
        from handlers.manual import manual_start
        await manual_start(message, state)
        return
    if message.text.strip().startswith("/add"):
        await state.clear()
        from handlers.add import add_start
        await add_start(message, state)
        return
    if message.text.strip().startswith("/delete"):
        await state.clear()
        from handlers.delete import delete_start
        await delete_start(message, state)
        return
    if message.text.strip().startswith("/renamectc"):
        await state.clear()
        from handlers.renamectc import renamectc_start
        await renamectc_start(message, state)
        return
    if message.text.strip().startswith("/renamefile"):
        await state.clear()
        from handlers.renamefile import renamefile_start
        await renamefile_start(message, state)
        return
    if message.text.strip().startswith("/merge"):
        await state.clear()
        from handlers.merge import merge_start
        await merge_start(message, state)
        return
    if message.text.strip().startswith("/split"):
        await state.clear()
        from handlers.split import split_start
        await split_start(message, state)
        return
    if message.text.strip().startswith("/count"):
        await state.clear()
        from handlers.count import count_start
        await count_start(message, state)
        return
    if message.text.strip().startswith("/nodup"):
        await state.clear()
        from handlers.nodup import nodup_start
        await nodup_start(message, state)
        return
    if message.text.strip().startswith("/getname"):
        await state.clear()
        from handlers.getname import getname_start
        await getname_start(message, state)
        return
    if message.text.strip().startswith("/generate"):
        await state.clear()
        from handlers.generate import generate_start
        await generate_start(message, state)
        return
    if message.text.strip().startswith("/setting"):
        await state.clear()
        from handlers.hide_menu import hide_menu_start
        await hide_menu_start(message, state)
        return

    log_user(message)
    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    # Urutkan files dan logs berdasarkan message_id agar urutan sesuai upload user
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)
    # Summary log sudah ada di atas, tidak perlu log individual
    # for _, log_msg in logs:
    #     logging.info(log_msg)
    bot_msg = "📝 Masukkan nama kontak:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ToVcfStates.waiting_contactname)

@router.message(ToVcfStates.waiting_contactname, F.chat.type == "private")
async def to_vcf_contactname(message: types.Message, state: FSMContext):
    # Cek jika user mengetik perintah utama lain di tengah proses
    if message.text.strip().startswith("/to_txt"):
        await state.clear()
        from handlers.to_txt import to_txt_start
        await to_txt_start(message, state)
        return
    if message.text.strip().startswith("/to_vcf"):
        await state.clear()
        await to_vcf_start(message, state)
        return
    if message.text.strip().startswith("/start"):
        await state.clear()
        from handlers.start import start_handler
        await start_handler(message, state)
        return
    if message.text.strip().startswith("/help"):
        await state.clear()
        from handlers.start import help_handler
        await help_handler(message, state)
        return
    if message.text.strip().startswith("/admin"):
        await state.clear()
        from handlers.admin import admin_start
        await admin_start(message, state)
        return
    if message.text.strip().startswith("/manual"):
        await state.clear()
        from handlers.manual import manual_start
        await manual_start(message, state)
        return
    if message.text.strip().startswith("/add"):
        await state.clear()
        from handlers.add import add_start
        await add_start(message, state)
        return
    if message.text.strip().startswith("/delete"):
        await state.clear()
        from handlers.delete import delete_start
        await delete_start(message, state)
        return
    if message.text.strip().startswith("/renamectc"):
        await state.clear()
        from handlers.renamectc import renamectc_start
        await renamectc_start(message, state)
        return
    if message.text.strip().startswith("/renamefile"):
        await state.clear()
        from handlers.renamefile import renamefile_start
        await renamefile_start(message, state)
        return
    if message.text.strip().startswith("/merge"):
        await state.clear()
        from handlers.merge import merge_start
        await merge_start(message, state)
        return
    if message.text.strip().startswith("/split"):
        await state.clear()
        from handlers.split import split_start
        await split_start(message, state)
        return
    if message.text.strip().startswith("/count"):
        await state.clear()
        from handlers.count import count_start
        await count_start(message, state)
        return
    if message.text.strip().startswith("/nodup"):
        await state.clear()
        from handlers.nodup import nodup_start
        await nodup_start(message, state)
        return
    if message.text.strip().startswith("/getname"):
        await state.clear()
        from handlers.getname import getname_start
        await getname_start(message, state)
        return
    if message.text.strip().startswith("/generate"):
        await state.clear()
        from handlers.generate import generate_start
        await generate_start(message, state)
        return
    if message.text.strip().startswith("/setting"):
        await state.clear()
        from handlers.hide_menu import hide_menu_start
        await hide_menu_start(message, state)
        return

    log_user(message)
    await state.update_data(contactname=message.text.strip())
    bot_msg = "💾 Masukkan nama file:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ToVcfStates.waiting_filename)

@router.message(ToVcfStates.waiting_filename, F.chat.type == "private")
async def to_vcf_filename(message: types.Message, state: FSMContext):
    # Cek jika user mengetik perintah utama lain di tengah proses
    if message.text.strip().startswith("/to_txt"):
        await state.clear()
        from handlers.to_txt import to_txt_start
        await to_txt_start(message, state)
        return
    if message.text.strip().startswith("/to_vcf"):
        await state.clear()
        await to_vcf_start(message, state)
        return
    if message.text.strip().startswith("/start"):
        await state.clear()
        from handlers.start import start_handler
        await start_handler(message, state)
        return
    if message.text.strip().startswith("/help"):
        await state.clear()
        from handlers.start import help_handler
        await help_handler(message, state)
        return
    if message.text.strip().startswith("/admin"):
        await state.clear()
        from handlers.admin import admin_start
        await admin_start(message, state)
        return
    if message.text.strip().startswith("/manual"):
        await state.clear()
        from handlers.manual import manual_start
        await manual_start(message, state)
        return
    if message.text.strip().startswith("/add"):
        await state.clear()
        from handlers.add import add_start
        await add_start(message, state)
        return
    if message.text.strip().startswith("/delete"):
        await state.clear()
        from handlers.delete import delete_start
        await delete_start(message, state)
        return
    if message.text.strip().startswith("/renamectc"):
        await state.clear()
        from handlers.renamectc import renamectc_start
        await renamectc_start(message, state)
        return
    if message.text.strip().startswith("/renamefile"):
        await state.clear()
        from handlers.renamefile import renamefile_start
        await renamefile_start(message, state)
        return
    if message.text.strip().startswith("/merge"):
        await state.clear()
        from handlers.merge import merge_start
        await merge_start(message, state)
        return
    if message.text.strip().startswith("/split"):
        await state.clear()
        from handlers.split import split_start
        await split_start(message, state)
        return
    if message.text.strip().startswith("/count"):
        await state.clear()
        from handlers.count import count_start
        await count_start(message, state)
        return
    if message.text.strip().startswith("/nodup"):
        await state.clear()
        from handlers.nodup import nodup_start
        await nodup_start(message, state)
        return
    if message.text.strip().startswith("/getname"):
        await state.clear()
        from handlers.getname import getname_start
        await getname_start(message, state)
        return
    if message.text.strip().startswith("/generate"):
        await state.clear()
        from handlers.generate import generate_start
        await generate_start(message, state)
        return
    if message.text.strip().startswith("/setting"):
        await state.clear()
        from handlers.hide_menu import hide_menu_start
        await hide_menu_start(message, state)
        return

    log_user(message)
    await state.update_data(filename=message.text.strip())
    bot_msg = "🔢 Jumlah kontak per file atau ketik 'all':"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ToVcfStates.waiting_split_choice)

@router.message(ToVcfStates.waiting_split_choice, F.chat.type == "private")
async def to_vcf_split_choice(message: types.Message, state: FSMContext):
    # Cek jika user mengetik perintah utama lain di tengah proses
    if message.text.strip().startswith("/to_txt"):
        await state.clear()
        from handlers.to_txt import to_txt_start
        await to_txt_start(message, state)
        return
    if message.text.strip().startswith("/to_vcf"):
        await state.clear()
        await to_vcf_start(message, state)
        return
    if message.text.strip().startswith("/start"):
        await state.clear()
        from handlers.start import start_handler
        await start_handler(message, state)
        return
    if message.text.strip().startswith("/help"):
        await state.clear()
        from handlers.start import help_handler
        await help_handler(message, state)
        return
    if message.text.strip().startswith("/admin"):
        await state.clear()
        from handlers.admin import admin_start
        await admin_start(message, state)
        return
    if message.text.strip().startswith("/manual"):
        await state.clear()
        from handlers.manual import manual_start
        await manual_start(message, state)
        return
    if message.text.strip().startswith("/add"):
        await state.clear()
        from handlers.add import add_start
        await add_start(message, state)
        return
    if message.text.strip().startswith("/delete"):
        await state.clear()
        from handlers.delete import delete_start
        await delete_start(message, state)
        return
    if message.text.strip().startswith("/renamectc"):
        await state.clear()
        from handlers.renamectc import renamectc_start
        await renamectc_start(message, state)
        return
    if message.text.strip().startswith("/renamefile"):
        await state.clear()
        from handlers.renamefile import renamefile_start
        await renamefile_start(message, state)
        return
    if message.text.strip().startswith("/merge"):
        await state.clear()
        from handlers.merge import merge_start
        await merge_start(message, state)
        return
    if message.text.strip().startswith("/split"):
        await state.clear()
        from handlers.split import split_start
        await split_start(message, state)
        return
    if message.text.strip().startswith("/count"):
        await state.clear()
        from handlers.count import count_start
        await count_start(message, state)
        return
    if message.text.strip().startswith("/nodup"):
        await state.clear()
        from handlers.nodup import nodup_start
        await nodup_start(message, state)
        return
    if message.text.strip().startswith("/getname"):
        await state.clear()
        from handlers.getname import getname_start
        await getname_start(message, state)
        return
    if message.text.strip().startswith("/generate"):
        await state.clear()
        from handlers.generate import generate_start
        await generate_start(message, state)
        return
    if message.text.strip().startswith("/setting"):
        await state.clear()
        from handlers.hide_menu import hide_menu_start
        await hide_menu_start(message, state)
        return

    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    text = message.text.strip().lower()
    if text == "all":
        await state.update_data(split="all")
        log_bot("Proses all kontak dalam satu file.")
        await process_vcf_with_isolation(message, state)
    elif text.isdigit() and int(text) > 0:
        await state.update_data(split=int(text))
        log_bot(f"Pecah kontak per {text} kontak.")
        await process_vcf_with_isolation(message, state)
    else:
        bot_msg = "⚠️ Input salah. Ketik 'all' atau jumlah kontak per file."
        await message.answer(bot_msg)
        log_bot(bot_msg)

async def process_vcf_with_isolation(message: types.Message, state: FSMContext):
    """
    Wrapper function untuk process_vcf dengan user isolation.
    Mencegah user melakukan multiple concurrent processing.
    """
    user_id = message.from_user.id

    # Start process tracking untuk Grace Period
    try:
        from payment.grace_period_helper import start_user_process
        start_user_process(user_id, "convert_vcf")
    except Exception:
        pass  # Silent fail jika grace period belum init

    # Jalankan process_vcf dengan user isolation
    success, result = await with_user_isolation(user_id, process_vcf, message, state)

    if not success:
        # User sedang busy - complete process tracking
        try:
            from payment.grace_period_helper import complete_user_process
            await complete_user_process(user_id)
        except Exception:
            pass  # Silent fail jika grace period belum init

        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)
        return

    # Processing berhasil, sekarang handle pengiriman
    if result and len(result) == 3:
        files_to_send, file_paths_to_delete, send_mode = result

        if files_to_send:
            # Set cancel keyboard sebelum mulai kirim
            await set_cancel_keyboard(message)

            # Kirim file dengan sending mode (di luar semaphore)
            # Keyboard akan di-restore otomatis di dalam send_files_with_sending_mode
            success, sent_count, total_count, cancelled = await send_files_with_sending_mode(message, files_to_send, user_id, send_mode)

        # Cleanup files setelah pengiriman dengan safe deletion
        if file_paths_to_delete:
            from utils.safe_file_cleanup import delayed_file_cleanup
            success_count, failed_count, failed_files = await delayed_file_cleanup(file_paths_to_delete, delay_before_cleanup=1.0)

            # Schedule background cleanup untuk file yang gagal
            if failed_count > 0:
                from utils.safe_file_cleanup import schedule_background_cleanup
                failed_paths = [path for path in file_paths_to_delete if os.path.basename(path) in failed_files]
                schedule_background_cleanup(failed_paths, delay_minutes=2)

    # Clear state dan complete process tracking
    await state.clear()
    try:
        from payment.grace_period_helper import complete_user_process
        await complete_user_process(user_id)
    except Exception:
        pass  # Silent fail jika grace period belum init

async def process_vcf(message: types.Message, state: FSMContext):
    data = await state.get_data()
    files = data.get("files", [])
    filename = data.get("filename", "kontak")
    contactname = data.get("contactname", "Kontak")
    split = data.get("split", "all")

    # Ambil mode pengiriman user
    send_mode = get_to_vcf_send_mode(message.from_user.id)

    file_paths_to_delete = []
    files_to_send = []  # List untuk menyimpan (file_path, filename) yang akan dikirim

    try:
        file_paths = [f[0] for f in files]
        original_names = [f[1] for f in files]
        if split == "all":
            file_names = file_naming.generate_file_names(filename, len(file_paths), split_mode="all")
            for idx, (file_path, original_filename, _) in enumerate(files):
                # logging.info(f"user: proses file {os.path.basename(file_path)}")  # Dikurangi untuk mengurangi spam log
                numbers = await file_utils.extract_numbers(file_path)
                numbers = list(dict.fromkeys(numbers))
                if not numbers:
                    bot_msg = f"⚠️ Tidak ada nomor di {original_filename}."
                    await message.answer(bot_msg)
                    log_bot(bot_msg)
                    continue
                contact_names = contact_naming.generate_contact_names(contactname, len(numbers), file_idx=idx, total_files=len(files))
                output_name = file_names[idx]
                if not output_name.lower().endswith('.vcf'):
                    output_name += ".vcf"
                vcf_content = format_utils.create_vcf_content(contact_names, numbers)
                output_path = get_user_file_path(message.from_user.id, output_name)
                await format_utils.write_vcf_file(output_path, vcf_content)

                # Tambahkan ke list untuk dikirim nanti
                files_to_send.append((output_path, output_name))
                file_paths_to_delete.append(output_path)
        else:
            split = int(split)
            part_counts = []
            numbers_list = []
            for file_path, original_filename, _ in files:
                numbers = await file_utils.extract_numbers(file_path)
                numbers = list(dict.fromkeys(numbers))
                numbers_list.append(numbers)
                total = len(numbers)
                part_total = (total + split - 1) // split
                part_counts.append(part_total)
            # Ambil setting penamaan user (hanya untuk multiple files + split)
            naming_mode = "beruntun"  # default
            if len(files) > 1:
                naming_mode = get_to_vcf_naming_mode(message.from_user.id)

            file_names = file_naming.generate_file_names(filename, len(files), part_counts=part_counts, split_mode=split, naming_mode=naming_mode)
            idx_name = 0
            for file_idx, numbers in enumerate(numbers_list):
                if not numbers:
                    bot_msg = f"⚠️ Tidak ada nomor di {original_names[file_idx]}."
                    await message.answer(bot_msg)
                    log_bot(bot_msg)
                    continue
                part_total = part_counts[file_idx]
                if len(files) == 1:
                    nomor_awal = 1
                    for part_idx in range(part_total):
                        part_numbers = numbers[part_idx*split:(part_idx+1)*split]
                        contact_names = [f"{contactname} {i+nomor_awal:02d}" for i in range(len(part_numbers))]
                        output_name = file_names[idx_name]
                        idx_name += 1
                        if not output_name.lower().endswith('.vcf'):
                            output_name += ".vcf"
                        vcf_content = format_utils.create_vcf_content(contact_names, part_numbers)
                        output_path = get_user_file_path(message.from_user.id, output_name)
                        await format_utils.write_vcf_file(output_path, vcf_content)

                        # Tambahkan ke list untuk dikirim nanti
                        files_to_send.append((output_path, output_name))
                        file_paths_to_delete.append(output_path)
                        nomor_awal += len(part_numbers)
                else:
                    label = contact_naming._alphabet_label(file_idx)
                    nomor_awal = 1  # Mulai dari 1 untuk setiap file
                    for part_idx in range(part_total):
                        part_numbers = numbers[part_idx*split:(part_idx+1)*split]
                        contact_names = [f"{contactname} {label} {i+nomor_awal:02d}" for i in range(len(part_numbers))]
                        output_name = file_names[idx_name]
                        idx_name += 1
                        if not output_name.lower().endswith('.vcf'):
                            output_name += ".vcf"
                        vcf_content = format_utils.create_vcf_content(contact_names, part_numbers)
                        output_path = get_user_file_path(message.from_user.id, output_name)
                        await format_utils.write_vcf_file(output_path, vcf_content)

                        # Tambahkan ke list untuk dikirim nanti
                        files_to_send.append((output_path, output_name))
                        file_paths_to_delete.append(output_path)
                        nomor_awal += len(part_numbers)  # Update nomor awal untuk part berikutnya

        # Return files untuk dikirim di luar semaphore
        return files_to_send, file_paths_to_delete, send_mode
    except Exception as e:
        err_msg = "❌ Gagal proses file. Ketik /to_vcf untuk ulang."
        logging.error(f"user: error proses vcf: {e}")
        log_bot(err_msg)
        try:
            await message.answer(err_msg)
        except TelegramRetryAfter:
            # Skip error message jika kena rate limit
            pass
        return None, [], "individual"  # Return empty untuk error case