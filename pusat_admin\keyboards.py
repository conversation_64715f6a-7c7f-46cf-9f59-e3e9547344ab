"""
Keyboard layouts untuk semua fitur admin
"""

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


def create_main_menu_keyboard():
    """Keyboard menu utama admin"""
    keyboard = [
        [
            InlineKeyboardButton(text="📊 Status", callback_data="admin_stats"),
            InlineKeyboardButton(text="👥 User", callback_data="admin_users")
        ],
        [
            InlineKeyboardButton(text="💰 Payment", callback_data="payment_stats"),
            InlineKeyboardButton(text="💬 Pesan", callback_data="user_send_message")
        ],
        [
            InlineKeyboardButton(text="👨‍💼 Admin", callback_data="admin_manage"),
            InlineKeyboardButton(text="🔧 Mainten", callback_data="admin_maintenance")
        ],
        [
            InlineKeyboardButton(text="📁 File", callback_data="admin_files"),
            InlineKeyboardButton(text="📦 Backup", callback_data="admin_backup")
        ],
        [InlineKeyboardButton(text="❌ Tutup", callback_data="admin_close")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_back_keyboard(callback_data="admin_back"):
    """Keyboard kembali sederhana"""
    keyboard = [
        [InlineKeyboardButton(text="🔙 Kembali", callback_data=callback_data)]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_user_menu_keyboard():
    """Keyboard menu kelola user"""
    keyboard = [
        [
            InlineKeyboardButton(text="✅ UnBlok", callback_data="user_unblock"),
            InlineKeyboardButton(text="🚫 Blokir", callback_data="user_block")
        ],
        [InlineKeyboardButton(text="🔍 Cari", callback_data="user_search")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_send_message_menu_keyboard():
    """Keyboard menu kirim pesan"""
    keyboard = [
        [InlineKeyboardButton(text="📨 User Tertentu", callback_data="user_message")],
        [InlineKeyboardButton(text="📢 Semua User", callback_data="user_broadcast")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_admin_manage_keyboard():
    """Keyboard kelola admin"""
    keyboard = [
        [
            InlineKeyboardButton(text="➕ Tambah", callback_data="admin_add"),
            InlineKeyboardButton(text="➖ Hapus", callback_data="admin_remove")
        ],
        [InlineKeyboardButton(text="📋 Daftar", callback_data="admin_list")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)





def create_maintenance_menu_keyboard():
    """Keyboard menu maintenance"""
    keyboard = [
        [
            InlineKeyboardButton(text="🔴 On", callback_data="maintenance_on"),
            InlineKeyboardButton(text="🟢 Off", callback_data="maintenance_off")
        ],
        [InlineKeyboardButton(text="✏️ Edit", callback_data="maintenance_edit")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)





def create_pagination_keyboard(current_page, total_pages, prefix="page"):
    """Keyboard pagination"""
    keyboard = []

    # Navigation buttons
    nav_buttons = []
    if current_page > 1:
        nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"{prefix}_{current_page-1}"))

    nav_buttons.append(InlineKeyboardButton(text=f"{current_page}/{total_pages}", callback_data="noop"))

    if current_page < total_pages:
        nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"{prefix}_{current_page+1}"))

    keyboard.append(nav_buttons)

    # Back button - kembali ke status bot
    keyboard.append([InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")])

    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_refresh_keyboard(callback_data):
    """Keyboard dengan tombol refresh"""
    keyboard = [
        [InlineKeyboardButton(text="🔄 Refresh", callback_data=f"{callback_data}_refresh")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_status_bot_keyboard():
    """Keyboard untuk status bot dengan daftar user dan user aktif"""
    keyboard = [
        [
            InlineKeyboardButton(text="👥 List", callback_data="total_users"),
            InlineKeyboardButton(text="🟢 Aktif", callback_data="user_aktif")
        ],
        [InlineKeyboardButton(text="🔄 Refresh", callback_data="admin_stats_refresh")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_info_file_keyboard():
    """Keyboard untuk info file dengan download dan hapus file"""
    keyboard = [
        [
            InlineKeyboardButton(text="📥 Download", callback_data="admin_download_all"),
            InlineKeyboardButton(text="🗑️ Hapus", callback_data="admin_delete_files_menu")
        ],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_delete_files_keyboard():
    """Keyboard untuk menu hapus file"""
    keyboard = [
        [
            InlineKeyboardButton(text="🗑️ .vcf", callback_data="admin_delete_vcf"),
            InlineKeyboardButton(text="🗑️ .txt", callback_data="admin_delete_txt")
        ],
        [
            InlineKeyboardButton(text="🗑️ .xlsx", callback_data="admin_delete_xlsx"),
            InlineKeyboardButton(text="🗑️ Semua", callback_data="admin_delete_all")
        ],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_files")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_delete_confirm_keyboard(action):
    """Keyboard untuk konfirmasi hapus file"""
    keyboard = [
        [
            InlineKeyboardButton(text="✅ Ya", callback_data=f"admin_confirm_delete_{action}"),
            InlineKeyboardButton(text="❌ Batal", callback_data="admin_delete_files_menu")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_payment_stats_keyboard():
    """Keyboard untuk statistik pembayaran"""
    keyboard = [
        [
            InlineKeyboardButton(text="🎁 Gratis", callback_data="free_premium"),
            InlineKeyboardButton(text="📋 ID Pay", callback_data="payment_history")
        ],
        [
            InlineKeyboardButton(text="📊 Trial", callback_data="trial_users"),
            InlineKeyboardButton(text="🔄 Reset", callback_data="reset_trial_users")
        ],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_back")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_payment_history_keyboard(current_page: int = 1, total_pages: int = 1, has_prev: bool = False, has_next: bool = False):
    """Keyboard untuk riwayat pembayaran dengan pagination"""
    keyboard = []

    # Navigation row (only if more than 1 page)
    if total_pages > 1:
        nav_row = []

        if has_prev:
            nav_row.append(InlineKeyboardButton(
                text="⬅️",
                callback_data=f"payment_history_page:{current_page - 1}"
            ))

        # Page info
        nav_row.append(InlineKeyboardButton(
            text=f"{current_page}/{total_pages}",
            callback_data="payment_history_stats"
        ))

        if has_next:
            nav_row.append(InlineKeyboardButton(
                text="➡️",
                callback_data=f"payment_history_page:{current_page + 1}"
            ))

        keyboard.append(nav_row)

    # Action buttons
    keyboard.append([
        InlineKeyboardButton(text="🔄 Refresh", callback_data="payment_history_refresh")
    ])

    # Back button
    keyboard.append([InlineKeyboardButton(text="🔙 Kembali", callback_data="payment_stats")])

    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_free_premium_keyboard():
    """Keyboard untuk gratis premium"""
    keyboard = [
        [InlineKeyboardButton(text="⏰ Durasi", callback_data="free_premium_duration")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_duration_keyboard():
    """Keyboard untuk pilih durasi premium gratis"""
    keyboard = [
        [
            InlineKeyboardButton(text="1 Hari", callback_data="duration_1d"),
            InlineKeyboardButton(text="3 Hari", callback_data="duration_3d")
        ],
        [
            InlineKeyboardButton(text="7 Hari", callback_data="duration_7d"),
            InlineKeyboardButton(text="30 Hari", callback_data="duration_30d")
        ],
        [InlineKeyboardButton(text="❌ Batal", callback_data="payment_stats")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_confirm_free_premium_keyboard():
    """Keyboard untuk konfirmasi gratis premium"""
    keyboard = [
        [
            InlineKeyboardButton(text="✅ Berikan", callback_data="confirm_free_premium"),
            InlineKeyboardButton(text="❌ Batal", callback_data="payment_stats")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_trial_users_keyboard(current_page: int = 1, total_pages: int = 1, has_prev: bool = False, has_next: bool = False):
    """Keyboard untuk trial users dengan pagination"""
    keyboard = []

    # Navigation row (only if more than 1 page)
    if total_pages > 1:
        nav_row = []

        if has_prev:
            nav_row.append(InlineKeyboardButton(
                text="◀️",
                callback_data=f"trial_users_page:{current_page - 1}"
            ))

        # Page info
        nav_row.append(InlineKeyboardButton(
            text=f"{current_page}/{total_pages}",
            callback_data="trial_users_stats"
        ))

        if has_next:
            nav_row.append(InlineKeyboardButton(
                text="▶️",
                callback_data=f"trial_users_page:{current_page + 1}"
            ))

        keyboard.append(nav_row)

    # Action buttons
    keyboard.append([
        InlineKeyboardButton(text="🔄 Refresh", callback_data="trial_users_refresh")
    ])

    # Back button
    keyboard.append([InlineKeyboardButton(text="🔙 Kembali", callback_data="payment_stats")])

    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def create_reset_trial_confirm_keyboard():
    """Keyboard untuk konfirmasi reset trial users"""
    keyboard = [
        [
            InlineKeyboardButton(text="✅ Ya", callback_data="confirm_reset_trial"),
            InlineKeyboardButton(text="❌ Batal", callback_data="payment_stats")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)



