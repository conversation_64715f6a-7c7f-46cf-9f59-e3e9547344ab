import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs
from management.membership import delete_join_message
from utils.user_settings import get_to_vcf_naming_mode, set_to_vcf_naming_mode, get_to_vcf_send_mode, set_to_vcf_send_mode, get_to_txt_send_mode, set_to_txt_send_mode, get_to_txt_format, set_to_txt_format

router = Router()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

def create_settings_menu_keyboard():
    """Keyboard menu pengaturan utama"""
    keyboard = [
        [InlineKeyboardButton(text="📝 Nama File VCF", callback_data="settings_nametovcf")],
        [InlineKeyboardButton(text="📤 Mode Kirim VCF", callback_data="settings_sendtovcf")],
        [InlineKeyboardButton(text="📤 Mode Kirim TXT", callback_data="settings_sendtotxt")],
        [InlineKeyboardButton(text="📊 Mode to TXT", callback_data="settings_totxt")],
        [InlineKeyboardButton(text="❌ Tutup Menu", callback_data="settings_close")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_hide_menu(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def hide_menu_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Membership check sudah ditangani oleh Premium Middleware
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    bot_msg = (
        "⚙️ **MENU PENGATURAN**\n\n"
        "Pilih pengaturan:"
    )

    # Kirim menu pengaturan
    bot_message = await message.answer(bot_msg, reply_markup=create_settings_menu_keyboard(), parse_mode="Markdown")

    # Hapus pesan command user setelah delay singkat
    try:
        await message.delete()
        log_bot("Tampilkan menu pengaturan bot - pesan user dihapus")
    except Exception as e:
        log_bot(f"Tampilkan menu pengaturan bot - gagal hapus pesan user: {e}")

# Callback handlers untuk tombol pengaturan
@router.callback_query(F.data == "settings_nametovcf")
async def settings_nametovcf_callback(callback: types.CallbackQuery):
    """Handler untuk tombol Atur Nama File VCF"""
    user_id = callback.from_user.id

    # Ambil setting saat ini
    current_mode = get_to_vcf_naming_mode(user_id)
    current_mode_text = "Beruntun" if current_mode == "beruntun" else "Per File"

    # Buat inline keyboard (sejajar dalam 1 baris) - disable tombol yang aktif
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="➡️ Beruntun" + (" ✅" if current_mode == "beruntun" else ""),
                callback_data="naming_beruntun" if current_mode != "beruntun" else "noop"
            ),
            InlineKeyboardButton(
                text="📂 Per File" + (" ✅" if current_mode == "per_file" else ""),
                callback_data="naming_per_file" if current_mode != "per_file" else "noop"
            )
        ],
        [InlineKeyboardButton(text="❌ Tutup", callback_data="settings_close")]
    ])

    bot_msg = (
        f"⚙️ Setting /to_vcf: {current_mode_text}\n\n"
        "➡️ Beruntun: hutao 79, hutao 80\n"
        "📂 Per File: hutao78_1, hutao78_2"
    )

    await callback.message.edit_text(bot_msg, reply_markup=keyboard)
    await callback.answer()
    log_bot("User akses setting nama file VCF")

@router.callback_query(F.data == "settings_sendtovcf")
async def settings_sendtovcf_callback(callback: types.CallbackQuery):
    """Handler untuk tombol Atur Mode Kirim VCF"""
    user_id = callback.from_user.id

    # Ambil setting saat ini
    current_mode = get_to_vcf_send_mode(user_id)
    current_mode_text = "Individual" if current_mode == "individual" else "(10 files)"

    # Buat inline keyboard (sejajar dalam 1 baris) - disable tombol yang aktif
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="📤 Individual" + (" ✅" if current_mode == "individual" else ""),
                callback_data="send_individual" if current_mode != "individual" else "noop"
            ),
            InlineKeyboardButton(
                text="📦 Group" + (" ✅" if current_mode == "group" else ""),
                callback_data="send_group" if current_mode != "group" else "noop"
            )
        ],
        [InlineKeyboardButton(text="❌ Tutup", callback_data="settings_close")]
    ])

    bot_msg = (
        f"⚙️ Mode Kirim /to_vcf: {current_mode_text}\n\n"
        "📤 Individual: Kirim satu per satu\n"
        "📦 Group: Kirim 10 file sekaligus"
    )

    await callback.message.edit_text(bot_msg, reply_markup=keyboard)
    await callback.answer()
    log_bot("User akses setting mode kirim VCF")

@router.callback_query(F.data == "settings_totxt")
async def settings_totxt_callback(callback: types.CallbackQuery):
    """Handler untuk setting format to TXT (hanya format, mode kirim ngikut setting /to_txt)"""
    user_id = callback.from_user.id
    current_format = get_to_txt_format(user_id)

    format_text = "📄 TXT" if current_format == "txt" else "📊 XLSX"

    # Buat inline keyboard hanya untuk format
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="📄 TXT" + (" ✅" if current_format == "txt" else ""),
                callback_data="txt_format_txt" if current_format != "txt" else "noop"
            ),
            InlineKeyboardButton(
                text="📊 XLSX" + (" ✅" if current_format == "xlsx" else ""),
                callback_data="txt_format_xlsx" if current_format != "xlsx" else "noop"
            )
        ],
        [InlineKeyboardButton(text="❌ Tutup", callback_data="settings_close")]
    ])

    bot_msg = (
        f"⚙️ Mode to TXT\n\n"
        f"📊 Format: {format_text}\n\n"
        "📄 TXT: Convert ke file teks\n"
        "📊 XLSX: Convert ke file Excel\n\n"
        "💡 Mode kirim mengikuti setting /to_txt"
    )

    await callback.message.edit_text(bot_msg, reply_markup=keyboard)
    await callback.answer()
    log_bot("User akses setting format to TXT")

@router.callback_query(F.data == "settings_sendtotxt")
async def settings_sendtotxt_callback(callback: types.CallbackQuery):
    """Handler untuk setting mode kirim TXT"""
    user_id = callback.from_user.id
    current_mode = get_to_txt_send_mode(user_id)
    current_mode_text = "Individual" if current_mode == "individual" else "(10 files)"

    # Buat inline keyboard (sejajar dalam 1 baris) - disable tombol yang aktif
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="📤 Individual" + (" ✅" if current_mode == "individual" else ""),
                callback_data="txt_send_individual" if current_mode != "individual" else "noop"
            ),
            InlineKeyboardButton(
                text="📦 Group" + (" ✅" if current_mode == "group" else ""),
                callback_data="txt_send_group" if current_mode != "group" else "noop"
            )
        ],
        [InlineKeyboardButton(text="❌ Tutup", callback_data="settings_close")]
    ])

    bot_msg = (
        f"⚙️ Mode Kirim /to_txt: {current_mode_text}\n\n"
        "📤 Individual: Kirim satu per satu\n"
        "📦 Group: Kirim 10 file sekaligus"
    )

    await callback.message.edit_text(bot_msg, reply_markup=keyboard)
    await callback.answer()
    log_bot("User akses setting mode kirim TXT")

@router.callback_query(F.data == "settings_back")
async def settings_back_callback(callback: types.CallbackQuery):
    """Handler untuk tombol Kembali ke menu utama"""
    bot_msg = (
        "⚙️ **MENU PENGATURAN**\n\n"
        "Pilih pengaturan yang ingin diubah:"
    )

    await callback.message.edit_text(bot_msg, reply_markup=create_settings_menu_keyboard(), parse_mode="Markdown")
    await callback.answer()
    log_bot("User kembali ke menu pengaturan utama")

@router.callback_query(F.data == "settings_close")
async def settings_close_callback(callback: types.CallbackQuery):
    """Handler untuk tombol Tutup Menu"""
    try:
        # Hapus pesan menu pengaturan
        await callback.message.delete()
        await callback.answer("✅ Menu pengaturan ditutup")
        log_bot("User tutup menu pengaturan - pesan dihapus")
    except Exception as e:
        # Fallback jika gagal hapus pesan
        await callback.message.edit_text(
            "✅ **Menu pengaturan ditutup**\n\n/setting untuk buka lagi",
            parse_mode="Markdown"
        )
        await callback.answer()
        log_bot(f"User tutup menu pengaturan - fallback edit: {e}")

@router.callback_query(F.data == "noop")
async def noop_callback(callback: types.CallbackQuery):
    """Handler untuk tombol yang disabled (tidak ada aksi)"""
    try:
        await callback.answer()
    except Exception as e:
        # Ignore expired callback queries
        logging.warning(f"Callback answer failed (probably expired): {e}")

# Handler untuk pilihan-pilihan di sub menu
@router.callback_query(F.data.startswith("naming_"))
async def handle_naming_choice(callback: types.CallbackQuery):
    """Handler untuk pilihan mode penamaan VCF"""
    user_id = callback.from_user.id
    # Ambil bagian setelah "naming_"
    choice = callback.data[7:]  # Hapus "naming_" dari awal

    # Validasi pilihan
    if choice not in ["beruntun", "per_file"]:
        try:
            await callback.answer("❌ Pilihan tidak valid", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        return

    # Cek apakah setting sudah sama - tetap hapus pesan
    current_setting = get_to_vcf_naming_mode(user_id)
    if current_setting == choice:
        try:
            await callback.message.delete()
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - pesan dihapus")
        except Exception as e:
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - gagal hapus: {e}")
        return

    # Simpan setting
    success = set_to_vcf_naming_mode(user_id, choice)

    if success:
        mode_text = "Beruntun" if choice == "beruntun" else "Per File"

        try:
            # Hapus pesan menu pengaturan setelah setting berubah
            await callback.message.delete()
            try:
                await callback.answer(f"✅ Setting diubah ke: {mode_text}")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} ubah setting nama file ke: {choice} - pesan dihapus")
        except Exception as e:
            # Fallback jika gagal hapus pesan - tidak perlu keyboard karena akan dihapus
            await callback.message.edit_text(f"✅ Setting diubah ke: {mode_text}")
            try:
                await callback.answer(f"✅ Setting diubah ke: {mode_text}")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} ubah setting nama file ke: {choice} - fallback edit: {e}")
    else:
        try:
            await callback.answer("❌ Gagal menyimpan setting", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        log_bot(f"Gagal simpan setting nama file untuk user {user_id}")

@router.callback_query(F.data.startswith("send_"))
async def handle_send_choice(callback: types.CallbackQuery):
    """Handler untuk pilihan mode pengiriman VCF"""
    user_id = callback.from_user.id
    # Ambil bagian setelah "send_"
    choice = callback.data[5:]  # Hapus "send_" dari awal

    # Validasi pilihan
    if choice not in ["individual", "group"]:
        try:
            await callback.answer("❌ Pilihan tidak valid", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        return

    # Cek apakah setting sudah sama - tetap hapus pesan
    current_setting = get_to_vcf_send_mode(user_id)
    if current_setting == choice:
        try:
            await callback.message.delete()
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - pesan dihapus")
        except Exception as e:
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - gagal hapus: {e}")
        return

    # Simpan setting
    success = set_to_vcf_send_mode(user_id, choice)

    if success:
        mode_text = "Individual" if choice == "individual" else "(10 files)"

        try:
            # Hapus pesan menu pengaturan setelah setting berubah
            await callback.message.delete()
            try:
                await callback.answer(f"✅ Mode kirim diubah ke {mode_text}")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} ubah mode kirim /to_vcf ke {choice} - pesan dihapus")
        except Exception as e:
            # Fallback jika gagal hapus pesan - tidak perlu keyboard karena akan dihapus
            await callback.message.edit_text(f"✅ Mode kirim diubah ke {mode_text}")
            try:
                await callback.answer(f"✅ Mode kirim diubah ke {mode_text}")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} ubah mode kirim /to_vcf ke {choice} - fallback edit: {e}")
    else:
        try:
            await callback.answer("❌ Gagal menyimpan setting", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        log_bot(f"Gagal simpan setting mode kirim untuk user {user_id}")

@router.callback_query(F.data.startswith("txt_send_"))
async def handle_txt_send_choice(callback: types.CallbackQuery):
    """Handler untuk pilihan mode pengiriman TXT"""
    user_id = callback.from_user.id
    # Ambil bagian setelah "txt_send_"
    choice = callback.data[9:]  # Hapus "txt_send_" dari awal

    # Validasi pilihan
    if choice not in ["individual", "group"]:
        try:
            await callback.answer("❌ Pilihan tidak valid", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        return

    # Cek apakah setting sudah sama - tetap hapus pesan
    current_setting = get_to_txt_send_mode(user_id)
    if current_setting == choice:
        try:
            await callback.message.delete()
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - pesan dihapus")
        except Exception as e:
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - gagal hapus: {e}")
        return

    # Simpan setting
    success = set_to_txt_send_mode(user_id, choice)

    if success:
        mode_text = "Individual" if choice == "individual" else "(10 files)"

        try:
            # Hapus pesan menu pengaturan setelah setting berubah
            await callback.message.delete()
            try:
                await callback.answer(f"✅ Mode kirim diubah ke {mode_text}")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} ubah mode kirim /to_txt ke {choice} - pesan dihapus")
        except Exception as e:
            # Fallback jika gagal hapus pesan - tidak perlu keyboard karena akan dihapus
            await callback.message.edit_text(f"✅ Mode kirim diubah ke {mode_text}")
            try:
                await callback.answer(f"✅ Mode kirim diubah ke {mode_text}")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} ubah mode kirim /to_txt ke {choice} - fallback edit: {e}")
    else:
        try:
            await callback.answer("❌ Gagal menyimpan setting", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        log_bot(f"Gagal simpan setting mode kirim untuk user {user_id}")

@router.callback_query(F.data.startswith("txt_format_"))
async def handle_txt_format_choice(callback: types.CallbackQuery):
    """Handler untuk pilihan format TXT (txt atau xlsx)"""
    user_id = callback.from_user.id
    # Ambil bagian setelah "txt_format_"
    choice = callback.data[11:]  # Hapus "txt_format_" dari awal

    # Validasi pilihan
    if choice not in ["txt", "xlsx"]:
        try:
            await callback.answer("❌ Pilihan tidak valid", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        return

    # Cek apakah setting sudah sama - tetap hapus pesan
    current_setting = get_to_txt_format(user_id)
    if current_setting == choice:
        try:
            await callback.message.delete()
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - pesan dihapus")
        except Exception as e:
            try:
                await callback.answer("✅ Setting sudah aktif")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} klik setting yang sudah aktif: {choice} - gagal hapus: {e}")
        return

    # Simpan setting
    success = set_to_txt_format(user_id, choice)

    if success:
        format_text = "TXT" if choice == "txt" else "XLSX"

        try:
            # Hapus pesan menu pengaturan setelah setting berubah
            await callback.message.delete()
            try:
                await callback.answer(f"✅ Format diubah ke {format_text}")
            except Exception as e:
                logging.warning(f"Callback answer failed (probably expired): {e}")
            log_bot(f"User {user_id} ubah format /to_txt ke {choice} - pesan dihapus")
        except Exception as e:
            # Fallback jika gagal hapus pesan - tidak perlu keyboard karena akan dihapus
            await callback.message.edit_text(f"✅ Format diubah ke {format_text}")
            try:
                await callback.answer(f"✅ Format diubah ke {format_text}")
            except Exception as e2:
                logging.warning(f"Callback answer failed (probably expired): {e2}")
            log_bot(f"User {user_id} ubah format /to_txt ke {choice} - fallback edit: {e}")
    else:
        try:
            await callback.answer("❌ Gagal menyimpan setting", show_alert=True)
        except Exception as e:
            logging.warning(f"Callback answer failed (probably expired): {e}")
        log_bot(f"Gagal simpan setting format untuk user {user_id}")
