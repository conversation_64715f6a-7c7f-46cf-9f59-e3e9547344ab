from aiogram import Router, types, F
from aiogram.types import FSInputFile, InlineKeyboardMarkup, InlineKeyboardButton
import logging
import os
import time
from .payment_service import PaymentService
from .premium_service import PremiumService
from .config_payment import QRIS_IMAGE_PATH, PREMIUM_PRICE, QRIS_TIMEOUT_MINUTES

router = Router()

@router.message(F.text == "PAYMENT_REQUIRED")
async def show_payment_required(message: types.Message):
    """Redirect to new package selection system"""
    try:
        # Redirect to new package selection handler
        from .package_selection_handler import show_package_selection
        await show_package_selection(message)
        return

    except ImportError:
        # Fallback to old system if new handlers not available
        logging.warning("New package selection handler not available, using legacy system")
        pass

    # Legacy system (kept for backward compatibility)
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    try:
        payment_service = PaymentService()

        # Check if user already has pending payment
        pending_payments = payment_service.get_pending_payments()
        user_pending = [p for p in pending_payments if p['user_id'] == user_id]

        if user_pending:
            # User already has pending payment
            existing_payment = user_pending[0]

            # Calculate remaining time
            from datetime import datetime, timezone
            created_time = datetime.strptime(existing_payment['created_at'], "%Y-%m-%d %H:%M:%S")
            created_time = created_time.replace(tzinfo=timezone.utc)
            current_time = datetime.now(timezone.utc)
            elapsed_seconds = (current_time - created_time).total_seconds()
            remaining_seconds = max(0, 300 - elapsed_seconds)  # 5 minutes = 300 seconds
            remaining_minutes = int(remaining_seconds // 60)

            if remaining_seconds > 0:
                # Still active, show existing payment info
                existing_text = f"""⏳ Bayar dulu, ya...

💰 Total bayar: Rp {existing_payment['amount']:,}
⏰ Sisa waktu: {remaining_minutes} menit
🆔 ID Pay: {existing_payment['payment_id']}"""

                keyboard = types.InlineKeyboardMarkup(
                    inline_keyboard=[
                        [types.InlineKeyboardButton(
                            text="🔍 Cek Status Pembayaran",
                            callback_data=f"check_payment:{existing_payment['payment_id']}"
                        )]
                    ]
                )

                sent_msg = await message.answer(existing_text, reply_markup=keyboard)

                # Add active message ID
                payment_service.add_active_message_id(existing_payment['payment_id'], sent_msg.message_id)
                return

        # Generate unique amount
        unique_amount = payment_service.generate_unique_amount(user_id)

        # Generate payment ID (9 digit)
        import random
        payment_id = f"{random.randint(100000000, 999999999)}"
        
        # Payment text
        payment_text = f"""
💎 **Upgrade Premium**

💰 Total bayar: Rp {unique_amount:,}
⏰ Batas waktu: {QRIS_TIMEOUT_MINUTES} menit
🚀 Premium: 30 hari
🆔 ID Pay: `{payment_id}`

⚠️ _Bayar tepat_ ***Rp {unique_amount:,}*** ⚠️"""

        # Create keyboard with check payment button
        keyboard = types.InlineKeyboardMarkup(
            inline_keyboard=[
                [types.InlineKeyboardButton(
                    text="🔍 Cek Status Pembayaran",
                    callback_data=f"check_payment:{payment_id}"
                )]
            ]
        )
        
        # Send QRIS image - try dynamic first, fallback to static
        from payment.config_payment import USE_DYNAMIC_QR, FALLBACK_TO_STATIC, QR_IMAGE_SIZE

        qris_sent = False
        qris_message = None

        # Try dynamic QR first if enabled
        if USE_DYNAMIC_QR:
            try:
                from payment.qris_generator import generate_payment_qr

                # Generate dynamic QR dengan auto-fill amount
                qr_buffer = generate_payment_qr(unique_amount, QR_IMAGE_SIZE)

                if qr_buffer:
                    # Send dynamic QR
                    qris_message = await message.answer_photo(
                        photo=types.BufferedInputFile(qr_buffer.getvalue(), filename="payment_qr.png"),
                        caption=payment_text,
                        parse_mode="Markdown",
                        reply_markup=keyboard
                    )
                    qris_sent = True
                    logging.info(f"Dynamic QR sent to user {username} ({user_id}): Rp {unique_amount}")
                else:
                    logging.warning(f"Dynamic QR generation failed for user {user_id}")

            except Exception as e:
                logging.error(f"Error generating dynamic QR: {e}")

        # Fallback to static QR if dynamic failed or disabled
        if not qris_sent and FALLBACK_TO_STATIC and os.path.exists(QRIS_IMAGE_PATH):
            try:
                photo_file = FSInputFile(QRIS_IMAGE_PATH)
                qris_message = await message.answer_photo(
                    photo=photo_file,
                    caption=payment_text,
                    parse_mode="Markdown",
                    reply_markup=keyboard
                )
                qris_sent = True
                logging.info(f"Static QR sent to user {username} ({user_id}): Rp {unique_amount}")

            except Exception as e:
                logging.error(f"Error sending static QR: {e}")

        # Final fallback to text only
        if not qris_sent:
            qris_message = await message.answer(
                payment_text,
                parse_mode="Markdown",
                reply_markup=keyboard
            )
            logging.warning(f"Text-only payment sent to user {username} ({user_id}): Rp {unique_amount}")

        # Save pending payment if message was sent
        if qris_message:
            payment_service.create_pending_payment(
                payment_id=payment_id,
                user_id=user_id,
                amount=unique_amount,
                qris_message_id=qris_message.message_id
            )

            # Add to payment history
            try:
                from payment.payment_history_service import PaymentHistoryService
                history_service = PaymentHistoryService()
                history_service.add_payment_history(
                    payment_id=payment_id,
                    user_id=user_id,
                    amount=unique_amount,
                    status="pending"
                )
            except ImportError:
                # Silent import - tidak perlu warning
                pass
        
    except Exception as e:
        logging.error(f"Error showing payment required: {e}")
        # Silent error - no user notification

@router.message(F.text == "MEMBERSHIP_REQUIRED")
async def show_membership_required(message: types.Message):
    """Show membership requirement message"""
    try:
        # Use existing membership system
        from management.membership import check_membership, send_membership_message

        user_id = message.from_user.id
        bot = message.bot

        # Check current membership status
        in_group, in_channel = await check_membership(bot, user_id)

        # Send appropriate membership message
        await send_membership_message(message, in_group, in_channel)

    except Exception as e:
        logging.error(f"Error showing membership required: {e}")

        # Fallback message if membership module fails
        fallback_text = """
❌ **Akses Ditolak**

📱 Silakan join grup dan channel terlebih dahulu.
🔄 Setelah join, ketik command lagi.
"""
        await message.answer(fallback_text, parse_mode="Markdown")





# Callback handler untuk tombol cek pembayaran
@router.callback_query(F.data.startswith("check_payment:"))
async def check_payment_callback(callback: types.CallbackQuery):
    """Handle check payment button"""
    try:
        # Extract payment ID from callback data
        payment_id = callback.data.split(":", 1)[1]
        user_id = callback.from_user.id

        # Check if payment exists and is still pending
        payment_service = PaymentService()
        pending_payments = payment_service.get_pending_payments()

        # Find the payment
        payment_found = None
        for payment in pending_payments:
            if payment['payment_id'] == payment_id and payment['user_id'] == user_id:
                payment_found = payment
                break

        if not payment_found:
            # Payment not found or already processed
            premium_service = PremiumService()
            is_premium, premium_info = premium_service.check_premium_status(user_id)

            if is_premium:
                await callback.answer("Pembayaran berhasil! Premium sudah aktif.", show_alert=True)
            else:
                await callback.answer("Pembayaran tidak ditemukan atau sudah expired.", show_alert=True)
            return

        # Payment still pending
        await callback.answer(
            f"Pembayaran masih pending.\n\n"
            f"Total bayar: Rp {payment_found['amount']:,}\n"
            f"Status update setelah pembayaran.",
            show_alert=True
        )

    except Exception as e:
        logging.error(f"Error checking payment: {e}")
        await callback.answer("Terjadi kesalahan saat mengecek pembayaran.", show_alert=True)
