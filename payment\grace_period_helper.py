"""
Grace Period Helper - Helper functions untuk Grace Period system
"""

import logging
from typing import Optional
from .premium_expiry_checker import get_expiry_checker
from .premium_service import PremiumService


class GracePeriodHelper:
    """Helper class untuk Grace Period operations"""
    
    @staticmethod
    def start_process(user_id: int, process_type: str):
        """
        Mark user sebagai sedang dalam proses (Grace Period)
        
        Args:
            user_id: ID user
            process_type: <PERSON><PERSON> proses ('convert_file', 'download', 'upload', etc)
        """
        try:
            checker = get_expiry_checker()
            if checker:
                checker.start_process(user_id, process_type)
        except Exception:
            pass  # Silent fail jika checker belum init
    
    @staticmethod
    async def complete_process(user_id: int):
        """
        Mark proses selesai dan kirim notifikasi expired jika perlu
        
        Args:
            user_id: ID user
        """
        try:
            checker = get_expiry_checker()
            if checker:
                await checker.complete_process(user_id)
        except Exception:
            pass  # Silent fail jika checker belum init
    
    @staticmethod
    def check_user_access(user_id: int) -> bool:
        """
        Check apakah user bisa akses fitur (premium, trial, atau grace period)

        Returns True jika user bisa akses, False jika tidak
        """
        try:
            # Check admin/super admin
            from .trial_service import TrialService
            trial_service = TrialService()

            if trial_service._is_admin_or_super_admin(user_id):
                return True

            # Check Grace Period first (untuk proses aktif)
            from .premium_expiry_checker import get_expiry_checker
            checker = get_expiry_checker()
            if checker and user_id in checker.active_processes:
                return True  # Grace Period

            # Check premium
            premium_service = PremiumService()
            is_premium, _ = premium_service.check_premium_status(user_id)
            if is_premium:
                return True

            # Check trial - HANYA JIKA AKTIF (bukan expired)
            is_trial_active, trial_info = trial_service.check_trial_status(user_id)
            if is_trial_active and trial_info:
                return True  # Trial aktif = langsung akses

            # Check user baru (tidak di database) - TIDAK BOLEH AKSES
            # User baru harus join grup & channel dulu, baru masuk ke total_users.txt
            user_exists = trial_service._user_exists_in_total_users(user_id)
            if not user_exists:
                return False  # User baru = harus join grup/channel dulu

            return False

        except Exception:
            # Fallback ke check premium saja
            try:
                premium_service = PremiumService()
                is_premium, _ = premium_service.check_premium_status(user_id)
                return is_premium
            except Exception:
                return False
    
    @staticmethod
    async def send_premium_required_message(bot, user_id: int):
        """
        Kirim pesan bahwa premium diperlukan
        
        Args:
            bot: Bot instance
            user_id: ID user
        """
        try:
            message = """🔒 Premium Only

💎 Beli premium untuk akses."""
            
            await bot.send_message(
                chat_id=user_id,
                text=message
            )
        except Exception:
            pass  # Silent fail


# Convenience functions untuk mudah dipakai
def start_user_process(user_id: int, process_type: str):
    """Start process tracking untuk user"""
    GracePeriodHelper.start_process(user_id, process_type)


async def complete_user_process(user_id: int):
    """Complete process tracking untuk user"""
    await GracePeriodHelper.complete_process(user_id)


def check_user_premium_access(user_id: int) -> bool:
    """Check user access (premium, trial, grace period)"""
    return GracePeriodHelper.check_user_access(user_id)


async def block_non_premium_user(bot, user_id: int):
    """Block non-premium user dengan pesan"""
    await GracePeriodHelper.send_premium_required_message(bot, user_id)
