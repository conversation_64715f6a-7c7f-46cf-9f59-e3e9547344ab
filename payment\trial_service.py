"""
Trial Service - Kelola sistem trial 3 jam untuk user
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Tu<PERSON>, Optional, List, Dict
from .config_payment import TRIAL_USERS_DB


class TrialService:
    """Service untuk mengelola trial 3 jam user"""
    
    # Trial duration: 3 jam = 10800 detik
    TRIAL_DURATION_SECONDS = 21600
    
    def __init__(self):
        self.ensure_trial_database_exists()
    
    def ensure_trial_database_exists(self):
        """Pastikan file trial database ada"""
        try:
            import os
            os.makedirs(os.path.dirname(TRIAL_USERS_DB), exist_ok=True)
            
            if not os.path.exists(TRIAL_USERS_DB):
                with open(TRIAL_USERS_DB, 'w', encoding='utf-8') as f:
                    pass  # Create empty file
        except Exception as e:
            logging.error(f"Error creating trial database: {e}")
    
    def is_eligible_for_trial(self, user_id: int) -> bool:
        """
        CRITICAL: Check apakah user eligible untuk trial

        HANYA untuk user yang:
        - <PERSON> di total_users.txt
        - BUKAN 5 kategori yang dikecualikan
        """
        try:
            # 1. HARUS ada di total_users.txt
            if not self._user_exists_in_total_users(user_id):
                return False

            # 2. Check admin status - EXCLUDE
            if self._is_admin_or_super_admin(user_id):
                return False

            # 3. Check active premium - EXCLUDE
            if self._has_active_premium(user_id):
                return False

            # 4. Check admin granted premium history - EXCLUDE
            if self._has_admin_granted_premium(user_id):
                return False

            # 5. Check trial history - EXCLUDE
            if self.has_used_trial_before(user_id):
                return False

            # Eligible: ada di database DAN bukan 5 kategori yang dikecualikan
            return True

        except Exception as e:
            logging.error(f"Error checking trial eligibility for user {user_id}: {e}")
            return False
    
    def has_used_trial_before(self, user_id: int) -> bool:
        """
        CRITICAL: Check apakah user pernah pakai trial (active/expired)
        """
        try:
            trial_record = self._get_trial_record(user_id)
            return trial_record is not None
        except Exception as e:
            logging.error(f"Error checking trial history for user {user_id}: {e}")
            return True  # Safe default: assume used
    
    def check_trial_status(self, user_id: int) -> Tuple[bool, Optional[Dict]]:
        """
        Check status trial user
        Returns: (is_active: bool, trial_info: dict)
        """
        try:
            trial_record = self._get_trial_record(user_id)

            if not trial_record:
                return False, None

            if trial_record['status'] == 'expired':
                return False, trial_record

            # Check timing
            expired_datetime = datetime.strptime(trial_record['expired_date'], "%Y-%m-%d %H:%M:%S")
            # Make timezone aware (UTC)
            expired_datetime = expired_datetime.replace(tzinfo=timezone.utc)
            current_utc = datetime.now(timezone.utc)

            if current_utc > expired_datetime:
                # Trial expired, update status
                self._update_trial_status(user_id, "expired")
                trial_record['status'] = 'expired'
                return False, trial_record

            return True, trial_record
            
        except Exception as e:
            logging.error(f"Error checking trial status for user {user_id}: {e}")
            return False, None
    
    def activate_trial(self, user_id: int, trial_type: str = "auto") -> bool:
        """
        CRITICAL: Activate trial untuk user
        
        Args:
            user_id: ID user
            trial_type: "auto" (new user) atau "manual" (existing user)
        """
        try:
            # Double check eligibility
            if not self.is_eligible_for_trial(user_id):
                # logging.error(f"User {user_id} not eligible for trial")  # Disabled - tidak perlu log error
                return False
            
            # Check no existing active trial
            is_active, _ = self.check_trial_status(user_id)
            if is_active:
                logging.error(f"User {user_id} already has active trial")
                return False
            
            # Calculate timing (UTC)
            activated_utc = datetime.now(timezone.utc)
            expired_utc = activated_utc + timedelta(seconds=self.TRIAL_DURATION_SECONDS)
            
            # Create trial record
            trial_record = {
                "user_id": user_id,
                "activated_date": activated_utc.strftime("%Y-%m-%d %H:%M:%S"),
                "expired_date": expired_utc.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "active",
                "trial_type": trial_type
            }
            
            # Save to database
            self._save_trial_record(trial_record)
            
            return True
            
        except Exception as e:
            logging.error(f"Error activating trial for user {user_id}: {e}")
            return False
    
    def get_active_trial_users(self) -> List[Dict]:
        """Get semua user dengan trial aktif - Simple format"""
        try:
            active_trials = []

            with open(TRIAL_USERS_DB, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            try:
                                if parts[3] == 'active':  # status
                                    active_trials.append({
                                        'user_id': int(parts[0]),
                                        'activated_date': parts[1],
                                        'expired_date': parts[2],
                                        'status': parts[3]
                                    })
                            except ValueError:
                                continue

            return active_trials

        except FileNotFoundError:
            return []
        except Exception as e:
            logging.error(f"Error getting active trial users: {e}")
            return []
    
    def get_trial_eligible_users_from_total(self) -> List[int]:
        """
        CRITICAL: Get eligible users dari total_user.txt untuk startup notification
        """
        try:
            eligible_users = []

            # Load total users
            try:
                with open("pusat_admin/database/total_users.txt", 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            parts = line.split('|')
                            if len(parts) >= 1:
                                try:
                                    user_id = int(parts[0])

                                    # Check eligibility
                                    if self.is_eligible_for_trial(user_id):
                                        eligible_users.append(user_id)
                                except ValueError:
                                    continue
            except FileNotFoundError:
                # logging.error("pusat_admin/database/total_users.txt not found")  # Disabled
                pass

            # logging.error(f"Found {len(eligible_users)} eligible users for trial")  # Disabled
            return eligible_users

        except Exception as e:
            logging.error(f"Error getting eligible users from total: {e}")
            return []
    
    def _get_trial_record(self, user_id: int) -> Optional[Dict]:
        """Get trial record untuk user - Simple format: user_id|activated|expired|status"""
        try:
            with open(TRIAL_USERS_DB, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            try:
                                if int(parts[0]) == user_id:
                                    return {
                                        'user_id': int(parts[0]),
                                        'activated_date': parts[1],
                                        'expired_date': parts[2],
                                        'status': parts[3]
                                    }
                            except ValueError:
                                continue
            return None

        except FileNotFoundError:
            return None
        except Exception as e:
            logging.error(f"Error getting trial record for user {user_id}: {e}")
            return None
    
    def _save_trial_record(self, trial_data: Dict):
        """CRITICAL: Save trial record - Simple format: user_id|activated|expired|status"""
        try:
            # Format: user_id|activated_date|expired_date|status (dengan newline yang jelas)
            line = f"{trial_data['user_id']}|{trial_data['activated_date']}|{trial_data['expired_date']}|{trial_data['status']}\n"

            with open(TRIAL_USERS_DB, 'a', encoding='utf-8') as f:
                f.write(line)
                f.flush()  # Pastikan langsung ditulis ke disk
        except Exception as e:
            logging.error(f"Error saving trial record: {e}")
            raise e
    
    def _update_trial_status(self, user_id: int, new_status: str):
        """Update status trial user - Simple format"""
        try:
            # Read all records
            lines = []
            try:
                with open(TRIAL_USERS_DB, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except FileNotFoundError:
                return

            # Update target record
            updated = False
            for i, line in enumerate(lines):
                line = line.strip()
                if line:
                    parts = line.split('|')
                    if len(parts) >= 4:
                        try:
                            if int(parts[0]) == user_id:
                                # Update status (last part)
                                parts[3] = new_status
                                lines[i] = '|'.join(parts) + '\n'
                                updated = True
                                break
                        except ValueError:
                            continue

            if updated:
                # Write back all records
                with open(TRIAL_USERS_DB, 'w', encoding='utf-8') as f:
                    f.writelines(lines)

        except Exception as e:
            logging.error(f"Error updating trial status for user {user_id}: {e}")
    
    def _is_admin_or_super_admin(self, user_id: int) -> bool:
        """Check apakah user admin atau super admin"""
        try:
            from pusat_admin.auth_admin import get_admin_level_by_id

            # Check by user ID
            admin_level = get_admin_level_by_id(user_id)
            if admin_level in ["admin", "super_admin"]:
                return True

            return False
        except Exception:
            return False
    
    def _has_active_premium(self, user_id: int) -> bool:
        """Check apakah user punya premium aktif"""
        try:
            from .premium_service import PremiumService
            premium_service = PremiumService()
            is_premium, _ = premium_service.check_premium_status(user_id)
            return is_premium
        except Exception:
            return False
    
    def _has_admin_granted_premium(self, user_id: int) -> bool:
        """Check apakah user pernah dapat premium gratis dari admin"""
        try:
            from .config_payment import PAYMENT_HISTORY_DB
            import json

            with open(PAYMENT_HISTORY_DB, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        payment_data = json.loads(line)
                        if (payment_data.get('user_id') == user_id and
                            payment_data.get('payment_method') == 'Admin Gratis'):
                            return True
            return False
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def _user_exists_in_total_users(self, user_id: int) -> bool:
        """Check apakah user ada di total_user.txt"""
        try:
            with open("pusat_admin/database/total_users.txt", 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split('|')
                        if len(parts) >= 1:
                            try:
                                if int(parts[0]) == user_id:
                                    return True
                            except ValueError:
                                continue
            return False

        except FileNotFoundError:
            return False
        except Exception:
            return False
