from aiogram import Bo<PERSON>, types
from config import GROUP_ID, CHANNEL_ID, GROUP_LINK, CHANNEL_LINK

# Simpan message_id pesan join per user (hanya untuk sesi bot ber<PERSON><PERSON>)
join_message_ids = {}

async def check_membership(bot: Bo<PERSON>, user_id: int):
    """
    ✅ PERBAIKAN: Check membership dengan support untuk user restricted

    Status yang diterima sebagai member valid:
    - "member": Member biasa
    - "administrator": <PERSON>min grup/channel
    - "creator": Owner grup/channel
    - "restricted": User yang dibatasi tapi masih member (PERBAIKAN BARU)

    Args:
        bot: Bot instance
        user_id: ID user yang akan dicek

    Returns:
        Tuple[bool, bool]: (in_group, in_channel)
    """
    in_group = False
    in_channel = False
    try:
        gr = await bot.get_chat_member(GROUP_ID, user_id)
        # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
        # User yang dibatasi (restricted) masih dianggap member yang sah
        if gr.status in ("member", "administrator", "creator", "restricted"):
            in_group = True
        # Debug logging untuk troubleshooting (uncomment jika perlu debug)
        # logging.debug(f"Group check user {user_id}: status={gr.status}, valid={in_group}")
    except Exception as e:
        # logging.debug(f"Group check error for user {user_id}: {e}")
        pass
    try:
        ch = await bot.get_chat_member(CHANNEL_ID, user_id)
        # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
        # User yang dibatasi (restricted) masih dianggap member yang sah
        if ch.status in ("member", "administrator", "creator", "restricted"):
            in_channel = True
        # Debug logging untuk troubleshooting (uncomment jika perlu debug)
        # logging.debug(f"Channel check user {user_id}: status={ch.status}, valid={in_channel}")
    except Exception as e:
        # logging.debug(f"Channel check error for user {user_id}: {e}")
        pass
    return in_group, in_channel

async def send_membership_message(message: types.Message, in_group: bool, in_channel: bool):
    keyboard = []
    if not in_group:
        keyboard.append([types.InlineKeyboardButton(text="👥 Gabung Grup", url=GROUP_LINK)])
    if not in_channel:
        keyboard.append([types.InlineKeyboardButton(text="📢 Join Channel", url=CHANNEL_LINK)])

    if not in_group and not in_channel:
        text = (
            "👑 *Pemilik Bot @KazuhaID2*\n\n"
            "🚫 Kamu belum gabung grup & channel!\n"
            "Setelah join, balik lagi dan ketik /start👍\n\n"
            "Yuk gabung dulu biar bisa pakai bot ini 👇"
        )
    elif not in_group:
        text = (
            "👑 *Pemilik Bot @KazuhaID2*\n\n"
            "👥 Gabung grup dulu ya biar bisa lanjut.\n"
            "Setelah join, balik lagi dan ketik /start👍\n\n"
            "Klik tombol di bawah ini 👇"
        )
    elif not in_channel:
        text = (
            "👑 *Pemilik Bot @KazuhaID2*\n\n"
            "📢 Join channel dulu ya biar bisa lanjut.\n"
            "Setelah join, balik lagi dan ketik /start👍\n\n"
            "Klik tombol di bawah ini 👇"
        )

    else:
        return

    sent = await message.answer(text, reply_markup=types.InlineKeyboardMarkup(inline_keyboard=keyboard), parse_mode="Markdown")
    # Simpan message_id pesan join terakhir user
    join_message_ids[message.from_user.id] = sent.message_id

async def delete_join_message(bot: Bot, user_id: int, chat_id: int):
    msg_id = join_message_ids.get(user_id)
    if msg_id:
        try:
            await bot.delete_message(chat_id, msg_id)
        except Exception:
            pass
        join_message_ids.pop(user_id, None)