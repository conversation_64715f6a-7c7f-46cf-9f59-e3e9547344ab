"""
Main Menu dan Router untuk Admin Panel
"""

import logging
import time
from aiogram import Router, types, F
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_main_menu_keyboard
from .utils import log_user, log_bot
from .auth_admin import is_admin, log_admin_activity

# Import semua feature modules
from . import status_bot
from . import kelola_user
from . import broadcast_pesan
from . import kirim_pesan
from . import kelola_admin
from . import kontrol_bot
from . import info_file
from . import blokir_user
from . import cari_user
from . import status_user_aktif  # User aktif dengan format baru
from . import payment_stats  # Payment statistics
from . import payment_history  # Payment history
from . import free_premium  # Free premium
from . import backup_system  # Backup system

router = Router()

# Include semua router dari feature modules
router.include_router(status_bot.router)
router.include_router(kelola_user.router)
router.include_router(broadcast_pesan.router)
router.include_router(kirim_pesan.router)
router.include_router(kelola_admin.router)
router.include_router(kontrol_bot.router)
router.include_router(info_file.router)
router.include_router(blokir_user.router)
router.include_router(cari_user.router)
router.include_router(status_user_aktif.router)
router.include_router(payment_stats.router)
router.include_router(payment_history.router)
router.include_router(free_premium.router)  # Payment statistics
router.include_router(backup_system.router)  # Backup system
# router.include_router(user_aktif.router)  # Tidak digunakan lagi


@router.message(Command("kazuhaid"), F.chat.type == "private")
async def kazuhaid_command(message: types.Message, state: FSMContext):
    """Command utama untuk akses admin panel"""
    await state.clear()

    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak. Anda bukan admin.")
        return

    # AUTO-CREATE SEMUA STRUKTUR - PENTING!
    try:
        from .data_sync import auto_create_all_structure, sync_user_to_all_databases, sync_active_user
        from .membership_checker import MembershipChecker

        # Auto-create semua file, folder, database yang belum ada
        auto_create_all_structure()

        user_id = str(message.from_user.id)
        username_data = message.from_user.username or ''
        first_name = message.from_user.first_name or ''
        last_name = message.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Sync ke semua database
        sync_user_to_all_databases(user_id, username_data, first_name, last_name, timestamp)

        # SEMUA USER YANG MENGGUNAKAN COMMAND DIANGGAP AKTIF
        # Langsung sync ke active users cache
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
        # logging.info(f"ADMIN PANEL ACCESS - Updated user {user_id}: username='{username_data}', name='{first_name} {last_name}'")  # Disabled

    except Exception as e:
        logging.error(f"Error syncing user data: {e}")

    log_user(message)
    log_admin_activity(username, "Akses menu admin")
    
    welcome_text = f"""🎛️ **ADMIN PANEL**

Selamat datang @{username}"""

    await message.answer(
        welcome_text,
        reply_markup=create_main_menu_keyboard(),
        parse_mode="Markdown"
    )

    await state.set_state(AdminStates.main_menu)
    log_bot("Menampilkan menu kazuhaid admin")


@router.callback_query(F.data == "admin_back")
async def back_to_main_menu(callback: types.CallbackQuery, state: FSMContext):
    """Kembali ke menu utama"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin ke active users
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")

    welcome_text = f"""🎛️ **ADMIN PANEL**

Selamat datang @{username}"""

    await callback.message.edit_text(
        welcome_text,
        reply_markup=create_main_menu_keyboard(),
        parse_mode="Markdown"
    )

    await state.set_state(AdminStates.main_menu)

    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer()
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")


@router.callback_query(F.data == "admin_close")
async def close_admin_panel(callback: types.CallbackQuery, state: FSMContext):
    """Tutup admin panel"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    await callback.message.edit_text(
        "✅ **Panel ditutup**\n\n/kazuhaid untuk buka lagi",
        parse_mode="Markdown"
    )
    
    await state.clear()

    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer()
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")

    log_admin_activity(username, "Tutup admin panel")


@router.callback_query(F.data == "noop")
async def noop_callback(callback: types.CallbackQuery):
    """No operation callback untuk button yang tidak perlu action"""
    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer()
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")
