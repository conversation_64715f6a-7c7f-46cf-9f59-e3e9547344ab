import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional
from .config_payment import PREMIUM_USERS_DB, PREMIUM_DURATION_SECONDS

class PremiumService:
    def __init__(self):
        pass

    def get_utc_datetime(self):
        """Ambil datetime dengan timezone UTC untuk konsistensi sistem"""
        return datetime.now(timezone.utc)

    def get_utc_datetime_from_string(self, datetime_str):
        """Convert string datetime ke UTC timezone"""
        dt = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
        return dt.replace(tzinfo=timezone.utc)

    def format_utc_to_wib_display(self, utc_datetime):
        """Format UTC datetime untuk display WIB ke user"""
        # Convert UTC ke WIB (+7 jam)
        wib_datetime = utc_datetime + timedelta(hours=7)

        # Format: DD-MM **HH:MM WIB**
        date_part = wib_datetime.strftime("%d-%m")
        time_part = wib_datetime.strftime("%H:%M")

        return f"{date_part} **{time_part} WIB**"

    def format_remaining_time_display(self, remaining_seconds):
        """Format remaining time untuk display ke user"""
        if remaining_seconds <= 0:
            return "Sudah habis"

        days = remaining_seconds // 86400
        hours = (remaining_seconds % 86400) // 3600
        minutes = (remaining_seconds % 3600) // 60

        if days > 0:
            return f"{days} hari {hours} jam"
        elif hours > 0:
            return f"{hours} jam {minutes} menit"
        elif minutes > 0:
            return f"{minutes} menit"
        else:
            return f"{remaining_seconds} detik"

    def format_utc_to_wib_admin_display(self, utc_datetime):
        """Format UTC datetime untuk display admin (simple WIB format)"""
        # Convert UTC ke WIB (+7 jam)
        wib_datetime = utc_datetime + timedelta(hours=7)

        # Format: MM-DD HH:MM WIB (untuk admin panel)
        return wib_datetime.strftime("%m-%d %H:%M WIB")

    def calculate_expiry_datetime(self, start_datetime, duration_seconds):
        """
        Hitung waktu expired berdasarkan sistem detik UTC:
        - Exact duration dalam detik
        - Fair untuk semua user (timezone independent)
        - UTC-based calculation

        Args:
            start_datetime: UTC datetime untuk mulai
            duration_seconds: Durasi dalam detik (int)

        Returns:
            tuple: (expired_datetime, expiry_type)
        """
        # Validasi parameter
        if not isinstance(duration_seconds, int):
            raise ValueError(f"duration_seconds must be int, got {type(duration_seconds)}")

        if duration_seconds <= 0:
            raise ValueError(f"duration_seconds must be positive, got {duration_seconds}")

        # Pastikan start_datetime adalah UTC
        if start_datetime.tzinfo is None:
            start_datetime = start_datetime.replace(tzinfo=timezone.utc)
        elif start_datetime.tzinfo != timezone.utc:
            start_datetime = start_datetime.astimezone(timezone.utc)

        # Hitung expired datetime: start_datetime + duration_seconds
        expired_datetime = start_datetime + timedelta(seconds=duration_seconds)

        # Menggunakan tipe "seconds" untuk sistem UTC
        expiry_type = "seconds"

        return expired_datetime, expiry_type
    
    def check_premium_status(self, user_id: int) -> Tuple[bool, Optional[Dict]]:
        """Check if user has active premium - UTC based"""
        try:
            premium_users = self.get_all_premium_users()

            for user in premium_users:
                if user['user_id'] == user_id:
                    # ✅ UTC SYSTEM: Gunakan UTC untuk semua operasi
                    expired_datetime = self.get_utc_datetime_from_string(user['expired_date'])
                    current_datetime = self.get_utc_datetime()

                    # ✅ UTC SYSTEM: Bandingkan dengan UTC datetime
                    is_active = current_datetime <= expired_datetime

                    # Get expiry type from user data (default to seconds for new system)
                    expiry_type = user.get('expiry_type', 'seconds')

                    if is_active:
                        # Still active - hitung remaining time
                        remaining_time = expired_datetime - current_datetime
                        remaining_seconds = max(0, int(remaining_time.total_seconds()))
                        remaining_hours = remaining_seconds // 3600
                        remaining_days = remaining_hours // 24

                        user_info = {
                            'activated_date': user['activated_date'],
                            'expired_date': user['expired_date'],
                            'remaining_days': remaining_days,
                            'remaining_hours': remaining_hours,
                            'remaining_seconds': remaining_seconds,
                            'is_active': True,
                            'expiry_type': expiry_type,
                            'transaction_id': user.get('transaction_id', 'N/A')
                        }
                        return True, user_info
                    else:
                        # Expired
                        user_info = {
                            'activated_date': user['activated_date'],
                            'expired_date': user['expired_date'],
                            'remaining_days': 0,
                            'remaining_hours': 0,
                            'remaining_seconds': 0,
                            'is_active': False,
                            'expiry_type': expiry_type,
                            'transaction_id': user.get('transaction_id', 'N/A')
                        }
                        return False, user_info

            # User not found
            return False, None

        except Exception as e:
            logging.error(f"Error checking premium status: {e}")
            return False, None
    
    def activate_premium(self, user_id: int, transaction_id: str, payment_method: str = "QRIS") -> bool:
        """Activate premium for user with default 30 days (pure seconds system)"""
        try:
            # ✅ UTC SYSTEM: Gunakan UTC timezone untuk konsistensi
            current_datetime = self.get_utc_datetime()

            # ✅ PURE SECONDS SYSTEM: Langsung pakai seconds tanpa conversion
            duration_seconds = PREMIUM_DURATION_SECONDS

            # Hitung expired datetime berdasarkan sistem UTC + seconds
            expired_datetime, expiry_type = self.calculate_expiry_datetime(current_datetime, duration_seconds)

            # Create premium user record
            premium_user = {
                "user_id": user_id,
                "transaction_id": transaction_id,
                "payment_method": payment_method,
                "activated_date": current_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "expired_date": expired_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "duration_seconds": duration_seconds,
                "expiry_type": expiry_type,
                "status": "active"
            }

            # Remove existing premium record for this user (if any)
            self.remove_premium_user(user_id)

            # Add new premium record
            with open(PREMIUM_USERS_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(premium_user) + '\n')

            logging.info(f"Premium activated for user {user_id}: {transaction_id} (duration: {duration_seconds}s, expiry_type: {expiry_type})")
            return True

        except Exception as e:
            logging.error(f"Error activating premium: {e}")
            return False

    def extend_premium_duration(self, user_id: int, transaction_id: str, payment_method: str = "Admin Gratis", additional_seconds: int = 86400) -> bool:
        """
        Extend premium duration for user (TAMBAH waktu, bukan replace).
        Jika user masih aktif: tambah dari expire date existing
        Jika user sudah expired: tambah dari sekarang

        Args:
            user_id: ID user
            transaction_id: ID transaksi
            payment_method: Metode pembayaran
            additional_seconds: Detik yang ditambahkan

        Returns:
            bool: True jika berhasil
        """
        try:
            current_datetime = self.get_utc_datetime()

            # Check apakah user sudah punya premium aktif
            is_premium, premium_info = self.check_premium_status(user_id)

            if is_premium and premium_info:
                # User masih aktif - extend dari expire date existing
                existing_expired_str = premium_info['expired_date']
                existing_expired_datetime = self.get_utc_datetime_from_string(existing_expired_str)

                # Tambah additional_seconds dari expire date existing
                new_expired_datetime = existing_expired_datetime + timedelta(seconds=additional_seconds)

                logging.info(f"Extending active premium for user {user_id}: {existing_expired_str} + {additional_seconds}s = {new_expired_datetime.strftime('%Y-%m-%d %H:%M:%S')}")

            else:
                # User tidak aktif atau expired - extend dari sekarang
                new_expired_datetime = current_datetime + timedelta(seconds=additional_seconds)

                logging.info(f"Extending expired/new premium for user {user_id}: now + {additional_seconds}s = {new_expired_datetime.strftime('%Y-%m-%d %H:%M:%S')}")

            # Create premium user record dengan expire date yang sudah di-extend
            premium_user = {
                "user_id": user_id,
                "transaction_id": transaction_id,
                "payment_method": payment_method,
                "activated_date": current_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "expired_date": new_expired_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "duration_seconds": additional_seconds,  # Durasi yang ditambahkan
                "expiry_type": "seconds",
                "status": "active"
            }

            # Remove existing premium record for this user (if any)
            self.remove_premium_user(user_id)

            # Add new premium record dengan waktu yang sudah di-extend
            with open(PREMIUM_USERS_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(premium_user) + '\n')

            logging.info(f"Premium extended for user {user_id}: +{additional_seconds}s, new expiry: {new_expired_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            return True

        except Exception as e:
            logging.error(f"Error extending premium: {e}")
            return False

    def activate_premium_with_duration(self, user_id: int, transaction_id: str, payment_method: str = "QRIS", duration_seconds: int = 86400) -> bool:
        """Activate premium for user with custom duration in seconds"""
        try:
            # ✅ UTC SYSTEM: Gunakan UTC timezone untuk konsistensi
            current_datetime = self.get_utc_datetime()

            # ✅ SECONDS SYSTEM: Hitung expired datetime berdasarkan detik
            expired_datetime, expiry_type = self.calculate_expiry_datetime(current_datetime, duration_seconds)

            # Create premium user record
            premium_user = {
                "user_id": user_id,
                "transaction_id": transaction_id,
                "payment_method": payment_method,
                "activated_date": current_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "expired_date": expired_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                "duration_seconds": duration_seconds,
                "expiry_type": expiry_type,
                "status": "active"
            }

            # Remove existing premium record for this user (if any)
            self.remove_premium_user(user_id)

            # Add new premium record
            with open(PREMIUM_USERS_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(premium_user) + '\n')

            logging.info(f"Premium activated for user {user_id}: {transaction_id} (duration: {duration_seconds}s)")
            return True

        except Exception as e:
            logging.error(f"Error activating premium with duration: {e}")
            return False

    def remove_premium_user(self, user_id: int):
        """Remove existing premium record for user"""
        try:
            premium_users = self.get_all_premium_users()
            
            # Filter out the user
            updated_users = [user for user in premium_users if user['user_id'] != user_id]
            
            # Save updated list
            with open(PREMIUM_USERS_DB, 'w', encoding='utf-8') as f:
                for user in updated_users:
                    f.write(json.dumps(user) + '\n')
            
        except Exception as e:
            logging.error(f"Error removing premium user: {e}")
    
    def get_all_premium_users(self) -> List[Dict]:
        """Get all premium users"""
        try:
            premium_users = []

            try:
                with open(PREMIUM_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            user = json.loads(line)

                            # ✅ UTC SYSTEM: Gunakan logika yang sama dengan check_premium_status
                            try:
                                expired_datetime = self.get_utc_datetime_from_string(user['expired_date'])
                                current_datetime = self.get_utc_datetime()

                                # ✅ UTC SYSTEM: Bandingkan dengan UTC datetime
                                user['is_active'] = current_datetime <= expired_datetime

                            except Exception as e:
                                logging.warning(f"Error checking active status for user {user.get('user_id')}: {e}")
                                user['is_active'] = False

                            premium_users.append(user)
            except FileNotFoundError:
                pass

            return premium_users

        except Exception as e:
            logging.error(f"Error getting premium users: {e}")
            return []
    
    def get_premium_stats(self) -> Dict:
        """Get premium statistics"""
        try:
            premium_users = self.get_all_premium_users()
            
            total_users = len(premium_users)
            active_users = len([u for u in premium_users if u['is_active']])
            expired_users = total_users - active_users
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'expired_users': expired_users
            }
            
        except Exception as e:
            logging.error(f"Error getting premium stats: {e}")
            return {
                'total_users': 0,
                'active_users': 0,
                'expired_users': 0
            }

    def check_premium_with_grace(self, user_id: int) -> bool:
        """
        Check premium dengan Grace Period untuk user yang lagi proses
        """
        try:
            from .premium_expiry_checker import get_expiry_checker

            checker = get_expiry_checker()
            if checker:
                return checker.check_premium_with_grace(user_id)
            else:
                # Fallback ke check normal jika checker belum init
                is_premium, _ = self.check_premium_status(user_id)
                return is_premium

        except Exception as e:
            logging.error(f"Error checking premium with grace for user {user_id}: {e}")
            # Fallback ke check normal
            is_premium, _ = self.check_premium_status(user_id)
            return is_premium
