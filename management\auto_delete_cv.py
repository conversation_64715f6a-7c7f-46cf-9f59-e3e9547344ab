"""
Auto-Delete CV Messages System
Hapus otomatis semua pesan yang mengandung "cv" di grup
- Hapus pesan lama (history cleanup)
- Hapus pesan baru (real-time)
- <PERSON><PERSON><PERSON> admin
- Silent delete (tanpa notifikasi)
"""

import asyncio
import re
from aiogram import Router, F
from aiogram.types import Message
from pusat_admin.auth_admin import is_admin_by_id
from utils.text_cleaner import ultimate_text_cleaner, extract_clean_text_from_message

router = Router()

# Konfigurasi
CLEANUP_BATCH_SIZE = 50  # Proses 50 pesan per batch
CLEANUP_DELAY = 1  # Delay 1 detik antar batch
MAX_CLEANUP_MESSAGES = 2000  # Maksimal 2000 pesan ke belakang
DELETE_DELAY = 0.5  # Delay 0.5 detik sebelum hapus pesan baru

class CVMessageDeleter:
    """Class untuk handle auto-delete pesan CV"""
    
    def __init__(self):
        self.is_cleanup_running = False
        self.deleted_count = 0
        
    def contains_cv_word(self, text: str) -> bool:
        """
        Legacy function untuk backward compatibility
        Cek apakah text mengandung kata 'cv' sebagai whole word
        """
        if not text:
            return False

        # Convert ke lowercase dan split jadi kata-kata
        words = re.findall(r'\b\w+\b', text.lower())
        return 'cv' in words

    def contains_cv_word_enhanced(self, text: str, entities: list = None) -> bool:
        """
        Enhanced CV detection dengan comprehensive text cleaning dan Unicode support

        Args:
            text: Text yang akan dicek
            entities: List of MessageEntity dari Telegram

        Returns:
            bool: True jika mengandung kata 'CV', False jika tidak
        """
        if not text:
            return False

        # Clean text dari semua formatting untuk deteksi yang akurat
        cleaned_text = ultimate_text_cleaner(text, entities)

        # Convert ke lowercase untuk case-insensitive detection
        cleaned_text = cleaned_text.lower()

        # Enhanced word extraction yang support Unicode Mathematical symbols
        # Gunakan multiple methods untuk memastikan deteksi yang comprehensive

        # Method 1: Standard word boundary (untuk ASCII)
        ascii_words = re.findall(r'\b\w+\b', cleaned_text)

        # Method 2: Unicode-aware word extraction (untuk Mathematical symbols)
        # Include Mathematical Alphanumeric Symbols range (U+1D400-U+1D7FF)
        unicode_words = re.findall(r'[\w\u1D400-\u1D7FF]+', cleaned_text)

        # Method 3: Split by non-letter characters (fallback)
        split_words = re.findall(r'[a-zA-Z\u1D400-\u1D7FF]+', cleaned_text)

        # Combine all methods untuk comprehensive detection
        all_words = set(ascii_words + unicode_words + split_words)

        # Check if 'cv' exists in any of the extracted words
        return 'cv' in all_words
    
    async def delete_message_safe(self, message: Message) -> bool:
        """
        Hapus pesan dengan error handling
        """
        try:
            await message.delete()
            return True
        except Exception:
            # Silent error - tidak log
            return False
    

    
    async def handle_new_cv_message(self, message: Message) -> bool:
        """
        Handle pesan CV baru (real-time) dengan enhanced detection
        """
        try:
            # Cek apakah user adalah admin
            if is_admin_by_id(message.from_user.id):
                return False  # Admin boleh kirim CV

            # Enhanced CV detection dengan comprehensive text extraction
            enhanced_text = extract_clean_text_from_message(message)
            has_cv = self.contains_cv_word_enhanced(enhanced_text) if enhanced_text else False

            if has_cv:
                await asyncio.sleep(DELETE_DELAY)
                success = await self.delete_message_safe(message)
                if success:
                    self.deleted_count += 1
                return success

            return False

        except Exception:
            # Silent error
            return False

# Global instance
cv_deleter = CVMessageDeleter()

# CV monitoring sudah diintegrasikan ke anti-link handler

def get_cv_delete_stats() -> dict:
    """
    Get statistik delete CV
    """
    return {
        "deleted_count": cv_deleter.deleted_count,
        "is_cleanup_running": cv_deleter.is_cleanup_running
    }
