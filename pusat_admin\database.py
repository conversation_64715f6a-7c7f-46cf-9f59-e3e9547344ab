"""
Database functions untuk semua fitur admin
"""

import os
import logging
import time
from typing import List, Dict, Optional


class AdminDatabase:
    """Class untuk mengelola database admin"""
    
    def __init__(self, base_path="pusat_admin/database"):
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
    
    def _get_file_path(self, filename):
        """Get full path untuk file database"""
        return os.path.join(self.base_path, filename)
    
    def read_lines(self, filename) -> List[str]:
        """Baca semua baris dari file"""
        file_path = self._get_file_path(filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except FileNotFoundError:
            return []
        except Exception as e:
            logging.error(f"Error reading {filename}: {e}")
            return []
    
    def write_lines(self, filename, lines: List[str]) -> bool:
        """Tulis baris-baris ke file"""
        file_path = self._get_file_path(filename)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for line in lines:
                    f.write(line.strip() + '\n')
            return True
        except Exception as e:
            logging.error(f"Error writing {filename}: {e}")
            return False
    
    def append_line(self, filename, line: str) -> bool:
        """Tambah baris ke file"""
        file_path = self._get_file_path(filename)
        try:
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(line.strip() + '\n')
            return True
        except Exception as e:
            logging.error(f"Error appending to {filename}: {e}")
            return False
    
    def read_dict_data(self, filename, separator='|') -> List[Dict]:
        """Baca data dalam format dictionary"""
        lines = self.read_lines(filename)
        data = []
        
        for line in lines:
            if separator in line:
                parts = line.split(separator)
                if len(parts) >= 2:
                    data.append({
                        'raw': line,
                        'parts': parts
                    })
        
        return data
    
    def find_by_field(self, filename, field_index: int, value: str, separator='|') -> Optional[Dict]:
        """Cari data berdasarkan field tertentu"""
        data = self.read_dict_data(filename, separator)
        
        for item in data:
            if len(item['parts']) > field_index:
                if item['parts'][field_index].lower() == value.lower():
                    return item
        
        return None
    
    def update_by_field(self, filename, field_index: int, old_value: str, new_line: str, separator='|') -> bool:
        """Update data berdasarkan field tertentu"""
        lines = self.read_lines(filename)
        updated_lines = []
        found = False
        
        for line in lines:
            if separator in line:
                parts = line.split(separator)
                if len(parts) > field_index and parts[field_index].lower() == old_value.lower():
                    updated_lines.append(new_line)
                    found = True
                else:
                    updated_lines.append(line)
            else:
                updated_lines.append(line)
        
        if found:
            return self.write_lines(filename, updated_lines)
        return False
    
    def delete_by_field(self, filename, field_index: int, value: str, separator='|') -> bool:
        """Hapus data berdasarkan field tertentu"""
        lines = self.read_lines(filename)
        filtered_lines = []
        found = False
        
        for line in lines:
            if separator in line:
                parts = line.split(separator)
                if len(parts) > field_index and parts[field_index].lower() == value.lower():
                    found = True
                    continue
            filtered_lines.append(line)
        
        if found:
            return self.write_lines(filename, filtered_lines)
        return False
    
    def clear_file(self, filename) -> bool:
        """Kosongkan file"""
        return self.write_lines(filename, [])
    
    def file_exists(self, filename) -> bool:
        """Cek apakah file ada"""
        return os.path.exists(self._get_file_path(filename))
    
    def get_file_size(self, filename) -> int:
        """Get ukuran file dalam bytes"""
        file_path = self._get_file_path(filename)
        try:
            return os.path.getsize(file_path)
        except:
            return 0
    
    def get_line_count(self, filename) -> int:
        """Get jumlah baris dalam file"""
        return len(self.read_lines(filename))


# Instance global database
db = AdminDatabase()


# Helper functions untuk backward compatibility
def read_file_lines(filename):
    """Baca baris dari file"""
    return db.read_lines(filename)


def write_file_lines(filename, lines):
    """Tulis baris ke file"""
    return db.write_lines(filename, lines)


def append_to_file(filename, line):
    """Tambah baris ke file"""
    return db.append_line(filename, line)


def get_timestamp():
    """Get current timestamp"""
    return str(int(time.time()))
