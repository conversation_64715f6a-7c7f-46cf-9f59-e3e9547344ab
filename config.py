import os
from dotenv import load_dotenv

load_dotenv()
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN")

# Link tutorial
TUTORIAL_LINK = os.getenv("TUTORIAL_LINK")

# Group & Channel Configuration
GROUP_ID = int(os.getenv("GROUP_ID") or "0")
CHANNEL_ID = int(os.getenv("CHANNEL_ID") or "0")
GROUP_LINK = os.getenv("GROUP_LINK") or ""
CHANNEL_LINK = os.getenv("CHANNEL_LINK") or ""

# Import admin configuration dari pusat admin
try:
    from pusat_admin.admin_config import SUPER_ADMIN_USERNAMES
except ImportError:
    # Fallback jika pusat admin belum tersedia
    SUPER_ADMIN_USERNAMES = os.getenv("SUPER_ADMIN_USERNAMES", "").split(",")
    SUPER_ADMIN_USERNAMES = [username.strip() for username in SUPER_ADMIN_USERNAMES if username.strip()]