#!/usr/bin/env python3
"""
Test Mutasi Payment API
Script untuk test langsung API payment dan cek mutasi
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class PaymentTester:
    def __init__(self):
        # Payment API Configuration
        self.token = "f0f7444a811741d9289ccbae9db9a481de0a1581a5b7d99dec93437549cf26c6"
        self.orkut_token = "2528455:NJ7MwxG2PgDurdWmoyYsl68Svk3qbaFc"
        self.orkut_username = "WARstoreUP"
        self.base_url = "https://api.wahdx.co"
        
        # Current session data
        self.current_merchant_id = None
        self.current_key = None
        self.key_expires_at = None
        
    async def refresh_api_key(self) -> bool:
        """
        Refresh API key dari /api/key-orkut
        WAJIB dipanggil sebelum cek mutasi
        """
        try:
            url = f"{self.base_url}/api/key-orkut"
            
            payload = {
                "username_orkut": self.orkut_username,
                "token_orkut": self.orkut_token
            }
            
            headers = {
                'Content-Type': 'application/json',
                'TokenKey': self.token
            }
            
            print(f"🔄 Refreshing API key...")
            print(f"URL: {url}")
            print(f"Payload: {json.dumps(payload, indent=2)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_text = await response.text()
                    
                    print(f"Status: {response.status}")
                    print(f"Response: {response_text}")
                    
                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            
                            if data.get('status') == 'success':
                                self.current_merchant_id = data.get('merchantId')
                                self.current_key = data.get('key')
                                self.key_expires_at = datetime.now().timestamp() + 50  # 50 detik buffer
                                
                                print(f"✅ Key refresh berhasil!")
                                print(f"Merchant ID: {self.current_merchant_id}")
                                print(f"Key: {self.current_key}")
                                return True
                            else:
                                print(f"❌ Key refresh gagal: {data}")
                                return False
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ Error parsing response: {e}")
                            return False
                    else:
                        print(f"❌ HTTP Error: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ Error refreshing key: {e}")
            return False
    
    async def check_mutasi(self) -> Optional[List[Dict]]:
        """
        Cek mutasi dari /api/mutasi-orkut
        """
        try:
            # Pastikan key sudah di-refresh
            if not self.current_merchant_id or not self.current_key:
                print("⚠️ Key belum di-refresh, melakukan refresh...")
                if not await self.refresh_api_key():
                    print("❌ Gagal refresh key")
                    return None
            
            url = f"{self.base_url}/api/mutasi-orkut"
            
            payload = {
                "merchantId": self.current_merchant_id,
                "key": self.current_key
            }
            
            headers = {
                'Content-Type': 'application/json',
                'TokenKey': self.token
            }
            
            print(f"\n🔍 Checking mutasi...")
            print(f"URL: {url}")
            print(f"Payload: {json.dumps(payload, indent=2)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    response_text = await response.text()
                    
                    print(f"Status: {response.status}")
                    print(f"Response: {response_text}")
                    
                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            
                            if data.get('status') == 'success':
                                mutations = data.get('data', [])
                                print(f"✅ Mutasi berhasil diambil!")
                                print(f"Jumlah mutasi: {len(mutations)}")
                                
                                if mutations:
                                    print("\n📋 Detail Mutasi:")
                                    for i, mutasi in enumerate(mutations, 1):
                                        print(f"\n--- Mutasi {i} ---")
                                        print(f"Amount: Rp {mutasi.get('amount', 'N/A')}")
                                        print(f"Brand: {mutasi.get('brand_name', 'N/A')}")
                                        print(f"Issuer Ref: {mutasi.get('issuer_reff', 'N/A')}")
                                        print(f"Buyer Ref: {mutasi.get('buyer_reff', 'N/A')}")
                                        print(f"Date: {mutasi.get('date', 'N/A')}")
                                else:
                                    print("ℹ️ Tidak ada mutasi baru")
                                
                                return mutations
                            else:
                                print(f"❌ Mutasi gagal: {data}")
                                return None
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ Error parsing response: {e}")
                            return None
                    else:
                        print(f"❌ HTTP Error: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ Error checking mutasi: {e}")
            return None
    
    async def test_full_flow(self):
        """
        Test full flow: refresh key + cek mutasi
        """
        print("🚀 Starting Payment API Test")
        print("=" * 50)
        
        # Step 1: Refresh key
        print("\n📝 Step 1: Refresh API Key")
        if await self.refresh_api_key():
            print("✅ Key refresh berhasil!")
        else:
            print("❌ Key refresh gagal, menghentikan test")
            return
        
        # Step 2: Check mutasi
        print("\n📝 Step 2: Check Mutasi")
        mutations = await self.check_mutasi()
        
        if mutations is not None:
            print(f"\n✅ Test selesai! Ditemukan {len(mutations)} mutasi")
        else:
            print("\n❌ Test gagal!")
        
        print("\n" + "=" * 50)

async def main():
    """Main function untuk menjalankan test"""
    tester = PaymentTester()
    
    print("🔧 Payment API Tester")
    print("Pilih opsi:")
    print("1. Test Refresh Key saja")
    print("2. Test Cek Mutasi saja")
    print("3. Test Full Flow (Refresh + Mutasi)")
    
    try:
        choice = input("\nPilih (1/2/3): ").strip()
        
        if choice == "1":
            await tester.refresh_api_key()
        elif choice == "2":
            await tester.check_mutasi()
        elif choice == "3":
            await tester.test_full_flow()
        else:
            print("❌ Pilihan tidak valid")
            
    except KeyboardInterrupt:
        print("\n👋 Test dibatalkan")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
