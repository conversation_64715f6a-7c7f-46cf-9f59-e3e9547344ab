"""
Trial Notification Service - Handle notifikasi trial untuk user
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Set
from aiogram import Bot

from .trial_service import TrialService



class TrialNotificationService:
    """Service untuk handle notifikasi trial"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        self.trial_service = TrialService()
        
        # Track user yang sudah di-notify untuk prevent spam
        self.startup_notified_users: Set[int] = set()
        self.expired_notified_users: Set[int] = set()
    
    async def send_startup_trial_notifications(self):
        """
        DISABLED: Trial notification ke eligible users saat bot startup - DISABLED
        """
        try:
            # Get eligible users dari database dengan filter yang benar
            eligible_users = self.trial_service.get_trial_eligible_users_from_total()

            if not eligible_users:
                # logging.error("No eligible users found for trial notifications")  # Disabled - tidak perlu log error
                return

            # DISABLED: Notification message tidak dikirim lagi
            # startup_message = """🎁 Akses Gratis <PERSON> 3 Jam! ⏰
            # 💎 Ketik /start untuk mengakses
            # ⚡ Jangan sampai terlewatkan! 🔥"""

            success_count = 0
            failed_count = 0

            for user_id in eligible_users:
                try:
                    # Skip jika sudah di-notify
                    if user_id in self.startup_notified_users:
                        continue

                    # DISABLED: Send notification - tidak mengirim pesan lagi
                    # await self.bot.send_message(
                    #     chat_id=user_id,
                    #     text=startup_message
                    # )

                    # Mark as notified (tetap track untuk konsistensi)
                    self.startup_notified_users.add(user_id)
                    success_count += 1

                    # Rate limiting untuk prevent spam detection (tetap ada untuk konsistensi)
                    await asyncio.sleep(0.1)

                except Exception as e:
                    failed_count += 1
                    logging.error(f"Failed to process startup trial notification for {user_id}: {e}")

            # logging.error(f"Startup trial notifications: {success_count} processed, {failed_count} failed")  # Disabled logging

        except Exception as e:
            logging.error(f"Critical error in startup trial notifications: {e}")
    
    async def send_trial_activated_notification(self, user_id: int, trial_info: dict):
        """
        Send notification saat trial berhasil diaktifkan - DISABLED
        """
        # DISABLED: Tidak kirim pesan trial activated
        pass
    
    async def send_trial_expired_notification(self, user_id: int):
        """
        CRITICAL: Send notification saat trial expired
        """
        try:
            # Skip jika sudah di-notify
            if user_id in self.expired_notified_users:
                return
            
            # Double check: pastikan trial benar-benar expired
            is_active, _ = self.trial_service.check_trial_status(user_id)
            if is_active:
                return  # Trial masih aktif, jangan kirim notification
            
            # Trial expired message
            expired_message = """⏰ Akses gratis Habis

💎 Ketik /start untuk upgrade premium."""
            
            await self.bot.send_message(
                chat_id=user_id,
                text=expired_message
            )
            
            # Mark as notified
            self.expired_notified_users.add(user_id)
            
        except Exception as e:
            # Silent handling untuk user yang block bot atau unreachable
            # logging.error(f"Failed to send trial expired notification to {user_id}: {e}")  # Disabled
            pass
    
    async def check_and_notify_expired_trials(self):
        """
        CRITICAL: Check trial expired dan kirim notifikasi
        """
        try:
            active_trials = self.trial_service.get_active_trial_users()
            
            from datetime import datetime, timezone
            current_utc = datetime.now(timezone.utc)
            
            for trial_data in active_trials:
                user_id = trial_data['user_id']
                expired_date_str = trial_data['expired_date']
                
                try:
                    # Parse expired date
                    expired_datetime = datetime.strptime(expired_date_str, "%Y-%m-%d %H:%M:%S")
                    # Make timezone aware (UTC)
                    expired_datetime = expired_datetime.replace(tzinfo=timezone.utc)

                    # Check if expired
                    if current_utc > expired_datetime:
                        # Trial expired
                        await self._handle_trial_expired(user_id)
                        
                except Exception as e:
                    # Silent handling untuk error processing trial expiry
                    # logging.error(f"Error processing trial expiry for user {user_id}: {e}")  # Disabled
                    pass

        except Exception as e:
            # Silent handling untuk error checking expired trials
            # logging.error(f"Error checking expired trials: {e}")  # Disabled
            pass
    
    async def _handle_trial_expired(self, user_id: int):
        """
        Handle trial expired dengan Grace Period
        """
        try:
            # Update status to expired
            self.trial_service._update_trial_status(user_id, "expired")
            
            # Check Grace Period
            from .premium_expiry_checker import get_expiry_checker
            checker = get_expiry_checker()
            
            if checker and user_id in checker.active_processes:
                # User lagi proses - mark untuk notify nanti
                checker.active_processes[user_id]['notify_on_complete'] = True
                return
            
            # User tidak ada proses aktif - kirim notifikasi langsung
            await self.send_trial_expired_notification(user_id)
            
        except Exception as e:
            # Silent handling untuk error saat handle trial expired
            # logging.error(f"Error handling trial expired for user {user_id}: {e}")  # Disabled
            pass
    
    def reset_notification_flags(self):
        """
        Reset notification flags untuk prevent memory leaks
        """
        try:
            # Clear jika terlalu banyak
            if len(self.startup_notified_users) > 10000:
                self.startup_notified_users.clear()
            
            if len(self.expired_notified_users) > 10000:
                self.expired_notified_users.clear()
                
        except Exception as e:
            logging.error(f"Error resetting notification flags: {e}")


# Global instance
trial_notification_service = None


def get_trial_notification_service() -> TrialNotificationService:
    """Get global trial notification service instance"""
    return trial_notification_service


def init_trial_notification_service(bot: Bot) -> TrialNotificationService:
    """Initialize global trial notification service"""
    global trial_notification_service
    trial_notification_service = TrialNotificationService(bot)
    return trial_notification_service
