"""
Payment History Module - Riwayat Pembayaran untuk Admin
"""

import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_payment_history_keyboard, create_back_keyboard
from .utils import log_bot, safe_edit_message, safe_answer_callback, format_rupiah
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()

# Import payment history service
try:
    from payment.payment_history_service import PaymentHistoryService
except ImportError:
    PaymentHistoryService = None
    # Silent import - tidak perlu warning


def escape_html(text: str) -> str:
    """Escape HTML special characters"""
    if not text:
        return ""
    return (text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace('"', "&quot;")
                .replace("'", "&#x27;"))


def format_payment_history_text(data: dict) -> str:
    """Format text untuk tampilan payment history"""
    histories = data.get('histories', [])
    current_page = data.get('current_page', 1)
    total_pages = data.get('total_pages', 0)
    total_count = data.get('total_count', 0)

    if not histories:
        return """📋 RIWAYAT ID PAY

❌ Tidak ada data ID PAY

[🔙 Kembali]"""

    # Header
    text = f"""📋 RIWAYAT ID PAY (Hal {current_page}/{total_pages})

💰 Total: {total_count} ID PAY

"""

    # List payments
    start_number = (current_page - 1) * 100 + 1
    for i, history in enumerate(histories):
        number = start_number + i
        payment_id = history.get('payment_id', 'N/A')
        username = history.get('username', '')
        full_name = history.get('full_name', 'Unknown User')
        amount = history.get('amount', 0)
        status = history.get('status', 'unknown')

        # Format status emoji
        status_emoji = {
            'pending': '⏳',     # Menunggu pembayaran (< 5 menit)
            'completed': '✅',   # Berhasil dibayar
            'failed': '❌',      # Gagal diproses (error)
            'expired': '❌'      # Timeout > 5 menit (sama dengan gagal)
        }.get(status.lower(), '❓')

        # Format user display - prioritas username, fallback ke nama dengan escape HTML
        user_display = escape_html(username) if username else escape_html(full_name)

        # Format compact: nomor. emoji <code>payment_id</code> amount user
        text += f"{number}. {status_emoji} <code>{payment_id}</code> {format_rupiah(amount)} {user_display}\n"

    return text.strip()


@router.callback_query(F.data == "payment_history")
async def show_payment_history(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan riwayat pembayaran halaman pertama"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions.get('can_view_files', True):
        await callback.answer("❌ Anda tidak memiliki izin melihat riwayat pembayaran", show_alert=True)
        return
    
    if not PaymentHistoryService:
        await callback.answer("❌ Service riwayat pembayaran tidak tersedia", show_alert=True)
        return
    
    try:
        # Get first page
        history_service = PaymentHistoryService()
        data = history_service.get_payment_histories_paginated(page=1, per_page=100)
        
        # Format text
        history_text = format_payment_history_text(data)
        
        # Create keyboard
        keyboard = create_payment_history_keyboard(
            current_page=data['current_page'],
            total_pages=data['total_pages'],
            has_prev=data['has_prev'],
            has_next=data['has_next']
        )
        
        await safe_edit_message(callback, history_text, keyboard, parse_mode="HTML")
        await safe_answer_callback(callback)

        log_admin_activity(username, "Lihat riwayat pembayaran")
        log_bot(f"Payment history viewed: Page 1/{data['total_pages']}, Total: {data['total_count']}")
        
    except Exception as e:
        logging.error(f"Error showing payment history: {e}")
        error_text = "❌ Gagal memuat riwayat pembayaran"
        await safe_edit_message(callback, error_text, create_back_keyboard())
        await safe_answer_callback(callback)


@router.callback_query(F.data.startswith("payment_history_page:"))
async def show_payment_history_page(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan halaman tertentu dari riwayat pembayaran"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    if not PaymentHistoryService:
        await callback.answer("❌ Service riwayat pembayaran tidak tersedia", show_alert=True)
        return
    
    try:
        # Extract page number
        page = int(callback.data.split(":", 1)[1])
        
        # Get data for the page
        history_service = PaymentHistoryService()
        data = history_service.get_payment_histories_paginated(page=page, per_page=100)
        
        # Format text
        history_text = format_payment_history_text(data)
        
        # Create keyboard
        keyboard = create_payment_history_keyboard(
            current_page=data['current_page'],
            total_pages=data['total_pages'],
            has_prev=data['has_prev'],
            has_next=data['has_next']
        )
        
        await safe_edit_message(callback, history_text, keyboard, parse_mode="HTML")
        await safe_answer_callback(callback)

        log_admin_activity(username, f"Lihat riwayat pembayaran halaman {page}")
        
    except (ValueError, IndexError):
        await callback.answer("❌ Halaman tidak valid", show_alert=True)
    except Exception as e:
        logging.error(f"Error showing payment history page: {e}")
        await callback.answer("❌ Gagal memuat halaman", show_alert=True)


@router.callback_query(F.data == "payment_history_refresh")
async def refresh_payment_history(callback: types.CallbackQuery, state: FSMContext):
    """Refresh riwayat pembayaran"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    if not PaymentHistoryService:
        await callback.answer("❌ Service riwayat pembayaran tidak tersedia", show_alert=True)
        return
    
    try:
        # Get first page (refresh)
        history_service = PaymentHistoryService()
        data = history_service.get_payment_histories_paginated(page=1, per_page=100)
        
        # Format text
        history_text = format_payment_history_text(data)
        
        # Create keyboard
        keyboard = create_payment_history_keyboard(
            current_page=data['current_page'],
            total_pages=data['total_pages'],
            has_prev=data['has_prev'],
            has_next=data['has_next']
        )
        
        await safe_edit_message(callback, history_text, keyboard, parse_mode="HTML")
        await callback.answer("🔄 Data diperbarui", show_alert=False)
        
        log_admin_activity(username, "Refresh riwayat pembayaran")
        
    except Exception as e:
        logging.error(f"Error refreshing payment history: {e}")
        await callback.answer("❌ Gagal memperbarui data", show_alert=True)



