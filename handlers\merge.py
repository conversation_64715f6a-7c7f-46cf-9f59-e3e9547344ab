import logging
import os
import time
import aiofiles
import asyncio
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.retry_send import retry_send_document
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs
from management.membership import check_membership, send_membership_message, delete_join_message
from management.data_file import log_file_upload

router = Router()

class MergeStates(StatesGroup):
    waiting_files = State()
    waiting_done = State()
    waiting_filename = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_global(message: types.Message, state: FSMContext):
    await state.clear()
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_merge(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

async def merge_start(message: types.Message, state: FSMContext):

    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    bot_msg = "📎 Kirim file yang mau digabung.\nminimal 2 file, format sama."
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(MergeStates.waiting_files)
    await state.update_data(files=[], logs=[], file_error=False, ext=None)

@router.message(MergeStates.waiting_files, F.document, F.chat.type == "private")
async def merge_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    await log_file_upload(message)
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())
    data = await state.get_data()
    if data.get("file_error"):
        return

    # Cek format file pertama
    if data.get("ext") is None:
        await state.update_data(ext=ext)
    # Jika format file berikutnya beda, error
    elif ext != data.get("ext"):
        await state.update_data(files=[], logs=[], file_error=True, ext=None)
        bot_msg = "❌ Semua file harus format sama!\nUlangi dengan /merge"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 2 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done jika sudah."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(MergeStates.waiting_files, Command("done"), F.chat.type == "private")
async def merge_done(message: types.Message, state: FSMContext):
    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files or len(files) < 2:
        bot_msg = "⚠️ Minimal 2 file. Kirim file lagi."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)

    # Summary log untuk file reception
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk merge")

    bot_msg = "📝 Nama file hasil gabung:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(MergeStates.waiting_filename)

@router.message(MergeStates.waiting_filename, F.chat.type == "private")
async def merge_receive_filename(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    filename = message.text.strip()
    if not filename:
        bot_msg = "Nama file tidak boleh kosong. Coba lagi:"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    await state.update_data(filename=filename)
    await process_merge_with_isolation(message, state)

async def process_merge_with_isolation(message, state):
    """
    Wrapper function untuk process_merge dengan user isolation.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_merge dengan user isolation
    success, result = await with_user_isolation(user_id, process_merge, message, state)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)

async def process_merge(message, state):
    """
    Fungsi utama untuk memproses merge file.
    """
    data = await state.get_data()
    files = data.get("files", [])
    ext = data.get("ext")
    filename = data.get("filename", "merged")
    output_name = f"{filename}{ext}"
    output_path = get_user_file_path(message.from_user.id, output_name)
    file_paths_to_delete = []

    try:
        # Gabung file sesuai format
        if ext == ".vcf":
            # Gabung semua blok BEGIN:VCARD ... END:VCARD
            all_vcards = []
            for file_path, _, _ in files:
                async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                    content = await f.read()
                cards = content.split("BEGIN:VCARD")
                for card in cards:
                    card = card.strip()
                    if not card:
                        continue
                    if not card.startswith("BEGIN:VCARD"):
                        card = "BEGIN:VCARD\n" + card
                    all_vcards.append(card)
            merged_content = "\n".join(all_vcards)
            async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                await f.write(merged_content)
        elif ext in [".txt", ".csv"]:
            # Gabung semua baris
            all_lines = []
            for file_path, _, _ in files:
                async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                    lines = await f.readlines()
                all_lines.extend([line.rstrip("\n") for line in lines])
            async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                await f.write("\n".join(all_lines))
        elif ext in [".xlsx", ".xls"]:
            import pandas as pd
            from openpyxl import Workbook

            # Collect all data from all files into single column
            all_data = []

            for file_path, _, _ in files:
                try:
                    # Read Excel file
                    df = pd.read_excel(file_path)

                    # Extract all non-empty values from all columns
                    for column in df.columns:
                        column_data = df[column].dropna().astype(str).tolist()
                        # Filter out empty strings and whitespace
                        column_data = [item.strip() for item in column_data if item.strip()]
                        all_data.extend(column_data)

                except Exception as e:
                    logging.warning(f"Error reading Excel file {file_path}: {e}")
                    continue

            if not all_data:
                bot_msg = "❌ Tidak ada data yang bisa digabung dari file Excel."
                await message.answer(bot_msg)
                log_bot(bot_msg)
                await state.clear()
                return

            # Create new Excel with simple single column format
            wb = Workbook()
            ws = wb.active
            ws.title = "Merged Data"

            # Write all data to column A (vertical merge)
            for row, data_item in enumerate(all_data, 1):
                ws.cell(row=row, column=1, value=data_item)

            # Set column width
            ws.column_dimensions['A'].width = 20

            # Save file
            wb.save(output_path)
        else:
            bot_msg = f"Format {ext} belum didukung untuk merge."
            await message.answer(bot_msg)
            log_bot(bot_msg)
            await state.clear()
            return

        await retry_send_document(message, output_path, output_name)
        log_bot(f"Berhasil kirim file gabungan: {output_name}")
        bot_msg = "✅ File gabungan sudah dikirim!"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        file_paths_to_delete.append(output_path)
    except Exception as e:
        err_msg = f"❌ Gagal gabung file. Ulangi dengan /merge\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        # Hapus file hasil setelah dikirim dengan safe cleanup
        if file_paths_to_delete:
            from utils.safe_file_cleanup import delayed_file_cleanup
            success_count, failed_count, failed_files = await delayed_file_cleanup(file_paths_to_delete, delay_before_cleanup=1.0)

            # Schedule background cleanup untuk file yang gagal
            if failed_count > 0:
                from utils.safe_file_cleanup import schedule_background_cleanup
                failed_paths = [path for path in file_paths_to_delete if os.path.basename(path) in failed_files]
                schedule_background_cleanup(failed_paths, delay_minutes=2)
        await state.clear()