"""
User Aktif Premium - Menampilkan user yang aktif di grup & channel DAN sudah bayar premium
Filter: membership + premium status + admin/super admin
Pagination 20 per halaman dengan real-time data
"""

import logging
import time
import asyncio
from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_pagination_keyboard, create_back_keyboard
from .utils import log_bot, format_time_ago, clean_text_for_markdown
from .database import db
from .auth_admin import is_admin_by_id, log_admin_activity, get_admin_permissions

router = Router()

USERS_PER_PAGE = 50  # Ubah dari 20 ke 50 sesuai preferensi user
TOTAL_USERS_PER_PAGE = 100  # Jumlah user per halaman untuk total users


def format_time_ago_realtime(timestamp_str):
    """Format timestamp menjadi 'X waktu yang lalu' - REAL-TIME CALCULATION"""
    try:
        if not timestamp_str or not str(timestamp_str).strip():
            return "baru saja"

        # Bersihkan dan validasi timestamp
        timestamp_clean = ''.join(filter(str.isdigit, str(timestamp_str).strip()))

        if not timestamp_clean:
            return "baru saja"

        timestamp = int(timestamp_clean)
        current_time = int(time.time())  # SELALU AMBIL WAKTU SEKARANG

        # Validasi timestamp range (2020-2033)
        if not (1600000000 <= timestamp <= 2000000000):
            return "baru saja"

        diff = current_time - timestamp

        # Validasi timestamp tidak negatif dan tidak terlalu jauh di masa depan
        if diff < -86400:  # Lebih dari 1 hari di masa depan
            return "waktu tidak valid"
        elif diff < 0:
            return "baru saja"
        elif diff < 60:
            # Detik
            if diff <= 5:
                return "baru saja"
            else:
                return f"{diff} detik lalu"
        elif diff < 3600:
            # Menit
            minutes = diff // 60
            return f"{minutes} menit lalu"
        elif diff < 86400:
            # Jam
            hours = diff // 3600
            return f"{hours} jam lalu"
        else:
            # Hari, minggu, bulan, tahun - LEBIH DETAIL
            days = diff // 86400
            if days == 1:
                return "1 hari lalu"
            elif days < 7:
                return f"{days} hari lalu"
            elif days < 30:
                weeks = days // 7
                if weeks == 1:
                    return "1 minggu lalu"
                return f"{weeks} minggu lalu"
            elif days < 365:
                months = days // 30
                if months == 1:
                    return "1 bulan lalu"
                return f"{months} bulan lalu"
            else:
                years = days // 365
                if years == 1:
                    return "1 tahun lalu"
                return f"{years} tahun lalu"
    except (ValueError, TypeError, OverflowError) as e:
        logging.warning(f"Error formatting timestamp '{timestamp_str}': {e}")
        return "baru saja"


def update_user_timestamp_realtime(user_id: str, username: str = "", first_name: str = "", last_name: str = ""):
    """Update timestamp user ke waktu sekarang saat mereka interact dengan bot"""
    try:
        current_time = str(int(time.time()))

        # Format data user dengan timestamp terbaru
        user_data = f"{user_id}|{username}|{first_name}|{last_name}|{current_time}"

        # Update di semua database yang relevan
        databases_to_update = [
            "active_users_cache.txt",
            "total_users.txt",
            "user_list.txt",
            "user_search_index.txt",
            "broadcast_users.txt",
            "targeted_users.txt"
        ]

        for db_file in databases_to_update:
            try:
                # Cek apakah user sudah ada
                existing = db.find_by_field(db_file, 0, user_id)
                if existing:
                    # Update existing user dengan timestamp baru
                    db.update_by_field(db_file, 0, user_id, user_data)
                else:
                    # Tambah user baru (untuk database yang belum ada user ini)
                    db.append_line(db_file, user_data)
            except Exception as e:
                logging.warning(f"Error updating timestamp in {db_file}: {e}")
                continue

        # logging.info(f"✅ Updated timestamp for user {user_id} ({username}) to real-time")  # Disabled untuk mengurangi noise
        return True

    except Exception as e:
        logging.error(f"Error updating user timestamp: {e}")
        return False


def is_user_premium_or_admin(user_id: str, username: str = "") -> bool:
    """Cek apakah user adalah premium atau admin/super admin"""
    try:
        # Cek admin/super admin
        from .auth_admin import is_admin, is_super_admin, get_admin_level_by_id

        if username and (is_admin(username) or is_super_admin(username)):
            return True

        # Fallback cek berdasarkan user ID
        admin_level = get_admin_level_by_id(int(user_id))
        if admin_level in ["admin", "super_admin"]:
            return True

        # Cek premium status
        try:
            from payment.premium_service import PremiumService
            premium_service = PremiumService()
            is_premium, _ = premium_service.check_premium_status(int(user_id))
            return is_premium
        except ImportError:
            # Silent import - tidak perlu warning
            return False

    except Exception as e:
        logging.error(f"Error checking premium/admin status for user {user_id}: {e}")
        return False


def get_active_users():
    """Ambil user yang AKTIF di grup DAN channel DAN sudah bayar premium (atau admin)"""
    try:
        users = []
        current_time = int(time.time())

        # Gunakan active_users_cache.txt yang berisi data user aktif real-time
        user_data = db.read_dict_data("active_users_cache.txt")

        # Jika cache kosong, ambil dari total_users.txt sebagai fallback
        if not user_data:
            user_data = db.read_dict_data("total_users.txt")

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5:
                try:
                    user_id = str(parts[0]).strip()
                    username = str(parts[1] if len(parts) > 1 else "").strip()
                    first_name = str(parts[2] if len(parts) > 2 else "").strip()
                    last_name = str(parts[3] if len(parts) > 3 else "").strip()
                    timestamp = str(parts[4] if len(parts) > 4 else current_time).strip()
                    status = str(parts[5] if len(parts) > 5 else "").strip()

                    if not user_id.isdigit():
                        continue

                    # FILTER 1: Skip user yang diblokir
                    if status.lower() == "blocked":
                        continue

                    # FILTER 2: Hanya user yang premium atau admin
                    if not is_user_premium_or_admin(user_id, username):
                        continue

                    # Validasi timestamp dengan lebih ketat
                    try:
                        # Bersihkan timestamp dari karakter non-digit
                        timestamp_clean = ''.join(filter(str.isdigit, str(timestamp)))

                        if not timestamp_clean:
                            # Jika tidak ada digit sama sekali, set default
                            timestamp = str(current_time - 86400)
                        else:
                            timestamp_int = int(timestamp_clean)
                            # Pastikan timestamp dalam range yang masuk akal (2020-2033)
                            if not (1600000000 <= timestamp_int <= 2000000000):
                                timestamp = str(current_time - 86400)  # Default 1 hari lalu
                            else:
                                timestamp = str(timestamp_int)  # Use cleaned timestamp
                    except (ValueError, TypeError, OverflowError):
                        timestamp = str(current_time - 86400)

                    # Tambahkan user ke daftar aktif
                    # Pengecekan membership dilakukan saat refresh manual saja
                    users.append({
                        "id": user_id,
                        "username": username,
                        "first_name": first_name,
                        "last_name": last_name,
                        "timestamp": timestamp
                    })

                except Exception as e:
                    logging.warning(f"Error processing user from users.txt: {parts} - {e}")
                    continue

        # Sort berdasarkan timestamp terbaru (real-time) dengan validasi
        def safe_timestamp_sort(user):
            try:
                timestamp = user.get("timestamp", "")
                if not timestamp or not str(timestamp).strip():
                    return 0  # Default timestamp untuk data kosong
                return int(str(timestamp).strip())
            except (ValueError, TypeError):
                return 0  # Default timestamp untuk data invalid

        users.sort(key=safe_timestamp_sort, reverse=True)

        # logging.info(f"Loaded {len(users)} users from users.txt with REAL-TIME timestamps")  # Disabled untuk mengurangi noise
        return users

    except Exception as e:
        logging.error(f"Error getting users: {e}")
        return []


def get_active_users_with_cleanup():
    """Ambil user aktif dengan cleanup berdasarkan timestamp - HANYA UNTUK REFRESH MANUAL"""
    try:
        users = []
        current_time = int(time.time())
        removed_count = 0

        # logging.info("Performing cleanup for active users (removing inactive >24 hours)")  # Disabled untuk mengurangi noise
        user_data = db.read_dict_data("active_users_cache.txt")

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5:
                try:
                    user_id = str(parts[0]).strip()
                    username = str(parts[1] if len(parts) > 1 else "").strip()
                    first_name = str(parts[2] if len(parts) > 2 else "").strip()
                    last_name = str(parts[3] if len(parts) > 3 else "").strip()
                    timestamp = str(parts[4] if len(parts) > 4 else current_time).strip()
                    status = str(parts[5] if len(parts) > 5 else "").strip()

                    if not user_id.isdigit():
                        continue

                    # FILTER 1: Skip user yang diblokir
                    if status.lower() == "blocked":
                        continue

                    # FILTER 2: Hanya user yang premium atau admin
                    if not is_user_premium_or_admin(user_id, username):
                        continue

                    # Validasi timestamp
                    try:
                        timestamp_int = int(timestamp)
                        if not (1600000000 <= timestamp_int <= 2000000000):
                            timestamp = str(current_time - 86400)
                    except (ValueError, TypeError):
                        timestamp = str(current_time - 86400)

                    # TIMESTAMP-BASED CLEANUP - Lebih reliable daripada API check
                    try:
                        user_timestamp = int(timestamp)
                        time_diff = current_time - user_timestamp

                        # Hanya hapus user yang tidak aktif lebih dari 24 jam
                        if time_diff > (24 * 60 * 60):  # 24 jam
                            # logging.info(f"🔴 User {user_id} ({username}) inactive for {time_diff//3600} hours - removing from cache")  # Disabled untuk mengurangi noise
                            db.delete_by_field("active_users_cache.txt", 0, user_id)
                            removed_count += 1
                        else:
                            # User masih aktif dalam 24 jam terakhir
                            users.append({
                                "id": user_id,
                                "username": username,
                                "first_name": first_name,
                                "last_name": last_name,
                                "timestamp": timestamp
                            })
                            hours_ago = time_diff // 3600
                            minutes_ago = (time_diff % 3600) // 60
                            if hours_ago > 0:
                                # logging.info(f"✅ User {user_id} ({username}) active {hours_ago}h {minutes_ago}m ago - keeping")  # Disabled untuk mengurangi noise
                                pass
                            else:
                                # logging.info(f"✅ User {user_id} ({username}) active {minutes_ago}m ago - keeping")  # Disabled untuk mengurangi noise
                                pass

                    except (ValueError, TypeError):
                        # Jika timestamp error, tetap tampilkan user
                        users.append({
                            "id": user_id,
                            "username": username,
                            "first_name": first_name,
                            "last_name": last_name,
                            "timestamp": timestamp
                        })
                        # logging.info(f"🟡 User {user_id} ({username}) kept due to invalid timestamp")  # Disabled untuk mengurangi noise

                except Exception as e:
                    logging.warning(f"Error processing user from active cache: {parts} - {e}")
                    continue

        # Sort berdasarkan timestamp terbaru dengan validasi
        def safe_timestamp_sort(user):
            try:
                timestamp = user.get("timestamp", "")
                if not timestamp or not str(timestamp).strip():
                    return 0  # Default timestamp untuk data kosong
                return int(str(timestamp).strip())
            except (ValueError, TypeError):
                return 0  # Default timestamp untuk data invalid

        users.sort(key=safe_timestamp_sort, reverse=True)

        # logging.info(f"Timestamp cleanup completed: {len(users)} active users, {removed_count} removed")  # Disabled untuk mengurangi noise
        return users

    except Exception as e:
        logging.error(f"Error getting active users with membership check: {e}")
        return get_active_users()  # Fallback ke fungsi normal


async def get_active_users_with_realtime_check(bot, progress_callback=None):
    """Ambil user aktif dengan pengecekan membership REAL-TIME - UNTUK CEK MEMBERSHIP MANUAL"""
    try:
        from .realtime_membership import filter_active_users_realtime

        # Ambil semua user dari cache
        user_data = db.read_dict_data("active_users_cache.txt")
        user_list = []

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5:
                user_id, username, first_name, last_name, timestamp = parts[:5]
                status = str(parts[5] if len(parts) > 5 else "").strip()

                # FILTER 1: Skip user yang diblokir
                if status.lower() == "blocked":
                    continue

                # FILTER 2: Hanya user yang premium atau admin
                if not is_user_premium_or_admin(user_id, username):
                    continue

                user_list.append({
                    "id": user_id,
                    "username": username,
                    "first_name": first_name,
                    "last_name": last_name,
                    "timestamp": timestamp
                })

        # Filter user yang benar-benar aktif di grup DAN channel dengan progress callback
        active_users = await filter_active_users_realtime(bot, user_list, progress_callback)

        # Update cache - hapus user yang tidak aktif
        removed_count = len(user_list) - len(active_users)
        if removed_count > 0:
            # Ambil ID user yang masih aktif
            active_user_ids = {user["id"] for user in active_users}

            # Hapus user yang tidak aktif dari cache
            for user in user_list:
                if user["id"] not in active_user_ids:
                    db.delete_by_field("active_users_cache.txt", 0, user["id"])

        # Sort berdasarkan timestamp terbaru dengan validasi
        def safe_timestamp_sort(user):
            try:
                timestamp = user.get("timestamp", "")
                if not timestamp or not str(timestamp).strip():
                    return 0  # Default timestamp untuk data kosong
                return int(str(timestamp).strip())
            except (ValueError, TypeError):
                return 0  # Default timestamp untuk data invalid

        active_users.sort(key=safe_timestamp_sort, reverse=True)

        return active_users

    except Exception as e:
        logging.error(f"Error getting active users with real-time check: {e}")
        return get_active_users()  # Fallback ke fungsi normal


def get_total_users():
    """Ambil SEMUA user yang pernah akses bot dari total_users.txt (termasuk yang diblokir)"""
    try:
        users = []

        # Ambil dari total_users.txt - SEMUA user yang pernah akses bot
        user_data = db.read_dict_data("total_users.txt")

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5:
                try:
                    user_id = str(parts[0]).strip()
                    username = str(parts[1] if len(parts) > 1 else "").strip()
                    first_name = str(parts[2] if len(parts) > 2 else "").strip()
                    last_name = str(parts[3] if len(parts) > 3 else "").strip()
                    timestamp = str(parts[4] if len(parts) > 4 else "").strip()
                    status = str(parts[5] if len(parts) > 5 else "").strip()

                    if not user_id.isdigit():
                        continue

                    # Tambahkan SEMUA user (termasuk yang diblokir)
                    users.append({
                        "id": user_id,
                        "username": username,
                        "first_name": first_name,
                        "last_name": last_name,
                        "timestamp": timestamp,
                        "is_blocked": status.lower() == "blocked"
                    })

                except Exception as e:
                    logging.warning(f"Error processing total user: {parts} - {e}")
                    continue

        # Sort berdasarkan timestamp terbaru
        def safe_timestamp_sort(user):
            try:
                timestamp = user.get("timestamp", "")
                if not timestamp or not str(timestamp).strip():
                    return 0
                return int(str(timestamp).strip())
            except (ValueError, TypeError):
                return 0

        users.sort(key=safe_timestamp_sort, reverse=True)
        return users

    except Exception as e:
        logging.error(f"Error getting total users: {e}")
        return []


def format_total_users_page(users, page, total_pages):
    """Format daftar total user untuk satu halaman"""
    try:
        if not users:
            return "👥 <b>DAFTAR USER</b>\n\n❌ Tidak ada user terdaftar"

        start_idx = (page - 1) * TOTAL_USERS_PER_PAGE
        end_idx = start_idx + TOTAL_USERS_PER_PAGE
        page_users = users[start_idx:end_idx]

        # Hitung statistik
        total_users = len(users)
        blocked_count = sum(1 for user in users if user.get('is_blocked', False))

        # Header
        text = f"👥 <b>DAFTAR USER</b>\n"
        text += f"📊 {total_users} user terdaftar\n"
        text += f"🚫 {blocked_count} user diblokir\n"
        text += f"📄 {page}/{total_pages}\n\n"

        for i, user in enumerate(page_users, start=start_idx + 1):
            try:
                # Ambil data user
                username = user.get('username', '').strip()
                first_name = user.get('first_name', '').strip()
                last_name = user.get('last_name', '').strip()
                user_id = user.get('id', '').strip()
                is_blocked = user.get('is_blocked', False)

                # Prioritas tampilan: username → nama pengguna → ID
                display_name = ""

                if username:
                    # Ada username, tampilkan dengan @
                    display_name = f"@{username}"
                    # Clean username
                    cleaned_display = clean_text_for_markdown(display_name)
                    if cleaned_display:
                        display_name = cleaned_display
                    # Jika cleaning gagal, tetap pakai username asli dengan @
                elif first_name or last_name:
                    # Tidak ada username, gunakan nama pengguna
                    full_name = f"{first_name} {last_name}".strip()
                    # Clean nama pengguna
                    cleaned_name = clean_text_for_markdown(full_name)
                    if cleaned_name:
                        # Check apakah nama informatif
                        meaningful_chars = cleaned_name.replace('-', '').replace(' ', '').replace('_', '').strip()
                        # Nama informatif jika:
                        # 1. Minimal 3 karakter bermakna
                        # 2. Tidak dimulai dengan angka (seperti "-48 murid...")
                        # 3. Mengandung huruf (bukan hanya angka dan simbol)
                        has_letters = any(c.isalpha() for c in meaningful_chars)
                        starts_with_number = cleaned_name.strip().lstrip('-').lstrip(' ')[0:1].isdigit() if cleaned_name.strip().lstrip('-').lstrip(' ') else False

                        if len(meaningful_chars) >= 3 and has_letters and not starts_with_number:
                            # Nama hasil cleaning cukup informatif
                            display_name = cleaned_name
                        else:
                            # Nama tidak informatif, fallback ke ID
                            display_name = f"ID: {user_id}"
                    else:
                        # Jika cleaning gagal (nama terlalu banyak Unicode), fallback ke ID
                        display_name = f"ID: {user_id}"
                elif user_id:
                    # Tidak ada username dan nama, gunakan ID
                    display_name = f"ID: {user_id}"
                else:
                    # Fallback jika semua kosong
                    display_name = "User Tanpa Data"

                # Escape HTML characters (sudah di-clean di atas)
                display_name = display_name.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                # Tambahkan emoji 🚫 di BELAKANG jika user diblokir
                if is_blocked:
                    display_name = f"{display_name} 🚫"

                text += f"{i}. {display_name}\n"

            except Exception as e:
                logging.warning(f"Error formatting total user {i}: {e}")
                text += f"{i}. User data error\n"

        return text

    except Exception as e:
        logging.error(f"Error formatting total users page: {e}")
        return "❌ Error loading users"



def format_active_users_page(users, page, total_pages, include_timestamp=False):
    """Format daftar semua user untuk satu halaman - PAGINATION"""
    try:
        if not users:
            return "📊 <b>USER AKTIF PREMIUM</b>\n\n❌ Tidak ada user aktif yang sudah bayar premium"

        start_idx = (page - 1) * USERS_PER_PAGE
        end_idx = start_idx + USERS_PER_PAGE
        page_users = users[start_idx:end_idx]

        # Header dengan total dan halaman
        text = f"📊 <b>USER AKTIF PREMIUM</b>\n"
        text += f"🟢 {len(users)} user aktif\n"
        text += f"📄 {page}/{total_pages}\n"

        # Tambahkan timestamp untuk refresh agar konten berbeda
        if include_timestamp:
            current_time = int(time.time())
            text += f"🕐 Update: {current_time}\n"

        text += "\n"

        for i, user in enumerate(page_users, start=start_idx + 1):
            try:
                # Ambil data user
                username = user.get('username', '').strip()
                first_name = user.get('first_name', '').strip()
                last_name = user.get('last_name', '').strip()
                user_id = user.get('id', '').strip()

                # Prioritas tampilan: username → nama pengguna → ID
                display_name = ""

                if username:
                    # Ada username, tampilkan dengan @
                    display_name = f"@{username}"
                    # Clean username
                    cleaned_display = clean_text_for_markdown(display_name)
                    if cleaned_display:
                        display_name = cleaned_display
                    # Jika cleaning gagal, tetap pakai username asli dengan @
                elif first_name or last_name:
                    # Tidak ada username, gunakan nama pengguna
                    full_name = f"{first_name} {last_name}".strip()
                    # Clean nama pengguna
                    cleaned_name = clean_text_for_markdown(full_name)
                    if cleaned_name:
                        # Check apakah nama informatif
                        meaningful_chars = cleaned_name.replace('-', '').replace(' ', '').replace('_', '').strip()
                        # Nama informatif jika:
                        # 1. Minimal 3 karakter bermakna
                        # 2. Tidak dimulai dengan angka (seperti "-48 murid...")
                        # 3. Mengandung huruf (bukan hanya angka dan simbol)
                        has_letters = any(c.isalpha() for c in meaningful_chars)
                        starts_with_number = cleaned_name.strip().lstrip('-').lstrip(' ')[0:1].isdigit() if cleaned_name.strip().lstrip('-').lstrip(' ') else False

                        if len(meaningful_chars) >= 3 and has_letters and not starts_with_number:
                            # Nama hasil cleaning cukup informatif
                            display_name = cleaned_name
                        else:
                            # Nama tidak informatif, fallback ke ID
                            display_name = f"ID: {user_id}"
                    else:
                        # Jika cleaning gagal (nama terlalu banyak Unicode), fallback ke ID
                        display_name = f"ID: {user_id}"
                elif user_id:
                    # Tidak ada username dan nama, gunakan ID
                    display_name = f"ID: {user_id}"
                else:
                    # Fallback jika semua kosong
                    display_name = "User Tanpa Data"

                # Format time dengan detail realtime - HITUNG ULANG SETIAP KALI
                timestamp = user.get('timestamp', '')
                time_ago = format_time_ago_realtime(timestamp)

                # Escape HTML characters untuk display_name dan time (sudah di-clean di atas)
                display_name = display_name.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                time_ago = time_ago.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                text += f"{i}. {display_name} - {time_ago}\n"

            except Exception as e:
                logging.warning(f"Error formatting active user {i}: {e}")
                text += f"{i}. User data error - baru saja\n"

        return text

    except Exception as e:
        logging.error(f"Error formatting active users page: {e}")
        return "❌ Error loading user"


@router.callback_query(F.data == "user_aktif")
async def show_active_users(callback: CallbackQuery, state: FSMContext):
    """Tampilkan semua user halaman pertama"""
    try:
        # Debug logging
        # logging.info(f"User {callback.from_user.id} ({callback.from_user.username}) accessing user_aktif")  # Disabled untuk mengurangi noise

        if not is_admin_by_id(callback.from_user.id):
            await callback.answer("❌ Akses ditolak", show_alert=True)
            return

        # ✅ IMMEDIATE RESPONSE - Langsung tampilkan pesan loading
        loading_text = "🔍 <b>Mengecek user...</b>\n\n⏳ Memuat user aktif..."

        # Keyboard loading (TANPA tombol kembali)
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        loading_keyboard = InlineKeyboardMarkup(inline_keyboard=[])

        # Edit message langsung tanpa delay
        await callback.message.edit_text(loading_text, reply_markup=loading_keyboard, parse_mode="HTML")
        await callback.answer()  # Answer callback immediately

        # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
        try:
            from .data_sync import sync_active_user

            user_id = str(callback.from_user.id)
            username_data = callback.from_user.username or ''
            first_name = callback.from_user.first_name or ''
            last_name = callback.from_user.last_name or ''
            timestamp = str(int(time.time()))

            # Update timestamp admin ke active users
            sync_active_user(user_id, username_data, first_name, last_name, timestamp)
        except Exception as e:
            logging.warning(f"Error updating admin timestamp: {e}")

        # 🔄 BACKGROUND PROCESSING - Ambil user dari cache (CEPAT)
        users = get_active_users()

        # Cache hasil di FSM state untuk pagination cepat
        await state.update_data(cached_users=users, current_page=1)

        total_pages = (len(users) + USERS_PER_PAGE - 1) // USERS_PER_PAGE if users else 1

        text = format_active_users_page(users, 1, total_pages)

        # Keyboard dengan pagination dan refresh
        keyboard_buttons = []

        # Navigation buttons
        nav_buttons = []
        if 1 > 1:  # current_page > 1
            nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"active_user_page_{1-1}"))

        nav_buttons.append(InlineKeyboardButton(text=f"1/{total_pages}", callback_data="noop"))

        if 1 < total_pages:  # current_page < total_pages
            nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"active_user_page_{1+1}"))

        if nav_buttons:
            keyboard_buttons.append(nav_buttons)

        # Refresh (otomatis real-time) and back buttons
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔄 Refresh", callback_data="refresh_active_users"),
            InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        # Update message dengan hasil final
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await state.set_state(AdminStates.viewing_active_users)

        # Log activity
        log_admin_activity(callback.from_user.username or str(callback.from_user.id), f"VIEW_ALL_USERS - Page 1, Total: {len(users)}")

    except Exception as e:
        logging.error(f"Error showing active users: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        # Error handling
        try:
            error_text = "❌ <b>Error</b>\n\nTerjadi kesalahan saat memuat data user aktif."
            error_keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")]
            ])
            await callback.message.edit_text(error_text, reply_markup=error_keyboard, parse_mode="HTML")
        except Exception as edit_error:
            logging.error(f"Error editing message during error handling: {edit_error}")
            try:
                await callback.answer("❌ Terjadi error saat memuat data", show_alert=True)
            except Exception:
                pass


@router.callback_query(F.data.startswith("active_user_page_"))
async def handle_active_user_pagination(callback: CallbackQuery, state: FSMContext):
    """Handle pagination untuk user aktif dengan cached data (INSTANT)"""
    if not is_admin_by_id(callback.from_user.id):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        page = int(callback.data.split("_")[-1])

        # ⚡ FAST PAGINATION - Gunakan cached data dari FSM state
        state_data = await state.get_data()
        cached_users = state_data.get('cached_users')

        if not cached_users:
            # Fallback jika tidak ada cache - redirect ke refresh
            await callback.answer("🔄 Data tidak tersedia, refresh dulu...", show_alert=True)
            return

        users = cached_users
        total_pages = (len(users) + USERS_PER_PAGE - 1) // USERS_PER_PAGE if users else 1

        if page < 1 or page > total_pages:
            await callback.answer("❌ Halaman invalid", show_alert=True)
            return

        # Update current page di state
        await state.update_data(current_page=page)

        text = format_active_users_page(users, page, total_pages)

        # Create keyboard manually
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        keyboard_buttons = []

        # Navigation buttons
        nav_buttons = []
        if page > 1:
            nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"active_user_page_{page-1}"))

        nav_buttons.append(InlineKeyboardButton(text=f"{page}/{total_pages}", callback_data="noop"))

        if page < total_pages:
            nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"active_user_page_{page+1}"))

        if nav_buttons:
            keyboard_buttons.append(nav_buttons)

        # Refresh (otomatis real-time) and back buttons
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔄 Refresh", callback_data="refresh_active_users"),
            InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        # ⚡ INSTANT UPDATE - Langsung edit message tanpa delay
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()  # Quick feedback

    except Exception as e:
        logging.error(f"Error handling active user pagination: {e}")
        try:
            await callback.answer("❌ Error navigasi", show_alert=True)
        except Exception:
            pass


@router.callback_query(F.data == "refresh_active_users")
async def refresh_active_users(callback: CallbackQuery, state: FSMContext):
    """Refresh data user aktif - OTOMATIS DENGAN REAL-TIME CHECK"""
    if not is_admin_by_id(callback.from_user.id):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin ke active users
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")

    # Langsung mulai progress real-time tanpa pesan "mohon tunggu"

    # Answer callback dengan error handling untuk expired query
    try:
        await callback.answer("🔄 Refresh + cek membership...", show_alert=False)
    except Exception as answer_error:
        # Jika query expired, abaikan error
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            # logging.info("Callback query expired at start of refresh, ignoring answer error")  # Disabled untuk mengurangi noise
            pass
        else:
            logging.warning(f"Error answering callback at start of refresh: {answer_error}")

    try:
        # Buat progress callback function
        async def progress_callback(current, total, percentage):
            try:
                progress_text = f"📊 <b>MENGECEK USER AKTIF</b>\n{percentage}% ({current}/{total} users)"
                await callback.message.edit_text(progress_text, parse_mode="HTML")
                # Small delay untuk rate limiting
                await asyncio.sleep(0.1)
            except Exception as e:
                # Jika edit gagal, skip saja
                pass

        # OTOMATIS: Gabungkan cleanup timestamp + real-time membership check dengan progress
        users = await get_active_users_with_realtime_check(callback.message.bot, progress_callback)

        # Cache hasil refresh untuk pagination cepat
        await state.update_data(cached_users=users, current_page=1)

        total_pages = (len(users) + USERS_PER_PAGE - 1) // USERS_PER_PAGE if users else 1

        text = format_active_users_page(users, 1, total_pages, include_timestamp=False)
        
        # Create keyboard manually
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        keyboard_buttons = []

        # Navigation buttons
        nav_buttons = []
        if 1 > 1:  # current_page > 1
            nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"active_user_page_{1-1}"))

        nav_buttons.append(InlineKeyboardButton(text=f"1/{total_pages}", callback_data="noop"))

        if 1 < total_pages:  # current_page < total_pages
            nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"active_user_page_{1+1}"))

        if nav_buttons:
            keyboard_buttons.append(nav_buttons)

        # Refresh (otomatis real-time) and back buttons
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔄 Refresh", callback_data="refresh_active_users"),
            InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        try:
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

            # Answer callback dengan error handling untuk expired query
            try:
                await callback.answer("✅ Updated", show_alert=False)
            except Exception as answer_error:
                # Jika query expired, abaikan error
                if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                    # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
                    pass
                else:
                    logging.warning(f"Error answering callback: {answer_error}")

        except Exception as edit_error:
            # Jika pesan tidak berubah, tetap beri feedback ke user
            if "message is not modified" in str(edit_error).lower():
                try:
                    await callback.answer("✅ Data sudah terbaru", show_alert=False)
                except Exception as answer_error:
                    # Jika query expired, abaikan error
                    if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                        # logging.info("Callback query expired, ignoring answer error")  # Disabled untuk mengurangi noise
                        pass
                    else:
                        logging.warning(f"Error answering callback: {answer_error}")
            else:
                raise edit_error
        
        # Log refresh activity
        log_admin_activity(callback.from_user.username or str(callback.from_user.id), f"AUTO_REFRESH_REALTIME - Total: {len(users)}")
        
    except Exception as e:
        logging.error(f"Error refreshing active users: {e}")

        # Answer callback dengan error handling untuk expired query
        try:
            await callback.answer("❌ Error refresh", show_alert=True)
        except Exception as answer_error:
            # Jika query expired, abaikan error
            if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                # logging.info("Callback query expired during error handling, ignoring answer error")  # Disabled untuk mengurangi noise
                pass
            else:
                logging.warning(f"Error answering callback during error handling: {answer_error}")


@router.callback_query(F.data == "total_users")
async def show_total_users(callback: CallbackQuery, state: FSMContext):
    """Tampilkan semua user yang pernah akses bot (halaman pertama)"""
    try:
        if not is_admin_by_id(callback.from_user.id):
            await callback.answer("❌ Akses ditolak", show_alert=True)
            return

        # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
        try:
            from .data_sync import sync_active_user

            user_id = str(callback.from_user.id)
            username_data = callback.from_user.username or ''
            first_name = callback.from_user.first_name or ''
            last_name = callback.from_user.last_name or ''
            timestamp = str(int(time.time()))

            # Update timestamp admin
            sync_active_user(user_id, username_data, first_name, last_name, timestamp)
        except Exception as e:
            logging.warning(f"Error updating admin timestamp: {e}")

        users = get_total_users()
        total_pages = (len(users) + TOTAL_USERS_PER_PAGE - 1) // TOTAL_USERS_PER_PAGE if users else 1

        text = format_total_users_page(users, 1, total_pages)

        # Keyboard dengan pagination dan refresh
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        keyboard_buttons = []

        # Navigation buttons
        nav_buttons = []
        if 1 > 1:  # current_page > 1
            nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"total_user_page_{1-1}"))

        nav_buttons.append(InlineKeyboardButton(text=f"1/{total_pages}", callback_data="noop"))

        if 1 < total_pages:  # current_page < total_pages
            nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"total_user_page_{1+1}"))

        if nav_buttons:
            keyboard_buttons.append(nav_buttons)

        # Refresh and back buttons
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔄 Refresh", callback_data="refresh_total_users"),
            InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        try:
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
            await state.set_state(AdminStates.viewing_total_users)
        except Exception as edit_error:
            if "message is not modified" in str(edit_error).lower():
                try:
                    await callback.answer("✅ Data sudah terbaru", show_alert=False)
                except Exception as answer_error:
                    if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                        pass
                    else:
                        logging.warning(f"Error answering callback: {answer_error}")
            else:
                raise edit_error

        # Log activity
        log_admin_activity(callback.from_user.username or str(callback.from_user.id), f"VIEW_TOTAL_USERS - Page 1, Total: {len(users)}")

    except Exception as e:
        logging.error(f"Error showing total users: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        try:
            await callback.answer("❌ Error loading", show_alert=True)
        except Exception as answer_error:
            if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                pass
            else:
                logging.warning(f"Error answering callback during error handling: {answer_error}")


@router.callback_query(F.data.startswith("total_user_page_"))
async def handle_total_user_pagination(callback: CallbackQuery, state: FSMContext):
    """Handle pagination untuk total users"""
    if not is_admin_by_id(callback.from_user.id):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        page = int(callback.data.split("_")[-1])
        users = get_total_users()
        total_pages = (len(users) + TOTAL_USERS_PER_PAGE - 1) // TOTAL_USERS_PER_PAGE if users else 1

        if page < 1 or page > total_pages:
            await callback.answer("❌ Halaman invalid", show_alert=True)
            return

        text = format_total_users_page(users, page, total_pages)

        # Create keyboard manually
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        keyboard_buttons = []

        # Navigation buttons
        nav_buttons = []
        if page > 1:
            nav_buttons.append(InlineKeyboardButton(text="⬅️", callback_data=f"total_user_page_{page-1}"))

        nav_buttons.append(InlineKeyboardButton(text=f"{page}/{total_pages}", callback_data="noop"))

        if page < total_pages:
            nav_buttons.append(InlineKeyboardButton(text="➡️", callback_data=f"total_user_page_{page+1}"))

        if nav_buttons:
            keyboard_buttons.append(nav_buttons)

        # Refresh and back buttons
        keyboard_buttons.append([
            InlineKeyboardButton(text="🔄 Refresh", callback_data="refresh_total_users"),
            InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_stats")
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        try:
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        except Exception as edit_error:
            if "message is not modified" in str(edit_error).lower():
                try:
                    await callback.answer("✅ Data sudah terbaru", show_alert=False)
                except Exception as answer_error:
                    if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                        pass
                    else:
                        logging.warning(f"Error answering callback: {answer_error}")
            else:
                raise edit_error

    except Exception as e:
        logging.error(f"Error handling total user pagination: {e}")

        try:
            await callback.answer("❌ Error navigasi", show_alert=True)
        except Exception as answer_error:
            if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
                pass
            else:
                logging.warning(f"Error answering callback during error handling: {answer_error}")


@router.callback_query(F.data == "refresh_total_users")
async def refresh_total_users(callback: CallbackQuery, state: FSMContext):
    """Refresh data total users"""
    if not is_admin_by_id(callback.from_user.id):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # UPDATE TIMESTAMP ADMIN SAAT AKTIVITAS DI PANEL
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")

    try:
        await callback.answer("🔄 Refresh data...", show_alert=False)
    except Exception as answer_error:
        if "query is too old" in str(answer_error).lower() or "query id is invalid" in str(answer_error).lower():
            pass
        else:
            logging.warning(f"Error answering callback: {answer_error}")

    # Redirect ke show_total_users
    await show_total_users(callback, state)




