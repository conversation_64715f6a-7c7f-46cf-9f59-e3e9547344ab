from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery, TelegramObject
from typing import Callable, Dict, Any, Awaitable

class GroupDetectionMiddleware(BaseMiddleware):
    """Middleware untuk mendeteksi penggunaan bot di grup dan memberikan pesan redirect"""

    def __init__(self):
        # Daftar semua perintah bot yang akan dideteksi
        self.bot_commands = {
            # Perintah utama
            '/start', '/help',

            # Perintah file processing
            '/to_vcf', '/to_txt', '/manual', '/add', '/delete',
            '/renamectc', '/renamefile', '/merge', '/split',
            '/count', '/nodup', '/getname', '/generate',

            # Perintah pengaturan
            '/hide_menu', '/cancel', '/setnametovcf',
            '/setsendtovcf', '/setsendtotxt',

            # Perintah admin
            '/admin', '/kazuhaid'
        }

        # Pesan yang akan dikirim ke user di grup
        self.group_message = (
            "🤖 Bot hanya bisa digunakan di chat bot\n\n"
            "💬 Silahkan chat bot di sini: @KazuhaCVBot"
        )
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        # Hanya proses untuk Message (tidak untuk CallbackQuery)
        if not isinstance(event, Message):
            return await handler(event, data)

        # Ambil informasi chat dan user
        chat_type = event.chat.type
        message_text = event.text or ""

        # Jika bukan grup/supergroup, lanjutkan normal
        if chat_type == "private":
            return await handler(event, data)

        # Jika di grup/supergroup, cek apakah ada perintah bot
        if chat_type in ["group", "supergroup"]:
            # Cek apakah pesan adalah perintah bot
            if message_text.startswith('/'):
                # Ambil command (tanpa parameter)
                full_command = message_text.split()[0].lower()

                # Cek apakah command ditujukan untuk bot ini
                should_process = False

                if '@' in full_command:
                    # Ada mention bot, cek apakah untuk bot ini
                    command_part, bot_mention = full_command.split('@', 1)

                    # Hanya proses jika mention bot ini (KazuhaCVBot)
                    if bot_mention.lower() == 'kazuhacvbot':
                        command = command_part
                        should_process = True
                else:
                    # Tidak ada mention, anggap untuk semua bot (behavior lama)
                    command = full_command
                    should_process = True

                # Cek apakah command ada dalam daftar bot commands
                if should_process and command in self.bot_commands:
                    try:
                        # Kirim pesan redirect sebagai reply ke pesan user
                        await event.reply(
                            self.group_message,
                            parse_mode=None
                        )
                    except Exception:
                        # Silent error handling
                        pass

                    # STOP - jangan lanjutkan ke handler (block command execution)
                    return

        # Lanjutkan ke handler berikutnya
        return await handler(event, data)
