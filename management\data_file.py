import os
import time
import aiofiles
import logging

USER_DATA_DIR = "management/user_data_file"

def get_user_identity(user):
    """
    Urutan prioritas untuk pengirim: username, nama pengguna, id, unknown
    """
    if hasattr(user, "username") and user.username:
        return f"@{user.username}"

    # Fallback ke nama pengguna (first_name + last_name)
    if hasattr(user, "first_name") or hasattr(user, "last_name"):
        first_name = getattr(user, "first_name", "") or ""
        last_name = getattr(user, "last_name", "") or ""
        full_name = f"{first_name} {last_name}".strip()

        if full_name:
            return full_name

    # Fallback ke ID (SELALU ADA untuk pengirim)
    if hasattr(user, "id") and user.id:
        return str(user.id)

    # Fallback terakhir (seharusnya tidak pernah terjadi untuk pengirim)
    return "unknown"


def get_forwarder_identity(user):
    """
    Urutan prioritas untuk penerus: username, nama pengguna, id, ------
    """
    if hasattr(user, "username") and user.username:
        return f"@{user.username}"

    # Fallback ke nama pengguna (first_name + last_name)
    if hasattr(user, "first_name") or hasattr(user, "last_name"):
        first_name = getattr(user, "first_name", "") or ""
        last_name = getattr(user, "last_name", "") or ""
        full_name = f"{first_name} {last_name}".strip()

        if full_name:
            return full_name

    # Fallback ke ID
    if hasattr(user, "id") and user.id:
        return str(user.id)

    # Fallback terakhir untuk penerus
    return "------"

def get_csv_filename_identity(user):
    """
    Urutan prioritas untuk nama file CSV: username, nama pengguna, id, unknown_user
    """
    if hasattr(user, "username") and user.username:
        return f"@{user.username}"

    # Fallback ke nama pengguna (first_name + last_name)
    if hasattr(user, "first_name") or hasattr(user, "last_name"):
        first_name = getattr(user, "first_name", "") or ""
        last_name = getattr(user, "last_name", "") or ""
        full_name = f"{first_name} {last_name}".strip()

        if full_name:
            # Replace spasi dengan underscore dan bersihkan karakter invalid untuk nama file
            clean_name = full_name.replace(" ", "_")
            # Hapus karakter yang tidak aman untuk nama file
            invalid_chars = '<>:"/\\|?*'
            for char in invalid_chars:
                clean_name = clean_name.replace(char, "")
            # Batasi panjang nama file
            if len(clean_name) > 50:
                clean_name = clean_name[:50]
            return clean_name

    # Fallback ke ID
    if hasattr(user, "id") and user.id:
        return str(user.id)

    # Fallback terakhir
    return "unknown_user"

async def log_file_upload(message):
    """
    Simpan log upload file ke management/user_data_file/{username}.csv
    Format: pengirim,penerus,nama file,tanggal dan Waktu
    """
    # Auto-create folder jika belum ada
    os.makedirs(USER_DATA_DIR, exist_ok=True)

    # Ambil identitas pengirim
    pengirim = get_user_identity(message.from_user)

    # Logika penerus yang diperbaiki
    penerus = "------"  # Default: tidak ada penerus

    # Cek apakah ada forward
    if getattr(message, "forward_from", None):
        # Ada forward dari user
        forward_user = message.forward_from

        # Cek apakah forward dari bot itu sendiri
        is_bot_forward = (hasattr(forward_user, "is_bot") and forward_user.is_bot and
                         hasattr(forward_user, "username") and forward_user.username == "KazuhaIDBot")

        if is_bot_forward:
            # Forward dari bot sendiri → penerus = "------"
            penerus = "------"
        else:
            # Forward dari user lain → gunakan prioritas penerus
            forward_identity = get_forwarder_identity(forward_user)

            # Jika penerus sama dengan pengirim → penerus = "------"
            if forward_identity == pengirim:
                penerus = "------"
            else:
                # Penerus berbeda dari pengirim
                penerus = forward_identity

    elif getattr(message, "forward_sender_name", None):
        # Ada forward tapi dari user yang hidden/private
        forward_name = message.forward_sender_name

        # Jika forward dari bot sendiri (berdasarkan nama), tetap "------"
        if forward_name == "KazuhaIDBot":
            penerus = "------"
        elif forward_name == pengirim:
            # Jika nama forward sama dengan pengirim, tetap "------"
            penerus = "------"
        else:
            # Jika nama forward berbeda dari pengirim dan bukan bot sendiri
            penerus = forward_name

    # Nama file
    nama_file = message.document.file_name if getattr(message, "document", None) else "------"

    # Waktu
    waktu = time.strftime("%d-%m %H:%M")

    # Nama file CSV menggunakan prioritas yang berbeda
    pengirim_csv = get_csv_filename_identity(message.from_user)
    csv_path = os.path.join(USER_DATA_DIR, f"{pengirim_csv}.csv")

    # Header dan data
    header = ["pengirim", "penerus", "nama file", "tanggal dan Waktu"]
    row = [pengirim, penerus, nama_file, waktu]

    # Perbaikan: Tulis data baru di paling atas (setelah header)
    encoding = "utf-8"  # Default encoding untuk CSV log

    try:
        # Baca semua data existing jika file ada
        existing_data = []
        file_exists = os.path.exists(csv_path)

        if file_exists:
            try:
                async with aiofiles.open(csv_path, "r", encoding=encoding) as f:
                    lines = await f.readlines()
                    existing_data = [line.strip() for line in lines if line.strip()]
            except Exception:
                # Jika error membaca, anggap file kosong
                existing_data = []

        # Tulis ulang file dengan data baru di atas
        async with aiofiles.open(csv_path, "w", encoding=encoding) as f:
            # Tulis header
            await f.write(",".join(header) + "\n")

            # Tulis data baru di paling atas
            await f.write(",".join(row) + "\n")

            # Tulis data existing (skip header jika ada)
            for line in existing_data:
                if line != ",".join(header):  # Skip header lama
                    await f.write(line + "\n")

    except UnicodeEncodeError:
        # Fallback ke utf-8-sig jika ada masalah encoding
        encoding = "utf-8-sig"

        # Ulangi logika yang sama dengan encoding berbeda
        existing_data = []
        file_exists = os.path.exists(csv_path)

        if file_exists:
            try:
                async with aiofiles.open(csv_path, "r", encoding=encoding) as f:
                    lines = await f.readlines()
                    existing_data = [line.strip() for line in lines if line.strip()]
            except Exception:
                existing_data = []

        # Tulis ulang file dengan data baru di atas
        async with aiofiles.open(csv_path, "w", encoding=encoding) as f:
            # Tulis header
            await f.write(",".join(header) + "\n")

            # Tulis data baru di paling atas
            await f.write(",".join(row) + "\n")

            # Tulis data existing (skip header jika ada)
            for line in existing_data:
                if line != ",".join(header):  # Skip header lama
                    await f.write(line + "\n")

    # UPDATE TIMESTAMP REAL-TIME saat user upload file
    try:
        from pusat_admin.status_user_aktif import update_user_timestamp_realtime
        user_id = str(message.from_user.id)
        username = message.from_user.username or ""
        first_name = message.from_user.first_name or ""
        last_name = message.from_user.last_name or ""

        # Update timestamp ke waktu sekarang
        update_user_timestamp_realtime(user_id, username, first_name, last_name)

    except Exception as e:
        # logging.warning(f"Error updating timestamp for file upload: {e}")  # Disabled untuk mengurangi noise
        pass