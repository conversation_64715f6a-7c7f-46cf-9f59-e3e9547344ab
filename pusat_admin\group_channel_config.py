"""
Utility untuk mengelola konfigurasi grup dan channel khusus admin
"""
import os
import time
import logging
from typing import Optional, Dict, Any

CONFIG_FILE = "pusat_admin/database/group_channel_config.txt"

class GroupChannelConfig:
    """Class untuk mengelola konfigurasi grup dan channel"""
    
    def __init__(self):
        self._cache = {}
        self._cache_time = 0
        self._cache_ttl = 60  # Cache 1 menit
        
    def _load_config(self) -> Dict[str, str]:
        """Load konfigurasi dari file"""
        config = {}
        
        if not os.path.exists(CONFIG_FILE):
            logging.warning(f"Config file not found: {CONFIG_FILE}")
            return self._get_default_config()
        
        try:
            with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#"):
                        parts = line.split("|")
                        if len(parts) >= 2:
                            key, value = parts[0], parts[1]
                            config[key] = value
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return self._get_default_config()
        
        return config
    
    def _get_default_config(self) -> Dict[str, str]:
        """Default config dari .env file"""
        from config import GROUP_ID, CHANNEL_ID, GROUP_LINK, CHANNEL_LINK
        return {
            "GROUP_ID": str(GROUP_ID),
            "CHANNEL_ID": str(CHANNEL_ID),
            "GROUP_LINK": GROUP_LINK,
            "CHANNEL_LINK": CHANNEL_LINK,
            "GROUP_NAME": "Room Kazuha",
            "CHANNEL_NAME": "Update",
            "GROUP_STATUS": "active",
            "CHANNEL_STATUS": "active",
            "MEMBERSHIP_CHECK_ENABLED": "true",
            "MEMBERSHIP_CACHE_TTL": "300",
            "MEMBERSHIP_TIMEOUT": "10",
            "ACTIVE_USER_THRESHOLD": "86400",
            "CLEANUP_INTERVAL": "3600"
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get config value dengan caching"""
        current_time = time.time()
        
        # Refresh cache jika expired
        if current_time - self._cache_time > self._cache_ttl:
            self._cache = self._load_config()
            self._cache_time = current_time
        
        value = self._cache.get(key, default)
        
        # Convert tipe data
        if isinstance(default, bool):
            return str(value).lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default, int):
            try:
                return int(value)
            except (ValueError, TypeError):
                return default
        elif isinstance(default, float):
            try:
                return float(value)
            except (ValueError, TypeError):
                return default
        
        return value
    
    def set(self, key: str, value: str, description: str = "", updated_by: str = "admin") -> bool:
        """Set config value dan simpan ke file"""
        try:
            # Load existing config
            config_lines = []
            
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                    config_lines = f.readlines()
            
            # Update atau tambah config
            updated = False
            current_time = int(time.time())
            
            for i, line in enumerate(config_lines):
                if line.strip() and not line.startswith("#"):
                    parts = line.strip().split("|")
                    if len(parts) >= 2 and parts[0] == key:
                        # Update existing
                        config_lines[i] = f"{key}|{value}|{description}|{current_time}|{updated_by}\n"
                        updated = True
                        break
            
            if not updated:
                # Add new
                config_lines.append(f"{key}|{value}|{description}|{current_time}|{updated_by}\n")
            
            # Save to file
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                f.writelines(config_lines)
            
            # Clear cache
            self._cache_time = 0
            
            logging.info(f"Config updated: {key} = {value}")
            return True
            
        except Exception as e:
            logging.error(f"Error setting config {key}: {e}")
            return False
    
    # Convenience methods untuk config yang sering digunakan
    def get_group_id(self) -> int:
        """Get GROUP_ID sebagai integer"""
        from config import GROUP_ID
        return self.get("GROUP_ID", GROUP_ID)

    def get_channel_id(self) -> int:
        """Get CHANNEL_ID sebagai integer"""
        from config import CHANNEL_ID
        return self.get("CHANNEL_ID", CHANNEL_ID)

    def get_group_link(self) -> str:
        """Get GROUP_LINK"""
        from config import GROUP_LINK
        return self.get("GROUP_LINK", GROUP_LINK)

    def get_channel_link(self) -> str:
        """Get CHANNEL_LINK"""
        from config import CHANNEL_LINK
        return self.get("CHANNEL_LINK", CHANNEL_LINK)

    def get_group_name(self) -> str:
        """Get GROUP_NAME"""
        return self.get("GROUP_NAME", "Room Kazuha")

    def get_channel_name(self) -> str:
        """Get CHANNEL_NAME"""
        return self.get("CHANNEL_NAME", "Update")
    
    def is_membership_check_enabled(self) -> bool:
        """Check apakah membership check enabled"""
        return self.get("MEMBERSHIP_CHECK_ENABLED", True)
    
    def get_membership_cache_ttl(self) -> int:
        """Get cache TTL untuk membership"""
        return self.get("MEMBERSHIP_CACHE_TTL", 300)
    
    def get_membership_timeout(self) -> int:
        """Get timeout untuk API call"""
        return self.get("MEMBERSHIP_TIMEOUT", 10)
    
    def get_active_user_threshold(self) -> int:
        """Get threshold user aktif (detik)"""
        return self.get("ACTIVE_USER_THRESHOLD", 86400)
    
    def get_cleanup_interval(self) -> int:
        """Get interval cleanup otomatis"""
        return self.get("CLEANUP_INTERVAL", 3600)

# Global instance
group_channel_config = GroupChannelConfig()

# Convenience functions
def get_group_id() -> int:
    return group_channel_config.get_group_id()

def get_channel_id() -> int:
    return group_channel_config.get_channel_id()

def get_group_link() -> str:
    return group_channel_config.get_group_link()

def get_channel_link() -> str:
    return group_channel_config.get_channel_link()

def get_group_name() -> str:
    return group_channel_config.get_group_name()

def get_channel_name() -> str:
    return group_channel_config.get_channel_name()
