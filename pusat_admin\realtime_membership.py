"""
Real-time membership checker untuk admin
Cek membership grup dan channel secara real-time

OTOMATIS TERINTEGRASI:
- Saat admin klik "🔄 Refresh" → otomatis cek membership real-time
- Tidak perlu tombol terpisah, semua otomatis
- User yang keluar grup/channel langsung dihapus dari daftar
"""
import asyncio
import logging
import time
import json
import os
from typing import Dict, Tuple, List, Optional
from .group_channel_config import get_group_id, get_channel_id

# File untuk cache membership real-time
MEMBERSHIP_CACHE_FILE = "pusat_admin/database/realtime_membership_cache.txt"
MEMBERSHIP_STATUS_FILE = "pusat_admin/database/membership_status_log.txt"

class RealtimeMembershipChecker:
    """Real-time membership checker dengan cache terpisah"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 menit
        self.last_cleanup = 0
        
        # Pastikan folder ada
        os.makedirs(os.path.dirname(MEMBERSHIP_CACHE_FILE), exist_ok=True)
        os.makedirs(os.path.dirname(MEMBERSHIP_STATUS_FILE), exist_ok=True)
        
        # Load cache dari file
        self._load_cache()
    
    def _load_cache(self):
        """Load cache dari file"""
        try:
            if os.path.exists(MEMBERSHIP_CACHE_FILE):
                with open(MEMBERSHIP_CACHE_FILE, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        # Skip baris kosong dan komentar
                        if not line or line.startswith("#"):
                            continue

                        parts = line.split("|")
                        if len(parts) >= 5:
                            try:
                                user_id, in_group, in_channel, timestamp, username = parts[:5]
                                # Validasi bahwa user_id adalah angka
                                if not user_id.isdigit():
                                    continue

                                self.cache[int(user_id)] = {
                                    "in_group": in_group == "True",
                                    "in_channel": in_channel == "True",
                                    "timestamp": int(timestamp),
                                    "username": username
                                }
                            except (ValueError, TypeError) as e:
                                # Skip baris yang tidak valid
                                continue
        except Exception as e:
            logging.warning(f"Error loading membership cache: {e}")
    
    def _save_cache(self):
        """Save cache ke file"""
        try:
            with open(MEMBERSHIP_CACHE_FILE, "w", encoding="utf-8") as f:
                f.write("# Real-time membership cache\n")
                f.write("# Format: user_id|in_group|in_channel|timestamp|username\n")
                for user_id, data in self.cache.items():
                    f.write(f"{user_id}|{data['in_group']}|{data['in_channel']}|{data['timestamp']}|{data.get('username', '')}\n")
        except Exception as e:
            logging.error(f"Error saving membership cache: {e}")
    
    def _log_membership_status(self, user_id: int, username: str, in_group: bool, in_channel: bool):
        """Log status membership untuk audit"""
        try:
            current_time = int(time.time())
            status = "ACTIVE" if (in_group and in_channel) else "INACTIVE"
            
            with open(MEMBERSHIP_STATUS_FILE, "a", encoding="utf-8") as f:
                f.write(f"{current_time}|{user_id}|{username}|{in_group}|{in_channel}|{status}\n")
        except Exception as e:
            # logging.warning(f"Error logging membership status: {e}")  # Disabled untuk mengurangi noise
            pass
    
    async def check_user_membership_realtime(self, bot, user_id: int, username: str = "") -> Tuple[bool, bool]:
        """
        Cek membership user secara real-time
        Returns: (in_group, in_channel)
        """
        try:
            GROUP_ID = get_group_id()
            CHANNEL_ID = get_channel_id()
            
            # logging.info(f"🔍 Real-time check for user {user_id} ({username})")  # Disabled untuk mengurangi noise

            # Cek grup
            in_group = False
            try:
                group_member = await asyncio.wait_for(
                    bot.get_chat_member(GROUP_ID, user_id),
                    timeout=10
                )
                # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
                # User yang dibatasi (restricted) masih dianggap member yang sah
                in_group = group_member.status in ['member', 'administrator', 'creator', 'restricted']
                # logging.info(f"📊 Group check {user_id}: {group_member.status} -> {in_group}")  # Disabled untuk mengurangi noise
            except asyncio.TimeoutError:
                # logging.warning(f"⏰ Group check timeout for {user_id}")  # Disabled untuk mengurangi noise
                in_group = False
            except Exception as e:
                # logging.warning(f"❌ Group check error for {user_id}: {e}")  # Disabled untuk mengurangi noise
                in_group = False

            # Cek channel
            in_channel = False
            try:
                channel_member = await asyncio.wait_for(
                    bot.get_chat_member(CHANNEL_ID, user_id),
                    timeout=10
                )
                # ✅ PERBAIKAN: Tambahkan "restricted" sebagai status yang valid
                # User yang dibatasi (restricted) masih dianggap member yang sah
                in_channel = channel_member.status in ['member', 'administrator', 'creator', 'restricted']
                # logging.info(f"📺 Channel check {user_id}: {channel_member.status} -> {in_channel}")  # Disabled untuk mengurangi noise
            except asyncio.TimeoutError:
                # logging.warning(f"⏰ Channel check timeout for {user_id}")  # Disabled untuk mengurangi noise
                in_channel = False
            except Exception as e:
                # logging.warning(f"❌ Channel check error for {user_id}: {e}")  # Disabled untuk mengurangi noise
                in_channel = False
            
            # Update cache
            current_time = int(time.time())
            self.cache[user_id] = {
                "in_group": in_group,
                "in_channel": in_channel,
                "timestamp": current_time,
                "username": username
            }
            
            # Log status
            self._log_membership_status(user_id, username, in_group, in_channel)
            
            is_active = in_group and in_channel
            status_emoji = "✅" if is_active else "❌"
            # logging.info(f"{status_emoji} User {user_id} ({username}): group={in_group}, channel={in_channel}, active={is_active}")  # Disabled untuk mengurangi noise

            return in_group, in_channel
            
        except Exception as e:
            logging.error(f"Error in real-time membership check for {user_id}: {e}")
            return False, False
    
    def get_cached_membership(self, user_id: int) -> Optional[Tuple[bool, bool, int]]:
        """
        Get cached membership data
        Returns: (in_group, in_channel, timestamp) atau None jika tidak ada/expired
        """
        if user_id not in self.cache:
            return None
        
        data = self.cache[user_id]
        current_time = int(time.time())
        
        # Check jika cache expired
        if current_time - data["timestamp"] > self.cache_ttl:
            return None
        
        return data["in_group"], data["in_channel"], data["timestamp"]
    
    def is_user_active_cached(self, user_id: int) -> Optional[bool]:
        """
        Check apakah user aktif berdasarkan cache
        Returns: True/False atau None jika tidak ada cache
        """
        cached = self.get_cached_membership(user_id)
        if cached is None:
            return None
        
        in_group, in_channel, _ = cached
        return in_group and in_channel
    
    async def check_multiple_users_realtime(self, bot, user_list: List[Dict], progress_callback=None) -> List[Dict]:
        """
        Cek membership multiple users secara real-time dengan progress callback

        Args:
            bot: Bot instance
            user_list: [{"id": user_id, "username": username, ...}, ...]
            progress_callback: Function untuk update progress (current, total, percentage)

        Returns: List user yang aktif di grup DAN channel
        """
        active_users = []
        total_users = len(user_list)

        # logging.info(f"🔍 Starting real-time check for {len(user_list)} users")  # Disabled untuk mengurangi noise

        for index, user_data in enumerate(user_list):
            user_id = int(user_data.get("id", 0))
            username = user_data.get("username", "")

            if user_id == 0:
                continue

            try:
                # Cek membership real-time
                in_group, in_channel = await self.check_user_membership_realtime(bot, user_id, username)

                # Jika aktif di KEDUA grup dan channel
                if in_group and in_channel:
                    active_users.append(user_data)

                # Update progress setelah setiap user
                current_processed = index + 1
                percentage = round((current_processed / total_users) * 100) if total_users > 0 else 0

                # Call progress callback jika ada
                if progress_callback:
                    try:
                        await progress_callback(current_processed, total_users, percentage)
                    except Exception as callback_error:
                        # Jika callback error, lanjut saja
                        pass

                # Delay kecil untuk menghindari rate limit dan biar progress keliatan
                await asyncio.sleep(0.2)

            except Exception as e:
                logging.error(f"Error checking user {user_id}: {e}")
                continue

        # Save cache setelah selesai
        self._save_cache()

        return active_users
    
    def cleanup_expired_cache(self):
        """Cleanup cache yang expired"""
        current_time = int(time.time())
        
        # Cleanup setiap 1 jam
        if current_time - self.last_cleanup < 3600:
            return
        
        expired_users = []
        for user_id, data in list(self.cache.items()):
            if current_time - data["timestamp"] > self.cache_ttl:
                expired_users.append(user_id)
                del self.cache[user_id]
        
        if expired_users:
            # logging.info(f"🧹 Cleaned up {len(expired_users)} expired cache entries")  # Disabled untuk mengurangi noise
            self._save_cache()
        
        self.last_cleanup = current_time
    
    def get_cache_stats(self) -> Dict:
        """Get statistik cache"""
        current_time = int(time.time())
        total = len(self.cache)
        active = 0
        expired = 0
        
        for data in self.cache.values():
            if current_time - data["timestamp"] <= self.cache_ttl:
                if data["in_group"] and data["in_channel"]:
                    active += 1
            else:
                expired += 1
        
        return {
            "total_cached": total,
            "active_users": active,
            "expired_entries": expired,
            "cache_ttl": self.cache_ttl
        }

# Global instance
realtime_checker = RealtimeMembershipChecker()

# Convenience functions
async def check_user_realtime(bot, user_id: int, username: str = "") -> Tuple[bool, bool]:
    """Check single user membership real-time"""
    return await realtime_checker.check_user_membership_realtime(bot, user_id, username)

async def filter_active_users_realtime(bot, user_list: List[Dict], progress_callback=None) -> List[Dict]:
    """Filter user list untuk yang aktif di grup DAN channel dengan progress callback"""
    return await realtime_checker.check_multiple_users_realtime(bot, user_list, progress_callback)

def get_membership_cache_stats() -> Dict:
    """Get cache statistics"""
    return realtime_checker.get_cache_stats()
