"""
Data Synchronization Module - Sinkronisasi data ke database terpisah
Setiap fitur punya database sendiri sesuai preferensi user
"""

import logging
import time
from .database import db


def sync_user_to_all_databases(user_id: str, username: str, first_name: str, last_name: str, timestamp: str = None):
    """Sinkronisasi data user ke semua database yang relevan"""
    if not timestamp:
        timestamp = str(int(time.time()))

    # Format baru dengan field status: user_id|username|first_name|last_name|timestamp|status
    # Cek apakah user sudah diblokir sebelumnya (hanya ambil status, data lain di-update)
    existing_total = db.find_by_field("total_users.txt", 0, user_id)
    current_status = ""
    if existing_total and len(existing_total['parts']) >= 6:
        current_status = existing_total['parts'][5]  # Ambil status yang ada

    # SELALU UPDATE data terbaru (username, first_name, last_name, timestamp)
    user_data = f"{user_id}|{username}|{first_name}|{last_name}|{timestamp}|{current_status}"
    
    try:
        # 1. Total Users - untuk status bot (semua user yang pernah akses)
        existing_total = db.find_by_field("total_users.txt", 0, user_id)
        if existing_total:
            db.update_by_field("total_users.txt", 0, user_id, user_data)
        else:
            db.append_line("total_users.txt", user_data)
        
        # Database duplikat dihapus - hanya gunakan total_users.txt sebagai sumber utama
        
        # logging.info(f"Synced user {user_id} to all databases")  # Disabled
        return True
        
    except Exception as e:
        logging.error(f"Error syncing user {user_id}: {e}")
        return False


def sync_active_user(user_id: str, username: str, first_name: str, last_name: str, timestamp: str = None):
    """Sinkronisasi user aktif - SEMUA USER YANG MENGGUNAKAN COMMAND DIANGGAP AKTIF"""
    if not timestamp:
        timestamp = str(int(time.time()))

    # Format konsisten dengan total_users.txt (dengan field status)
    # Cek status blokir dari total_users.txt
    existing_total = db.find_by_field("total_users.txt", 0, user_id)
    current_status = ""
    if existing_total and len(existing_total['parts']) >= 6:
        current_status = existing_total['parts'][5]  # Ambil status yang ada

    # Format dengan status untuk konsistensi
    user_data_with_status = f"{user_id}|{username}|{first_name}|{last_name}|{timestamp}|{current_status}"
    user_data_simple = f"{user_id}|{username}|{first_name}|{last_name}|{timestamp}"

    try:
        # 1. Update total_users.txt dengan data terbaru (REAL-TIME UPDATE)
        if existing_total:
            db.update_by_field("total_users.txt", 0, user_id, user_data_with_status)
        else:
            db.append_line("total_users.txt", user_data_with_status)

        # 2. Active Users Cache - untuk status user aktif dan status bot
        existing_active = db.find_by_field("active_users_cache.txt", 0, user_id)
        if existing_active:
            db.update_by_field("active_users_cache.txt", 0, user_id, user_data_with_status)
        else:
            db.append_line("active_users_cache.txt", user_data_with_status)

        # Database duplikat dihapus - tidak perlu sync ke database lain

        return True

    except Exception as e:
        logging.error(f"Error syncing active user {user_id}: {e}")
        return False


def remove_inactive_user(user_id: str):
    """Hapus user dari active users cache jika tidak aktif lagi"""
    try:
        db.delete_by_field("active_users_cache.txt", 0, user_id)
        return True
    except Exception as e:
        # logging.error(f"Error removing inactive user {user_id}: {e}")  # Disabled untuk mengurangi noise
        return False


def cleanup_old_active_users(max_age_hours: int = 24):
    """Bersihkan user aktif yang sudah lama tidak aktif"""
    try:
        current_time = int(time.time())
        max_age_seconds = max_age_hours * 3600
        
        active_data = db.read_dict_data("active_users_cache.txt")
        valid_users = []
        
        for item in active_data:
            parts = item['parts']
            if len(parts) >= 5:
                try:
                    user_timestamp = int(parts[4])
                    if current_time - user_timestamp <= max_age_seconds:
                        valid_users.append(item['raw'])
                except (ValueError, IndexError):
                    continue
        
        db.write_lines("active_users_cache.txt", valid_users)
        return True
        
    except Exception as e:
        # logging.error(f"Error cleaning up active users: {e}")  # Disabled untuk mengurangi noise
        return False


def get_database_stats():
    """Ambil statistik semua database"""
    try:
        stats = {
            'total_users': db.get_line_count("total_users.txt"),
            'active_users': db.get_line_count("active_users_cache.txt"),
            'blocked_users': db.get_line_count("blocked_users.txt"),
            'admins': db.get_line_count("admins.txt")
        }
        return stats
    except Exception as e:
        logging.error(f"Error getting database stats: {e}")
        return {}


def initialize_databases():
    """Inisialisasi semua database dengan file kosong jika belum ada"""
    databases = [
        "total_users.txt",
        "active_users_cache.txt",
        "blocked_users.txt",
        "admins.txt",
        "bot_status.txt",
        "broadcast_history.txt",
        "targeted_message_history.txt",
        "admin_activity_log.txt",
        "super_admins.txt",
        "admin_permissions.txt"
    ]

    try:
        for db_file in databases:
            if not db.file_exists(db_file):
                db.write_lines(db_file, [])

        return True

    except Exception as e:
        logging.error(f"Error initializing databases: {e}")
        return False


def auto_create_all_structure():
    """Auto-create SEMUA file, folder, database yang belum ada saat bot running"""
    import os

    try:
        # 1. AUTO-CREATE FOLDER UTAMA
        folders_to_create = [
            "pusat_admin/database",

            "management/user_data_file",
            "management/database",
            "data"
        ]

        for folder in folders_to_create:
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
                # logging.info(f"Created folder: {folder}")  # Disabled

        # 2. AUTO-CREATE DATABASE FILES (pusat_admin/database/)
        database_files = [
            "total_users.txt",
            "active_users_cache.txt",
            "blocked_users.txt",
            "admins.txt",
            "super_admins.txt",
            "admin_permissions.txt",
            "bot_status.txt",
            "broadcast_history.txt",
            "targeted_message_history.txt",
            "admin_activity_log.txt"
        ]

        for db_file in database_files:
            if not db.file_exists(db_file):
                db.write_lines(db_file, [])
                # logging.info(f"Created database file: {db_file}")  # Disabled

        # 3. AUTO-CREATE DATA FILES (pusat_admin/database/)
        data_files = [
            "pusat_admin/database/message.txt",
            "pusat_admin/database/user_log.txt"
        ]

        for file_path in data_files:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("")  # File kosong
                # logging.info(f"Created data file: {file_path}")  # Disabled

        # 4. AUTO-CREATE USER SETTINGS FILE (management/database/)
        settings_file = "management/database/user_settings.txt"
        if not os.path.exists(settings_file):
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write("")  # File kosong
            # logging.info(f"Created settings file: {settings_file}")  # Disabled

        # logging.info("Auto-create all structure completed successfully")  # Disabled
        return True

    except Exception as e:
        logging.error(f"Error auto-creating structure: {e}")
        return False
