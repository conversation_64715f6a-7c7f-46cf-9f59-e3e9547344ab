import os
from dotenv import load_dotenv

load_dotenv()

# Payment Gateway Configuration - WORKING API
PAYMENT_TOKEN = "f0f7444a811741d9289ccbae9db9a481de0a1581a5b7d99dec93437549cf26c6"  # TokenKey untuk header
PAYMENT_API_KEY = "5fdf5e1cc95a1e9234fb2e00344be801"
PAYMENT_MERCHANT_ID = "OK2528455"  # Merchant yang benar dari screenshot
PAYMENT_BASE_URL = "https://api.wahdx.co"
PAYMENT_MUTASI_ENDPOINT = "/api/mutasi-orkut"

# Token Orkut untuk /api/key-orkut endpoint (CRITICAL!)
PAYMENT_ORKUT_TOKEN = "2528455:NJ7MwxG2PgDurdWmoyYsl68Svk3qbaFc"
PAYMENT_ORKUT_USERNAME = "WARstoreUP"  # Username Orkut yang benar

# Legacy aliases untuk backward compatibility
ORDERKUOTA_API_KEY = PAYMENT_TOKEN
ORDERKUOTA_MERCHANT_CODE = PAYMENT_MERCHANT_ID
ORDERKUOTA_QRIS_URL = f"{PAYMENT_BASE_URL}{PAYMENT_MUTASI_ENDPOINT}"

# Premium Configuration - NEW PRICING SYSTEM
PREMIUM_PACKAGES = {
    "1d": {"price": 3000, "duration_seconds": 86400, "name": "1 Hari"},      # 24 jam
    "3d": {"price": 7000, "duration_seconds": 259200, "name": "3 Hari"},     # 72 jam
    "7d": {"price": 12000, "duration_seconds": 604800, "name": "7 Hari"},    # 168 jam
    "30d": {"price": 20000, "duration_seconds": 2592000, "name": "30 Hari"}  # 720 jam
}

# Legacy config for backward compatibility
PREMIUM_PRICE = 20000  # Default to 30 days package
PREMIUM_DURATION_SECONDS = 2592000  # 30 days in seconds (30 * 24 * 60 * 60)
QRIS_TIMEOUT_MINUTES = 5

# Package Selection Timeout
PACKAGE_SELECTION_TIMEOUT_MINUTES = 5

# QRIS Configuration
QRIS_IMAGE_PATH = os.path.join(os.path.dirname(__file__), "qris.png")

# Dynamic QR Settings
USE_DYNAMIC_QR = True  # Enable dynamic QR dengan auto-fill
QR_IMAGE_SIZE = 512  # QR image size in pixels
FALLBACK_TO_STATIC = True  # Fallback ke static QR jika dynamic gagal

# Database Paths
DATABASE_DIR = os.path.join(os.path.dirname(__file__), "database")
PREMIUM_USERS_DB = os.path.join(DATABASE_DIR, "premium_users.txt")
TRANSACTIONS_DB = os.path.join(DATABASE_DIR, "transactions.txt")
PENDING_PAYMENTS_DB = os.path.join(DATABASE_DIR, "pending_payments.txt")
PROCESSED_MUTATIONS_DB = os.path.join(DATABASE_DIR, "processed_mutations.txt")
PAYMENT_HISTORY_DB = os.path.join(DATABASE_DIR, "payment_history.txt")
TRIAL_USERS_DB = os.path.join(DATABASE_DIR, "trial_users.txt")
TOTAL_USERS_DB = "pusat_admin/database/total_users.txt"

# Payment Checker Configuration
PAYMENT_CHECK_INTERVAL = 30  # seconds
PAYMENT_TIMEOUT = 300  # 5 minutes in seconds

# Ensure database directory exists
os.makedirs(DATABASE_DIR, exist_ok=True)
