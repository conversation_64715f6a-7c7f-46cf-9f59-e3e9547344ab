from aiogram import BaseMiddleware
from aiogram.types import Message
import logging
from typing import Callable, Dict, Any, Awaitable
from .premium_service import PremiumService

class PremiumMiddleware(BaseMiddleware):
    def __init__(self):
        self.premium_service = PremiumService()
        
        # Commands yang tidak perlu premium check
        self.exempt_commands = {
            'PAYMENT_REQUIRED', 'MEMBERSHIP_REQUIRED', '/start', '/help', '/status'
        }
        
        # Commands yang perlu premium
        self.premium_required_commands = {
            '/to_vcf', '/to_txt', '/manual', '/add', '/delete',
            '/merge', '/split', '/count', '/nodup', '/generate', '/getname',
            '/admin', '/hide_menu'
        }
    
    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message,
        data: Dict[str, Any]
    ) -> Any:
        # Skip jika bukan private chat
        if event.chat.type != "private":
            return await handler(event, data)
        
        # Skip jika tidak ada text
        if not event.text:
            return await handler(event, data)
        
        user_id = event.from_user.id
        command = event.text.split()[0].lower() if event.text else ""
        
        # Skip untuk command yang dikecualikan - KECUALI user trial expired
        if command in self.exempt_commands:
            # Special check untuk /start dan /help - jangan exempt user trial expired
            if command in ['/start', '/help']:
                # Check apakah user trial expired
                try:
                    from payment.trial_service import TrialService
                    trial_service = TrialService()
                    is_trial_active, trial_info = trial_service.check_trial_status(user_id)

                    # Jika user punya trial expired, tetap lanjut ke premium check
                    if trial_info and trial_info.get('status') == 'expired':
                        # Jangan exempt, lanjut ke premium check
                        pass
                    else:
                        # User tidak punya trial atau trial aktif - exempt normal
                        return await handler(event, data)
                except Exception as e:
                    logging.error(f"Error checking trial for exempt command: {e}")
                    return await handler(event, data)
            else:
                # Command lain yang exempt (PAYMENT_REQUIRED, etc)
                return await handler(event, data)
        
        # Skip untuk admin commands khusus (kazuhaid)
        if command == '/kazuhaid':
            return await handler(event, data)
        
        # Cek apakah command memerlukan premium
        needs_premium = (
            command in self.premium_required_commands or
            event.text.startswith('/') or
            event.document or  # File upload
            event.photo      # Photo upload
        )
        
        if not needs_premium:
            return await handler(event, data)
        
        try:
            # 1. Cek apakah user adalah admin/super admin (auto premium) - PRIORITAS TERTINGGI
            if await self._is_admin_user(event):
                # logging.info(f"Admin user {user_id} accessing {command} (auto premium)")  # Disabled - tidak perlu log
                return await handler(event, data)

            # 2. Cek apakah user sudah premium/trial - BYPASS MEMBERSHIP JIKA SUDAH PREMIUM/TRIAL
            has_premium_access = await self._check_premium_or_trial_access(event)
            if has_premium_access:
                # Silent premium access
                return await handler(event, data)

            # 3. Cek membership untuk user yang belum premium/trial
            if not await self._check_membership(event):
                # Import membership handler
                from payment.premium_handler import show_membership_required
                return await show_membership_required(event)

            # 3.5. Hapus pesan join jika user sudah join
            await self._delete_join_message(event)

            # 3.6. AUTO TRIAL ACTIVATION untuk user baru yang eligible
            # HANYA SETELAH membership check berhasil (user sudah join grup & channel)
            await self._try_auto_activate_trial(event)

            # 4. Double check premium/trial access (setelah trial activation)
            from .grace_period_helper import check_user_premium_access
            has_access = check_user_premium_access(user_id)

            if not has_access:
                # User tidak punya akses, check payment states untuk routing yang benar
                try:
                    # Priority 1: Check QR payment state
                    from payment.qr_payment_handler import qr_payment_states, handle_spam_during_qr
                    if user_id in qr_payment_states:
                        qr_state = qr_payment_states[user_id]
                        if not qr_state.is_expired():
                            return await handle_spam_during_qr(event)

                    # Priority 2: Check package selection state
                    from payment.package_selection_handler import user_package_states, show_package_selection
                    if user_id in user_package_states:
                        package_state = user_package_states[user_id]
                        if not package_state.is_expired():
                            return await show_package_selection(event)

                    # Priority 3: No state, show package selection
                    return await show_package_selection(event)

                except ImportError:
                    from payment.premium_handler import show_payment_required
                    return await show_payment_required(event)
            
            # 5. User sudah premium, lanjut ke handler normal
            # Silent premium access
            return await handler(event, data)
            
        except Exception as e:
            logging.error(f"Error in premium middleware: {e}")
            return await handler(event, data)
    
    async def _check_membership(self, message: Message) -> bool:
        """Check if user has joined required groups/channels"""
        try:
            # Import membership check dari existing system
            from management.membership import check_membership

            user_id = message.from_user.id
            # Get bot instance from message
            bot = message.bot

            # check_membership returns (in_group, in_channel)
            in_group, in_channel = await check_membership(bot, user_id)

            # User must be in BOTH group and channel
            return in_group and in_channel

        except ImportError:
            # Fallback jika membership module tidak ada
            logging.warning("Membership module not found, skipping membership check")
            return True
        except Exception as e:
            logging.error(f"Error checking membership: {e}")
            return True  # Default allow jika error

    async def _delete_join_message(self, message: Message):
        """Delete join message if user has joined"""
        try:
            from management.membership import delete_join_message

            user_id = message.from_user.id
            chat_id = message.chat.id
            bot = message.bot

            await delete_join_message(bot, user_id, chat_id)

        except ImportError:
            # Fallback jika membership module tidak ada
            logging.warning("Membership module not found, skipping join message deletion")
        except Exception as e:
            logging.error(f"Error deleting join message: {e}")

    async def _try_auto_activate_trial(self, message: Message):
        """
        AUTO TRIAL ACTIVATION untuk user baru yang eligible
        HANYA dipanggil SETELAH membership check berhasil
        """
        try:
            from payment.trial_service import TrialService

            user_id = message.from_user.id
            trial_service = TrialService()

            # Check jika user eligible dan belum punya trial
            if trial_service.is_eligible_for_trial(user_id):
                is_active, _ = trial_service.check_trial_status(user_id)
                if not is_active:
                    # Auto activate trial untuk user baru yang sudah join grup & channel
                    success = trial_service.activate_trial(user_id, "auto_after_membership")
                    if success:
                        print(f"✅ Auto trial activated for user {user_id}")
        except Exception as e:
            # Jangan crash middleware jika trial activation gagal
            logging.error(f"Error auto activating trial for user {user_id}: {e}")

    async def _is_admin_user(self, message: Message) -> bool:
        """Check if user is admin or super admin (auto premium)"""
        try:
            from pusat_admin.auth_admin import is_admin, is_super_admin, get_admin_level_by_id

            username = message.from_user.username or ""
            user_id = message.from_user.id

            # Cek berdasarkan username
            if username and (is_admin(username) or is_super_admin(username)):
                return True

            # Cek berdasarkan user ID (fallback)
            admin_level = get_admin_level_by_id(user_id)

            return admin_level in ["admin", "super_admin"]

        except ImportError:
            # Silent import - tidak perlu warning
            return False
        except Exception as e:
            logging.error(f"Error checking admin status: {e}")
            return False

    async def _check_premium_or_trial_access(self, message: Message) -> bool:
        """
        ✅ PERBAIKAN: Check apakah user sudah premium atau trial
        Fungsi ini digunakan untuk bypass membership check untuk user restricted yang sudah premium/trial
        """
        try:
            user_id = message.from_user.id

            # Check premium access dengan grace period
            from .grace_period_helper import check_user_premium_access
            has_access = check_user_premium_access(user_id)

            if has_access:
                return True

            # Fallback: Check trial status langsung
            try:
                from payment.trial_service import TrialService
                trial_service = TrialService()
                is_trial_active, _ = trial_service.check_trial_status(user_id)
                if is_trial_active:
                    return True
            except Exception as e:
                logging.error(f"Error checking trial status: {e}")

            # Fallback: Check premium status langsung
            try:
                from payment.premium_service import PremiumService
                premium_service = PremiumService()
                is_premium, _ = premium_service.check_premium_status(user_id)
                if is_premium:
                    return True
            except Exception as e:
                logging.error(f"Error checking premium status: {e}")

            return False

        except Exception as e:
            logging.error(f"Error checking premium/trial access: {e}")
            return False
