from aiogram import Router, types, F
import logging

router = Router()

# Handler untuk join message
@router.message(F.chat.type.in_({"group", "supergroup"}), F.new_chat_members)
async def clean_join_message(message: types.Message):
    try:
        await message.delete()
        # logging.info(f"Deleted join message in group {message.chat.title or message.chat.id}")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.error(f"Failed to delete join message: {e}")

# Handler untuk leave message
@router.message(F.chat.type.in_({"group", "supergroup"}), F.left_chat_member)
async def clean_leave_message(message: types.Message):
    try:
        await message.delete()
        # logging.info(f"Deleted leave message in group {message.chat.title or message.chat.id}")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.error(f"Failed to delete leave message: {e}")