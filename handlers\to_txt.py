import asyncio
import logging
import os
import time
import aiofiles
import asyncio
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.file import extract_numbers
from utils.retry_send import retry_send_document, retry_send_documents_group, send_files_with_sending_mode
from utils.cancel_keyboard import set_cancel_keyboard
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.user_settings import get_to_txt_send_mode, get_to_txt_format
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs
from management.membership import check_membership, send_membership_message, delete_join_message
from management.data_file import log_file_upload

router = Router()

class ToTxtStates(StatesGroup):
    waiting_files = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

# Import fungsi write dari utils.format
from utils.format import write_text_file
# Import XLSX converter
from utils.xlsx_converter import convert_to_xlsx, get_contact_count_from_file

# Handler global untuk perintah lain agar bisa membatalkan proses ini

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_txt(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

# Handler utama
@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_start(message: types.Message, state: FSMContext):

    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    in_group, in_channel = await check_membership(message.bot, message.from_user.id)
    if not (in_group and in_channel):
        await send_membership_message(message, in_group, in_channel)
        return
    await delete_join_message(message.bot, message.from_user.id, message.chat.id)
    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    # Get format yang dipilih user
    user_format = get_to_txt_format(message.from_user.id)
    format_text = "TXT" if user_format == "txt" else "Excel"
    format_emoji = "📄" if user_format == "txt" else "📊"

    bot_msg = f"{format_emoji} Kirim file untuk diubah ke .{user_format.upper()}"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ToTxtStates.waiting_files)
    await state.update_data(files=[], logs=[])

@router.message(ToTxtStates.waiting_files, F.document, F.chat.type == "private")
async def to_txt_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    # === Tambahkan logging upload file ===
    await log_file_upload(message)
    # =====================================
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())

    data = await state.get_data()
    # Jika sudah pernah error, abaikan SEMUA upload berikutnya (tidak kirim pesan apapun, tidak proses file apapun)
    if data.get("file_error"):
        return

    # Validasi file berdasarkan format yang dipilih user
    user_format = get_to_txt_format(message.from_user.id)

    # Jika ada file salah format, reset semua file & logs, set flag error, kirim error, dan JANGAN proses apapun lagi
    if user_format == "txt" and ext == ".txt":
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ File TXT tidak bisa dikonversi ke TXT!\nKetik /to_txt untuk mulai ulang."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    elif user_format == "xlsx" and ext == ".xlsx":
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ File XLSX tidak bisa dikonversi ke XLSX!\nKetik /to_txt untuk mulai ulang."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    # File valid, proses seperti biasa (hanya jika belum pernah error)
    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        # Pesan /done hanya muncul jika ini file valid pertama DAN tidak ada file_error di state
        # Cek ulang state setelah update
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done untuk lanjut."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(ToTxtStates.waiting_files, Command("done"), F.chat.type == "private")
async def to_txt_done(message: types.Message, state: FSMContext):
    # Cek jika user mengetik perintah utama lain di tengah proses
    if message.text.strip().startswith("/to_vcf"):
        await state.clear()
        from handlers.to_vcf import to_vcf_start
        await to_vcf_start(message, state)
        return
    if message.text.strip().startswith("/to_txt"):
        await state.clear()
        await to_txt_start(message, state)
        return
    if message.text.strip().startswith("/start"):
        await state.clear()
        from handlers.start import start_handler
        await start_handler(message, state)
        return
    if message.text.strip().startswith("/help"):
        await state.clear()
        from handlers.start import help_handler
        await help_handler(message, state)
        return
    if message.text.strip().startswith("/admin"):
        await state.clear()
        from handlers.admin import admin_start
        await admin_start(message, state)
        return
    if message.text.strip().startswith("/manual"):
        await state.clear()
        from handlers.manual import manual_start
        await manual_start(message, state)
        return
    if message.text.strip().startswith("/add"):
        await state.clear()
        from handlers.add import add_start
        await add_start(message, state)
        return
    if message.text.strip().startswith("/delete"):
        await state.clear()
        from handlers.delete import delete_start
        await delete_start(message, state)
        return
    if message.text.strip().startswith("/renamectc"):
        await state.clear()
        from handlers.renamectc import renamectc_start
        await renamectc_start(message, state)
        return
    if message.text.strip().startswith("/renamefile"):
        await state.clear()
        from handlers.renamefile import renamefile_start
        await renamefile_start(message, state)
        return
    if message.text.strip().startswith("/merge"):
        await state.clear()
        from handlers.merge import merge_start
        await merge_start(message, state)
        return
    if message.text.strip().startswith("/split"):
        await state.clear()
        from handlers.split import split_start
        await split_start(message, state)
        return
    if message.text.strip().startswith("/count"):
        await state.clear()
        from handlers.count import count_start
        await count_start(message, state)
        return
    if message.text.strip().startswith("/nodup"):
        await state.clear()
        from handlers.nodup import nodup_start
        await nodup_start(message, state)
        return
    if message.text.strip().startswith("/getname"):
        await state.clear()
        from handlers.getname import getname_start
        await getname_start(message, state)
        return
    if message.text.strip().startswith("/generate"):
        await state.clear()
        from handlers.generate import generate_start
        await generate_start(message, state)
        return
    if message.text.strip().startswith("/setting"):
        await state.clear()
        from handlers.hide_menu import hide_menu_start
        await hide_menu_start(message, state)
        return

    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    # Urutkan files dan logs berdasarkan message_id agar urutan sesuai upload user
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)
    # Cetak log penerimaan file sesuai urutan upload user (summary)
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk konversi ke TXT")

    # Jalankan processing dengan user isolation
    await process_to_txt_with_isolation(message, state, files)

async def process_to_txt_with_isolation(message, state, files):
    """
    Wrapper function untuk process_to_txt dengan user isolation dan sending mode.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_to_txt dengan user isolation
    success, result = await with_user_isolation(user_id, process_to_txt, message, state, files)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)
        return

    # Processing berhasil, sekarang handle pengiriman
    if result and len(result) == 3:
        files_to_send, file_paths_to_delete, send_mode = result

        if files_to_send:
            # Set cancel keyboard sebelum mulai kirim
            await set_cancel_keyboard(message)

            # Kirim file dengan sending mode (di luar semaphore)
            # Keyboard akan di-restore otomatis di dalam send_files_with_sending_mode
            success, sent_count, total_count, cancelled = await send_files_with_sending_mode(message, files_to_send, user_id, send_mode)

        # Cleanup files setelah pengiriman dengan safe deletion
        if file_paths_to_delete:
            from utils.safe_file_cleanup import delayed_file_cleanup
            success_count, failed_count, failed_files = await delayed_file_cleanup(file_paths_to_delete, delay_before_cleanup=1.0)

            # Schedule background cleanup untuk file yang gagal
            if failed_count > 0:
                from utils.safe_file_cleanup import schedule_background_cleanup
                failed_paths = [path for path in file_paths_to_delete if os.path.basename(path) in failed_files]
                schedule_background_cleanup(failed_paths, delay_minutes=2)

    # Clear state
    await state.clear()

async def process_to_txt(message, state, files):
    """
    Fungsi utama untuk memproses file ke txt atau xlsx berdasarkan setting user.
    """
    from utils.format import write_text_file

    file_paths_to_delete = []
    files_to_send = []  # (file_path, filename) untuk group sending

    # Get format yang dipilih user
    user_format = get_to_txt_format(message.from_user.id)

    try:
        # Proses semua file dulu
        for file_path, original_filename, _ in files:
            # logging.info(f"user: proses file {os.path.basename(file_path)}")  # Dikurangi untuk mengurangi spam log
            if not os.path.exists(file_path):
                logging.error(f"File tidak ditemukan: {file_path}")
                await message.answer(f"⚠️ File tidak ditemukan: {os.path.basename(file_path)}")
                continue

            base_name, file_ext = os.path.splitext(original_filename)

            if user_format == "txt":
                # Mode TXT - convert ke TXT seperti biasa
                numbers = await extract_numbers(file_path)
                # logging.info(f"extract_numbers result: {len(numbers)} nomor ditemukan")  # Dikurangi untuk mengurangi spam log
                if not numbers:
                    bot_msg = f"⚠️ Tidak ada nomor di {original_filename}."
                    await message.answer(bot_msg)
                    log_bot(bot_msg)
                    continue
                output_name = f"{base_name}.txt"
                output_path = get_user_file_path(message.from_user.id, output_name)
                # Tulis file txt dengan retry dan deteksi encoding
                await write_text_file(output_path, "\n".join(numbers))
                # logging.info(f"File hasil ditulis: {output_path}")
                files_to_send.append((output_path, output_name))
                file_paths_to_delete.append(output_path)

            elif user_format == "xlsx":
                # Mode XLSX - convert ke Excel
                output_name = f"{base_name}.xlsx"
                output_path = get_user_file_path(message.from_user.id, output_name)

                # Convert ke XLSX menggunakan logic yang sama dengan to_txt
                success = await convert_to_xlsx(file_path, output_path)

                if success:
                    # Check jumlah kontak yang berhasil diconvert
                    contact_count = await get_contact_count_from_file(output_path)
                    if contact_count > 0:
                        files_to_send.append((output_path, output_name))
                        file_paths_to_delete.append(output_path)
                        # logging.info(f"Successfully converted {contact_count} contacts to XLSX")  # Reduced logging
                    else:
                        bot_msg = f"⚠️ Tidak ada nomor valid di {original_filename}."
                        await message.answer(bot_msg)
                        log_bot(bot_msg)
                        # Remove empty file
                        if os.path.exists(output_path):
                            os.remove(output_path)
                else:
                    bot_msg = f"⚠️ Gagal convert {original_filename} ke Excel."
                    await message.answer(bot_msg)
                    log_bot(bot_msg)

        # Return files untuk dikirim di luar semaphore
        send_mode = get_to_txt_send_mode(message.from_user.id)
        return files_to_send, file_paths_to_delete, send_mode
    except Exception as e:
        err_msg = f"❌ Gagal proses file. Ketik /to_txt untuk ulang.\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
        return None, [], "individual"  # Return empty untuk error case