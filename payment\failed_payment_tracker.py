"""
Failed Payment Tracker - Service untuk tracking user yang gagal bayar
"""

import json
import os
import logging
from datetime import datetime
from typing import Set, List, Dict
from .config_payment import DATABASE_DIR

# Path ke file failed users
FAILED_USERS_DB = os.path.join(DATABASE_DIR, "failed_users.txt")

class FailedPaymentTracker:
    """Service untuk tracking user yang gagal bayar"""
    
    def __init__(self):
        self.ensure_file_exists()
    
    def ensure_file_exists(self):
        """Pastikan file failed users ada"""
        os.makedirs(DATABASE_DIR, exist_ok=True)
        if not os.path.exists(FAILED_USERS_DB):
            with open(FAILED_USERS_DB, 'w', encoding='utf-8') as f:
                pass  # Create empty file
    
    def add_failed_user(self, user_id: int, payment_id: str = None, amount: int = None) -> bool:
        """
        Tambahkan user ke daftar failed (jika belum ada)
        
        Args:
            user_id: ID user yang gagal
            payment_id: ID pembayaran yang gagal (opsional)
            amount: Jumlah pembayaran yang gagal (opsional)
            
        Returns:
            bool: True jika berhasil ditambahkan, False jika sudah ada
        """
        try:
            if not user_id:
                return False
            
            # Check if user already in failed list
            existing_failed = self.get_failed_users()
            
            # Check if user already exists
            for failed_user in existing_failed:
                if failed_user.get('user_id') == user_id:
                    # User sudah ada di failed list, update info jika perlu
                    self._update_failed_user(user_id, payment_id, amount)
                    return False
            
            # Add new failed user
            failed_user_data = {
                "user_id": user_id,
                "failed_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_payment_id": payment_id,
                "last_amount": amount,
                "failed_count": 1,
                "status": "failed"
            }
            
            with open(FAILED_USERS_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(failed_user_data) + '\n')
            
            # logging.info(f"Added user {user_id} to failed list")  # Disabled untuk mengurangi noise
            return True
        
        except Exception as e:
            logging.error(f"Error adding failed user: {e}")
            return False
    
    def _update_failed_user(self, user_id: int, payment_id: str = None, amount: int = None):
        """Update informasi user yang sudah ada di failed list"""
        try:
            failed_users = self.get_failed_users()
            updated_users = []
            
            for failed_user in failed_users:
                if failed_user.get('user_id') == user_id:
                    # Update existing user
                    failed_user['failed_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    failed_user['failed_count'] = failed_user.get('failed_count', 1) + 1
                    if payment_id:
                        failed_user['last_payment_id'] = payment_id
                    if amount:
                        failed_user['last_amount'] = amount
                
                updated_users.append(failed_user)
            
            # Rewrite file with updated data
            with open(FAILED_USERS_DB, 'w', encoding='utf-8') as f:
                for user in updated_users:
                    f.write(json.dumps(user) + '\n')
            
            # logging.info(f"Updated failed user {user_id}")  # Disabled untuk mengurangi noise
        
        except Exception as e:
            logging.error(f"Error updating failed user: {e}")
    
    def get_failed_users(self) -> List[Dict]:
        """Ambil semua user yang gagal"""
        try:
            failed_users = []
            
            if os.path.exists(FAILED_USERS_DB):
                with open(FAILED_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                user_data = json.loads(line)
                                failed_users.append(user_data)
                            except json.JSONDecodeError:
                                continue
            
            return failed_users
        except Exception as e:
            logging.error(f"Error getting failed users: {e}")
            return []
    
    def get_failed_user_ids(self) -> Set[int]:
        """Ambil set user ID yang gagal"""
        try:
            failed_users = self.get_failed_users()
            return {user.get('user_id') for user in failed_users if user.get('user_id')}
        except Exception as e:
            logging.error(f"Error getting failed user IDs: {e}")
            return set()
    
    def count_failed_users(self) -> int:
        """Hitung jumlah user yang gagal (unique)"""
        return len(self.get_failed_user_ids())
    
    def remove_failed_user(self, user_id: int) -> bool:
        """
        Hapus user dari daftar failed (ketika user berhasil bayar)
        
        Args:
            user_id: ID user yang akan dihapus
            
        Returns:
            bool: True jika berhasil dihapus
        """
        try:
            failed_users = self.get_failed_users()
            updated_users = [user for user in failed_users if user.get('user_id') != user_id]
            
            # Rewrite file without the removed user
            with open(FAILED_USERS_DB, 'w', encoding='utf-8') as f:
                for user in updated_users:
                    f.write(json.dumps(user) + '\n')
            
            # logging.info(f"Removed user {user_id} from failed list")  # Disabled untuk mengurangi noise
            return True
        
        except Exception as e:
            logging.error(f"Error removing failed user: {e}")
            return False
    
    def is_user_failed(self, user_id: int) -> bool:
        """Cek apakah user ada di daftar failed"""
        failed_user_ids = self.get_failed_user_ids()
        return user_id in failed_user_ids
    
    def get_user_failed_info(self, user_id: int) -> Dict:
        """Ambil informasi detail user yang gagal"""
        try:
            failed_users = self.get_failed_users()
            for user in failed_users:
                if user.get('user_id') == user_id:
                    return user
            return {}
        except Exception as e:
            logging.error(f"Error getting user failed info: {e}")
            return {}
    
    def cleanup_old_failed_users(self, days: int = 30):
        """
        Bersihkan user failed yang sudah lama (opsional untuk maintenance)
        
        Args:
            days: Hapus user failed yang lebih dari X hari
        """
        try:
            failed_users = self.get_failed_users()
            current_time = datetime.now()
            updated_users = []
            
            for user in failed_users:
                try:
                    failed_date = datetime.strptime(user['failed_date'], "%Y-%m-%d %H:%M:%S")
                    days_diff = (current_time - failed_date).days
                    
                    if days_diff <= days:
                        updated_users.append(user)
                    else:
                        # logging.info(f"Cleaned up old failed user {user.get('user_id')} ({days_diff} days old)")  # Disabled untuk mengurangi noise
                        pass  # User expired, tidak ditambahkan ke updated_users
                
                except (ValueError, KeyError):
                    # Keep user if date parsing fails
                    updated_users.append(user)
            
            # Rewrite file with cleaned data
            with open(FAILED_USERS_DB, 'w', encoding='utf-8') as f:
                for user in updated_users:
                    f.write(json.dumps(user) + '\n')
            
            # logging.info(f"Cleaned up failed users older than {days} days")  # Disabled untuk mengurangi noise
        
        except Exception as e:
            logging.error(f"Error cleaning up old failed users: {e}")
