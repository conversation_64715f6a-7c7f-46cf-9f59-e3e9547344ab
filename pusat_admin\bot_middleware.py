import logging
import time
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery
from typing import Callable, Dict, Any, Awaitable
try:
    from pusat_admin.auth_admin import is_super_admin, is_admin
    from pusat_admin.kontrol_bot import get_maintenance_status
    def is_bot_active():
        return not get_maintenance_status()
except ImportError:
    # Fallback untuk compatibility
    def is_super_admin(username):
        return False
    def is_admin(username):
        return False
    def is_bot_active():
        return True

class BotStatusMiddleware(BaseMiddleware):
    """Middleware untuk mengecek status bot"""
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        # <PERSON><PERSON> proses untuk message dan callback query
        if not isinstance(event, (Message, CallbackQuery)):
            return await handler(event, data)

        # Skip jika bukan private chat (untuk Message)
        if isinstance(event, Message) and event.chat.type != "private":
            return await handler(event, data)

        # Ambil username user
        username = event.from_user.username or ""
        
        # Jika admin (termasuk super admin), selalu izinkan
        if is_admin(username):
            return await handler(event, data)

        # Cek blokir user dipindah ke BlockCheckMiddleware - HAPUS DARI SINI

        # Jika bot dimatikan, tolak user biasa
        if not is_bot_active():
            try:
                from pusat_admin.kontrol_bot import get_maintenance_message
                maintenance_msg = get_maintenance_message()
            except ImportError:
                maintenance_msg = "sabar gua tester dulu"

            if isinstance(event, Message):
                await event.answer(maintenance_msg)
            elif isinstance(event, CallbackQuery):
                await event.answer(maintenance_msg, show_alert=True)
            pass  # Log removed for cleaner terminal
            return

        # Jika bot aktif, simpan user ke database terpisah dan lanjutkan (untuk Message DAN CallbackQuery)
        if isinstance(event, (Message, CallbackQuery)):
            try:
                from pusat_admin.data_sync import sync_user_to_all_databases, sync_active_user
                from pusat_admin.membership_checker import MembershipChecker

                user_id = str(event.from_user.id)
                username = event.from_user.username or ''
                first_name = event.from_user.first_name or ''
                last_name = event.from_user.last_name or ''
                timestamp = str(int(time.time()))

                # Sync ke semua database (total users, user list, search index, dll)
                sync_user_to_all_databases(user_id, username, first_name, last_name, timestamp)

                # AUTO TRIAL ACTIVATION DIPINDAH KE PREMIUM MIDDLEWARE
                # Setelah membership check berhasil untuk memastikan user sudah join grup & channel

                # SEMUA USER YANG MENGGUNAKAN COMMAND DIANGGAP AKTIF
                # Langsung sync ke active users cache tanpa cek membership
                # Pengecekan membership dilakukan saat refresh manual saja
                sync_active_user(user_id, username, first_name, last_name, timestamp)
                pass  # Log removed for cleaner terminal

            except ImportError:
                pass  # Skip jika module belum ada
            except Exception as e:
                logging.error(f"Error syncing user to databases: {e}")

        return await handler(event, data)
