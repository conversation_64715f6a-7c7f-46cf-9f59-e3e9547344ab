"""
Info File Module - Statistik file dan storage
"""

import logging
import os
import asyncio
from datetime import datetime, timedelta
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .keyboards import create_back_keyboard, create_info_file_keyboard, create_delete_files_keyboard, create_delete_confirm_keyboard
from .utils import format_file_size
from .database import db
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()

# Global storage untuk prevent multiple downloads
download_locks = {}  # user_id -> asyncio.Lock()
download_in_progress = {}  # user_id -> timestamp


async def acquire_download_lock(user_id: int) -> bool:
    """
    Acquire download lock untuk user. Return True jika berhasil, False jika sedang download.
    """
    if user_id not in download_locks:
        download_locks[user_id] = asyncio.Lock()

    # Coba acquire lock tanpa blocking
    if download_locks[user_id].locked():
        return False

    # Acquire lock
    await download_locks[user_id].acquire()
    download_in_progress[user_id] = datetime.now()
    return True


def release_download_lock(user_id: int):
    """
    Release download lock untuk user.
    """
    if user_id in download_locks and download_locks[user_id].locked():
        download_locks[user_id].release()

    if user_id in download_in_progress:
        del download_in_progress[user_id]


def is_user_downloading(user_id: int) -> bool:
    """
    Check apakah user sedang download.
    """
    return user_id in download_locks and download_locks[user_id].locked()


def get_utc_timestamp():
    """Ambil timestamp UTC dalam format DD-MM HH.MM (titik untuk nama file)"""
    from datetime import datetime, timezone

    # ✅ UTC SYSTEM: Gunakan UTC untuk konsistensi sistem
    now_utc = datetime.now(timezone.utc)

    # Format: DD-MM HH.MM (pakai titik karena : tidak bisa di nama file Windows)
    return now_utc.strftime("%d-%m %H.%M")


def get_file_statistics():
    """Ambil statistik file"""
    try:
        stats = {
            'data_folder_files': {'vcf': 0, 'txt': 0, 'xlsx': 0},
            'management_files': {'csv': 0},
            'total_size': 0,
            'user_dirs': 0
        }
        
        # Hitung file di management/user_data_file (CSV files)
        user_data_dir = "management/user_data_file"
        if os.path.exists(user_data_dir):
            for filename in os.listdir(user_data_dir):
                file_path = os.path.join(user_data_dir, filename)
                if os.path.isfile(file_path):
                    ext = filename.split('.')[-1].lower()
                    if ext == 'csv':
                        stats['management_files']['csv'] += 1
                    stats['total_size'] += os.path.getsize(file_path)
        
        # Hitung file di data directory (user folders)
        data_dir = "data"
        if os.path.exists(data_dir):
            for item in os.listdir(data_dir):
                item_path = os.path.join(data_dir, item)
                if os.path.isdir(item_path):
                    stats['user_dirs'] += 1
                    # Hitung file dalam subdirectory user
                    for filename in os.listdir(item_path):
                        file_path = os.path.join(item_path, filename)
                        if os.path.isfile(file_path):
                            ext = filename.split('.')[-1].lower()
                            if ext in stats['data_folder_files']:
                                stats['data_folder_files'][ext] += 1
                            stats['total_size'] += os.path.getsize(file_path)
        
        # Hitung ukuran database admin
        db_size = 0
        try:
            db_dir = "pusat_admin/database"
            if os.path.exists(db_dir):
                for filename in os.listdir(db_dir):
                    file_path = os.path.join(db_dir, filename)
                    if os.path.isfile(file_path):
                        db_size += os.path.getsize(file_path)
        except:
            pass

        stats['database_size'] = db_size
        
        return stats
        
    except Exception as e:
        logging.error(f"Error getting file statistics: {e}")
        return {
            'data_folder_files': {'vcf': 0, 'txt': 0, 'xlsx': 0},
            'management_files': {'csv': 0},
            'total_size': 0,
            'user_dirs': 0,
            'database_size': 0
        }


@router.callback_query(F.data == "admin_files")
async def show_files_info(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan info file"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_view_files']:
        await callback.answer("❌ Anda tidak memiliki izin lihat file", show_alert=True)
        return

    # Tampilkan loading
    await callback.message.edit_text("🔄 Menghitung file...")

    try:
        stats = get_file_statistics()
        
        # Format info text sesuai permintaan
        info_text = f"""📁 **DATA & MANAGEMENT**

📊 **FOLDER & FILE:**
• FOLDER: {stats['user_dirs']} file
• VCF: {stats['data_folder_files']['vcf']} file
• TXT: {stats['data_folder_files']['txt']} file
• XLSX: {stats['data_folder_files']['xlsx']} file

📂 **MANAGEMENT:**
• CSV: {stats['management_files']['csv']} file"""

        await callback.message.edit_text(
            info_text,
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )
        
    except Exception as e:
        logging.error(f"Error showing files info: {e}")
        await callback.message.edit_text(
            "❌ **Error**\n\nGagal memuat info file.",
            reply_markup=create_back_keyboard("admin_back"),
            parse_mode="Markdown"
        )

    await callback.answer()


@router.callback_query(F.data == "admin_storage_info")
async def show_storage_info(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan info storage detail"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        stats = get_file_statistics()
        
        # Hitung breakdown storage
        csv_files_total = stats['management_files']['csv']
        data_files_total = stats['data_folder_files']['vcf'] + stats['data_folder_files']['txt'] + stats['data_folder_files']['xlsx']
        
        storage_text = f"""💾 **DETAIL STORAGE**

📊 **Breakdown:**
• CSV User Files: {csv_files_total} file
• Data Files: {data_files_total} file
• Folder ID User: {stats['user_dirs']} folder
• Database Files: 5+ file

💽 **Size:**
• Database: {format_file_size(stats['database_size'])}
• Total: {format_file_size(stats['total_size'])}

📈 **Statistics:**
• Avg per user folder: {stats['total_size'] // max(stats['user_dirs'], 1):,} bytes
• CSV = Folder ratio: {csv_files_total}:{stats['user_dirs']} (should be equal)

🔧 **Maintenance:**
• Regular cleanup recommended
• Monitor storage usage
• Archive old files"""

        await callback.message.edit_text(
            storage_text,
            reply_markup=create_back_keyboard("admin_data_manage"),
            parse_mode="Markdown"
        )
        
        pass  # Log removed for cleaner terminal
        
    except Exception as e:
        logging.error(f"Error showing storage info: {e}")
        await callback.message.edit_text(
            "❌ **Error**\n\nGagal memuat info storage.",
            reply_markup=create_back_keyboard("admin_data_manage"),
            parse_mode="Markdown"
        )

    await callback.answer()


@router.callback_query(F.data == "admin_download_all")
async def download_all_files(callback: types.CallbackQuery, state: FSMContext):
    """Download kedua file ZIP sekaligus (File Data + File CSV) - PROTECTED AGAINST MULTIPLE CLICKS"""
    user_id = callback.from_user.id
    username = callback.from_user.username or ""

    # STEP 1: Answer callback immediately untuk acknowledge click
    await callback.answer("🔄 Memproses download...")

    # STEP 2: Check admin access
    if not is_admin(username):
        await callback.message.edit_text(
            "❌ **AKSES DITOLAK**\n\nAnda tidak memiliki akses admin.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )
        return

    # STEP 3: Check permissions
    permissions = get_admin_permissions(username)
    if not permissions['can_view_files']:
        await callback.message.edit_text(
            "❌ **IZIN DITOLAK**\n\nAnda tidak memiliki izin download file.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )
        return

    # STEP 4: Check if user already downloading (PREVENT MULTIPLE DOWNLOADS)
    if is_user_downloading(user_id):
        await callback.message.edit_text(
            "⚠️ **DOWNLOAD SEDANG BERJALAN**\n\nTunggu download sebelumnya selesai.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )
        return

    # STEP 5: Acquire download lock
    if not await acquire_download_lock(user_id):
        await callback.message.edit_text(
            "⚠️ **DOWNLOAD SEDANG BERJALAN**\n\nTunggu download sebelumnya selesai.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )
        return

    # STEP 6: Start download process
    timestamp_utc = get_utc_timestamp()
    await callback.message.edit_text(f"📥 **DOWNLOAD FILES**\n\n🔄 Membuat File Data {timestamp_utc}.zip...", parse_mode="Markdown")

    try:
        import zipfile

        # Buat nama file ZIP dengan timestamp UTC yang sama
        data_zip_filename = f"File Data {timestamp_utc}.zip"
        csv_zip_filename = f"File CSV {timestamp_utc}.zip"

        data_zip_path = data_zip_filename
        csv_zip_path = csv_zip_filename

        # 1. BUAT FILE DATA ZIP
        data_file_count = 0
        with zipfile.ZipFile(data_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Tambahkan file dari folder data/ (hanya .txt, .vcf, .xlsx)
            data_dir = "data"
            if os.path.exists(data_dir):
                for user_folder in os.listdir(data_dir):
                    user_path = os.path.join(data_dir, user_folder)
                    if os.path.isdir(user_path):
                        for filename in os.listdir(user_path):
                            file_path = os.path.join(user_path, filename)
                            if os.path.isfile(file_path):
                                # Filter hanya file .txt, .vcf, .xlsx
                                _, ext = os.path.splitext(filename)
                                if ext.lower() in ['.txt', '.vcf', '.xlsx']:
                                    # Simpan dengan struktur folder user
                                    zip_name = f"{user_folder}/{filename}"
                                    zipf.write(file_path, zip_name)
                                    data_file_count += 1

        # Update progress
        await callback.message.edit_text(f"📥 **DOWNLOAD FILES**\n\n🔄 Membuat File CSV {timestamp_utc}.zip...", parse_mode="Markdown")

        # 2. BUAT FILE CSV ZIP
        csv_file_count = 0
        with zipfile.ZipFile(csv_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Tambahkan file dari management/user_data_file/ (hanya .csv)
            csv_dir = "management/user_data_file"
            if os.path.exists(csv_dir):
                for filename in os.listdir(csv_dir):
                    file_path = os.path.join(csv_dir, filename)
                    if os.path.isfile(file_path):
                        # Filter hanya file .csv
                        _, ext = os.path.splitext(filename)
                        if ext.lower() == '.csv':
                            # Simpan dengan nama asli
                            zipf.write(file_path, filename)
                            csv_file_count += 1

        # Cek apakah ada file untuk dikirim
        total_files = data_file_count + csv_file_count
        if total_files == 0:
            # Hapus file ZIP kosong
            if os.path.exists(data_zip_path):
                os.remove(data_zip_path)
            if os.path.exists(csv_zip_path):
                os.remove(csv_zip_path)

            await callback.message.edit_text(
                "❌ **TIDAK ADA FILE**\n\nTidak ada file untuk didownload.",
                reply_markup=create_info_file_keyboard(),
                parse_mode="Markdown"
            )
            return

        # Update progress
        await callback.message.edit_text(f"📥 **DOWNLOAD FILES**\n\n🔄 Mengirim file...", parse_mode="Markdown")

        # 3. KIRIM KEDUA FILE ZIP
        files_sent = 0

        # Kirim File Data ZIP jika ada
        if data_file_count > 0:
            with open(data_zip_path, 'rb') as zip_file:
                await callback.message.answer_document(
                    types.BufferedInputFile(zip_file.read(), data_zip_filename)
                )
            files_sent += 1
            # Hapus file ZIP setelah kirim
            os.remove(data_zip_path)

        # Kirim File CSV ZIP jika ada
        if csv_file_count > 0:
            with open(csv_zip_path, 'rb') as zip_file:
                await callback.message.answer_document(
                    types.BufferedInputFile(zip_file.read(), csv_zip_filename)
                )
            files_sent += 1
            # Hapus file ZIP setelah kirim
            os.remove(csv_zip_path)

        # Pesan sukses
        success_text = f"✅ **DOWNLOAD BERHASIL**\n\n📦 {files_sent} file ZIP telah dikirim!\n📊 Total: {total_files} file"

        await callback.message.edit_text(
            success_text,
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )

    except Exception as e:
        logging.error(f"Error downloading all files: {e}")

        # Cleanup file ZIP jika ada error
        for zip_path in [data_zip_path, csv_zip_path]:
            if 'zip_path' in locals() and os.path.exists(zip_path):
                try:
                    os.remove(zip_path)
                except:
                    pass

        await callback.message.edit_text(
            "❌ **ERROR DOWNLOAD**\n\nGagal membuat file ZIP.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )

    finally:
        # ALWAYS release download lock
        release_download_lock(user_id)




@router.callback_query(F.data == "admin_delete_files_menu")
async def show_delete_files_menu(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan menu hapus file"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        stats = get_file_statistics()

        menu_text = f"""🗑️ **HAPUS FILE**

📊 **File yang dapat dihapus:**
• VCF: {stats['data_folder_files']['vcf']} file kontak
• TXT: {stats['data_folder_files']['txt']} file teks
• XLSX: {stats['data_folder_files']['xlsx']} file excel"""

        await callback.message.edit_text(
            menu_text,
            reply_markup=create_delete_files_keyboard(),
            parse_mode="Markdown"
        )

    except Exception as e:
        logging.error(f"Error showing delete files menu: {e}")
        await callback.message.edit_text(
            "❌ **Error**\n\nGagal memuat menu hapus file.",
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )

    await callback.answer()


@router.callback_query(F.data.startswith("admin_delete_"))
async def handle_delete_file_type(callback: types.CallbackQuery, state: FSMContext):
    """Handler untuk pilihan jenis file yang akan dihapus"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    action = callback.data.replace("admin_delete_", "")

    # Skip jika sudah konfirmasi
    if action.startswith("confirm_"):
        return

    try:
        stats = get_file_statistics()

        if action == "vcf":
            file_count = stats['data_folder_files']['vcf']
            file_type = ".vcf"
        elif action == "txt":
            file_count = stats['data_folder_files']['txt']
            file_type = ".txt"
        elif action == "xlsx":
            file_count = stats['data_folder_files']['xlsx']
            file_type = ".xlsx"
        elif action == "all":
            file_count = stats['data_folder_files']['vcf'] + stats['data_folder_files']['txt'] + stats['data_folder_files']['xlsx']
            file_type = "(.vcf .txt .xlsx)"
        else:
            await callback.answer("❌ Pilihan tidak valid", show_alert=True)
            return

        if file_count == 0:
            await callback.answer(f"❌ Tidak ada file {file_type} untuk dihapus", show_alert=True)
            return

        confirm_text = f"""⚠️ **KONFIRMASI HAPUS**

Yakin hapus {file_count} file {file_type}?"""

        await callback.message.edit_text(
            confirm_text,
            reply_markup=create_delete_confirm_keyboard(action),
            parse_mode="Markdown"
        )

    except Exception as e:
        logging.error(f"Error handling delete file type: {e}")
        await callback.message.edit_text(
            "❌ **Error**\n\nGagal memproses permintaan.",
            reply_markup=create_delete_files_keyboard(),
            parse_mode="Markdown"
        )

    await callback.answer()


@router.callback_query(F.data.startswith("admin_confirm_delete_"))
async def confirm_delete_files(callback: types.CallbackQuery, state: FSMContext):
    """Eksekusi hapus file setelah konfirmasi"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    action = callback.data.replace("admin_confirm_delete_", "")

    # Tampilkan loading
    await callback.message.edit_text("🗑️ **MENGHAPUS FILE**\n\n🔄 Memproses...", parse_mode="Markdown")

    try:
        deleted_count = 0

        # Tentukan ekstensi file yang akan dihapus
        if action == "vcf":
            extensions = [".vcf"]
        elif action == "txt":
            extensions = [".txt"]
        elif action == "xlsx":
            extensions = [".xlsx"]
        elif action == "all":
            extensions = [".vcf", ".txt", ".xlsx"]
        else:
            await callback.answer("❌ Aksi tidak valid", show_alert=True)
            return

        # Hapus file dari folder data/
        data_dir = "data"
        if os.path.exists(data_dir):
            for user_folder in os.listdir(data_dir):
                user_path = os.path.join(data_dir, user_folder)
                if os.path.isdir(user_path):
                    for filename in os.listdir(user_path):
                        file_path = os.path.join(user_path, filename)
                        if os.path.isfile(file_path):
                            # Cek ekstensi file
                            _, ext = os.path.splitext(filename)
                            if ext.lower() in extensions:
                                try:
                                    os.remove(file_path)
                                    deleted_count += 1
                                except Exception as e:
                                    logging.error(f"Error deleting file {file_path}: {e}")

        # Hasil
        if action == "all":
            file_type = "(.vcf .txt .xlsx)"
        else:
            file_type = f".{action}"

        result_text = f"""✅ **HAPUS SELESAI**

🗑️ {deleted_count} file {file_type} berhasil dihapus"""

        await callback.message.edit_text(
            result_text,
            reply_markup=create_info_file_keyboard(),
            parse_mode="Markdown"
        )

    except Exception as e:
        logging.error(f"Error deleting files: {e}")
        await callback.message.edit_text(
            "❌ **ERROR HAPUS**\n\nGagal menghapus file.",
            reply_markup=create_delete_files_keyboard(),
            parse_mode="Markdown"
        )

    await callback.answer()
