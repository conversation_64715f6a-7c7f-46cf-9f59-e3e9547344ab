"""
Broadcast Pesan Module - <PERSON><PERSON> pesan ke semua user
"""

import logging
import asyncio
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_send_message_menu_keyboard, create_back_keyboard
from .utils import log_bot, get_current_timestamp
from .database import db
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()


def get_all_users_for_broadcast():
    """Ambil SEMUA user yang pernah menggunakan bot dari total_users.txt"""
    # GUNAKAN DATABASE UTAMA (total_users.txt) - SEMUA user yang pernah akses bot
    user_data = db.read_dict_data("total_users.txt")
    users = []

    for item in user_data:
        parts = item['parts']
        if len(parts) >= 5:
            users.append({
                "id": parts[0],
                "username": parts[1],
                "first_name": parts[2],
                "last_name": parts[3]
            })

    return users


def save_broadcast_history(admin_username, media_type, sent_count, failed_count, total_count):
    """Simpan history broadcast"""
    timestamp = get_current_timestamp()
    history_entry = f"{timestamp}|{admin_username}|{media_type}|{sent_count}|{failed_count}|{total_count}"
    db.append_line("broadcast_history.txt", history_entry)


@router.callback_query(F.data == "user_broadcast")
async def show_broadcast_menu(callback: types.CallbackQuery, state: FSMContext):
    """Menu broadcast pesan"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_broadcast']:
        await callback.answer("❌ Anda tidak memiliki izin broadcast", show_alert=True)
        return

    broadcast_text = """📢 **BROADCAST KE SEMUA USER**

Kirim pesan atau media:"""

    try:
        await callback.message.edit_text(
            broadcast_text,
            reply_markup=create_back_keyboard("user_send_message"),
            parse_mode="Markdown"
        )
        await callback.answer()
    except Exception as e:
        if "message is not modified" in str(e).lower():
            await callback.answer("✅ Menu sudah terbaru")
        else:
            logging.error(f"Error editing broadcast menu: {e}")
            await callback.answer("❌ Error loading menu", show_alert=True)
            return

    await state.set_state(AdminStates.waiting_broadcast_message)


@router.message(AdminStates.waiting_broadcast_message, F.chat.type == "private")
async def process_broadcast_message(message: types.Message, state: FSMContext):
    """Proses broadcast pesan - mendukung semua jenis media"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_broadcast']:
        await message.answer("❌ Anda tidak memiliki izin broadcast")
        await state.clear()
        return

    # Tentukan jenis media
    media_type = "teks"
    if message.photo:
        media_type = "foto"
    elif message.video:
        media_type = "video"
    elif message.document:
        media_type = "dokumen"
    elif message.sticker:
        media_type = "stiker"
    elif message.animation:
        media_type = "animasi"

    # Ambil semua user
    users = get_all_users_for_broadcast()
    
    if not users:
        await message.answer(
            "❌ **Tidak ada user terdaftar**\n\nBelum ada user yang menggunakan bot ini.",
            reply_markup=create_send_message_menu_keyboard(),
            parse_mode="Markdown"
        )
        await state.clear()
        return

    # Tampilkan status awal
    status_msg = await message.answer(
        f"📤 **BROADCAST {media_type.upper()}**\n\nSedang mengirim ke {len(users)} user terdaftar...",
        parse_mode="Markdown"
    )

    # Proses broadcast
    sent = 0
    failed = 0
    bot = message.bot

    for i, user in enumerate(users):
        try:
            user_id = int(user["id"])

            # Kirim sesuai jenis media
            if message.text:
                await bot.send_message(user_id, message.text)
            elif message.photo:
                await bot.send_photo(user_id, message.photo[-1].file_id, caption=message.caption)
            elif message.video:
                await bot.send_video(user_id, message.video.file_id, caption=message.caption)
            elif message.document:
                await bot.send_document(user_id, message.document.file_id, caption=message.caption)
            elif message.sticker:
                await bot.send_sticker(user_id, message.sticker.file_id)
            elif message.animation:
                await bot.send_animation(user_id, message.animation.file_id, caption=message.caption)

            sent += 1
            
            # Update progress setiap 10 user
            if (i + 1) % 10 == 0:
                progress = f"📤 **BROADCAST {media_type.upper()}**\n\n📊 {i + 1}/{len(users)}\n✅ {sent} | ❌ {failed}"
                try:
                    await status_msg.edit_text(progress, parse_mode="Markdown")
                except:
                    pass
                    
        except Exception as e:
            logging.warning(f"Gagal kirim broadcast ke {user['id']}: {e}")
            failed += 1

        # Delay kecil untuk menghindari rate limit
        await asyncio.sleep(0.1)

    # Hasil akhir - SIMPLE
    result_text = f"""✅ **BROADCAST SELESAI**

Berhasil mengirim ke {sent} dari {len(users)} user ({(sent/len(users)*100):.1f}% sukses)"""

    await status_msg.edit_text(
        result_text,
        reply_markup=create_send_message_menu_keyboard(),
        parse_mode="Markdown"
    )

    # Simpan history
    save_broadcast_history(username, media_type, sent, failed, len(users))
    
    await state.set_state(AdminStates.main_menu)
