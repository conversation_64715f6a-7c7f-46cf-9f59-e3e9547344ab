"""
Middleware untuk cek user yang diblokir
"""

import logging
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery


class BlockCheckMiddleware(BaseMiddleware):
    """Middleware untuk cek apakah user diblokir"""
    
    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        """Cek apakah user diblokir sebelum memproses request"""
        
        try:
            # Ambil user ID
            user_id = str(event.from_user.id)
            
            # Skip cek untuk admin panel (biar admin bisa akses)
            if hasattr(event, 'text') and event.text:
                if event.text.startswith('/kazuhaid'):
                    return await handler(event, data)
            
            # Skip cek untuk callback admin
            if hasattr(event, 'data') and event.data:
                admin_callbacks = [
                    'admin_back', 'admin_stats', 'admin_users', 'user_aktif',
                    'refresh_active_users', 'admin_stats_refresh'
                ]
                if any(event.data.startswith(cb) for cb in admin_callbacks):
                    return await handler(event, data)
            
            # Cek apakah user diblokir
            from pusat_admin.blokir_user import is_user_blocked, get_user_block_message

            if is_user_blocked(user_id):
                # User diblokir - cek apakah ini command atau pesan biasa
                if isinstance(event, Message):
                    # Jika pesan biasa (bukan command), abaikan saja
                    if event.text and not event.text.startswith('/'):
                        # Pesan biasa dari user diblokir - tidak ada respon
                        return

                    # Jika command, cek apakah command valid
                    if event.text and event.text.startswith('/'):
                        # Daftar command valid yang ada di bot
                        valid_commands = [
                            '/start', '/help', '/to_vcf', '/to_txt', '/admin', '/manual',
                            '/add', '/delete', '/renamectc', '/renamefile', '/merge', '/split',
                            '/count', '/nodup', '/hide_menu', '/setnametovcf', '/setsendtovcf',
                            '/setsendtotxt', '/kazuhaid'
                        ]

                        # Ambil command (tanpa parameter)
                        command = event.text.split()[0].lower()

                        # Hanya respon jika command valid
                        if command in valid_commands:
                            block_message = get_user_block_message(user_id)
                            full_message = f"🚫 {block_message}\n\n📞 Untuk membuka blokir, hubungi admin: @KazuhaID1"
                            await event.answer(full_message)

                        # Command tidak valid atau valid, tetap return (jangan lanjut ke handler)
                        return

                elif isinstance(event, CallbackQuery):
                    # Callback query dari user diblokir - kirim pesan blokir
                    block_message = get_user_block_message(user_id)
                    full_message = f"🚫 {block_message}\n\n📞 Untuk membuka blokir, hubungi admin: @KazuhaID1"
                    await event.answer(full_message, show_alert=True)
                    return

                # Untuk jenis event lain, jangan lanjutkan
                return
            
            # User tidak diblokir, lanjutkan ke handler
            return await handler(event, data)
            
        except Exception as e:
            logging.warning(f"Error in BlockCheckMiddleware: {e}")
            # Jika ada error, tetap lanjutkan ke handler
            return await handler(event, data)
