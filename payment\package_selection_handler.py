"""
Package Selection Handler - Sistem Pemilihan Paket Premium Baru
Menangani flow: User non-premium → Pilih paket → QR Payment → Konfirmasi
"""

import logging
import asyncio
from datetime import datetime, timedelta
from aiogram import Router, types, F
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from .config_payment import PREMIUM_PACKAGES, PACKAGE_SELECTION_TIMEOUT_MINUTES
from .payment_service import PaymentService
from .premium_service import PremiumService

router = Router()

# Global storage untuk tracking user states dan message IDs
user_package_states = {}
user_message_tracking = {}
clicked_buttons = {}  # Track tombol yang sudah diklik untuk prevent double click

class PackageState:
    """Class untuk tracking state user dalam flow pembayaran"""
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.state = "package_selection"  # package_selection, qr_payment, completed
        self.package_key = None
        self.package_info = None
        self.payment_id = None
        self.created_at = datetime.now()
        self.message_ids = []  # Track semua message IDs untuk auto-delete
        self.button_message_ids = []  # Track button messages (prioritas hapus pertama)
        self.spam_message_ids = []  # Track spam response messages
        self.user_command_ids = []  # Track user commands (ke-2, ke-3, dst untuk dihapus)
        self.initial_command_id = None  # Track initial user command (PRESERVED)
        self.timeout_task = None

    def add_message_id(self, message_id: int):
        """Tambah message ID untuk tracking"""
        if message_id not in self.message_ids:
            self.message_ids.append(message_id)

    def add_button_message_id(self, message_id: int):
        """Tambah button message ID untuk prioritas cleanup"""
        if message_id not in self.button_message_ids:
            self.button_message_ids.append(message_id)
            self.add_message_id(message_id)

    def add_spam_message_id(self, message_id: int):
        """Tambah spam response message ID"""
        if message_id not in self.spam_message_ids:
            self.spam_message_ids.append(message_id)
            self.add_message_id(message_id)

    def add_user_command_id(self, message_id: int):
        """Tambah user command ID (ke-2, ke-3, dst untuk dihapus)"""
        if message_id not in self.user_command_ids:
            self.user_command_ids.append(message_id)
            self.add_message_id(message_id)

    def is_expired(self) -> bool:
        """Cek apakah state sudah expired (5 menit)"""
        return datetime.now() - self.created_at > timedelta(minutes=PACKAGE_SELECTION_TIMEOUT_MINUTES)


def create_package_selection_keyboard():
    """Buat keyboard untuk pilihan paket premium (2x2 layout)"""
    keyboard = [
        [
            InlineKeyboardButton(text="1 Hari", callback_data="package_1d"),
            InlineKeyboardButton(text="3 Hari", callback_data="package_3d")
        ],
        [
            InlineKeyboardButton(text="7 Hari", callback_data="package_7d"),
            InlineKeyboardButton(text="30 Hari", callback_data="package_30d")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


async def cleanup_package_selection_timeout(bot, user_id: int):
    """Hapus SEMUA pesan, command, dan tombol saat timeout (5 menit) KECUALI perintah pertama"""
    try:
        if user_id in user_package_states:
            state = user_package_states[user_id]

            # Prioritas 1: Hapus tombol dulu (dari atas ke bawah)
            messages_to_delete = []

            # Kumpulkan button messages (prioritas pertama)
            for message_id in state.button_message_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan spam messages
            for message_id in state.spam_message_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan user commands (ke-2, ke-3, dst)
            for message_id in state.user_command_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan sisa messages lain
            for message_id in state.message_ids:
                if (message_id != state.initial_command_id and
                    message_id not in messages_to_delete):
                    messages_to_delete.append(message_id)

            # Batch delete semua messages
            if messages_to_delete:
                await batch_delete_messages(bot, user_id, messages_to_delete)

            # Cancel timeout task jika ada
            if state.timeout_task and not state.timeout_task.done():
                state.timeout_task.cancel()

            # Hapus dari tracking
            del user_package_states[user_id]

    except Exception as e:
        logging.error(f"Error cleaning up package selection timeout for user {user_id}: {e}")


async def cleanup_package_selection_on_button_click(bot, user_id: int):
    """Hapus SEMUA pesan kecuali perintah pertama saat user klik tombol paket"""
    try:
        if user_id in user_package_states:
            state = user_package_states[user_id]

            # Prioritas 1: Hapus tombol dulu
            messages_to_delete = []

            # Kumpulkan button messages (prioritas pertama)
            for message_id in state.button_message_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan spam messages
            for message_id in state.spam_message_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan user commands (ke-2, ke-3, dst)
            for message_id in state.user_command_ids:
                if message_id != state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan sisa messages lain
            for message_id in state.message_ids:
                if (message_id != state.initial_command_id and
                    message_id not in messages_to_delete):
                    messages_to_delete.append(message_id)

            # Batch delete semua messages
            if messages_to_delete:
                await batch_delete_messages(bot, user_id, messages_to_delete)

            # Cancel timeout task jika ada
            if state.timeout_task and not state.timeout_task.done():
                state.timeout_task.cancel()

            # Hapus dari tracking
            del user_package_states[user_id]

    except Exception as e:
        logging.error(f"Error cleaning up package selection on button click for user {user_id}: {e}")


async def batch_delete_messages(bot, user_id: int, message_ids: list):
    """Batch delete multiple messages dengan error handling"""
    try:
        if not message_ids:
            return

        # Coba batch delete dulu (lebih efisien)
        try:
            if len(message_ids) > 1:
                await bot.delete_messages(chat_id=user_id, message_ids=message_ids)
                return
        except Exception as e:
            # Check if it's a "message not found" error for batch delete
            if "message to delete not found" not in str(e).lower():
                logging.warning(f"Batch delete failed for user {user_id}: {e}")

        # Fallback ke individual delete
        for message_id in message_ids:
            try:
                await bot.delete_message(chat_id=user_id, message_id=message_id)
            except Exception as e:
                # Only log non-"not found" errors
                if "message to delete not found" not in str(e).lower():
                    logging.warning(f"Failed to delete message {message_id} for user {user_id}: {e}")

    except Exception as e:
        logging.error(f"Error in batch delete messages for user {user_id}: {e}")


async def timeout_handler(bot, user_id: int):
    """Handler untuk timeout paket selection (5 menit) - hapus SEMUA pesan"""
    try:
        await asyncio.sleep(PACKAGE_SELECTION_TIMEOUT_MINUTES * 60)  # 5 menit

        if user_id in user_package_states:
            await cleanup_package_selection_timeout(bot, user_id)

    except asyncio.CancelledError:
        # Task dibatalkan, normal behavior
        pass
    except Exception as e:
        logging.error(f"Error in timeout handler for user {user_id}: {e}")


@router.message(F.text == "PAYMENT_REQUIRED")
async def show_package_selection(message: types.Message):
    """Tampilkan pilihan paket premium atau handle spam command"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    try:
        # CRITICAL: Check apakah user trial aktif - jangan kasih premium menu
        from .trial_service import TrialService
        from .grace_period_helper import check_user_premium_access

        trial_service = TrialService()
        is_trial_active, _ = trial_service.check_trial_status(user_id)
        has_access = check_user_premium_access(user_id)

        if is_trial_active or has_access:
            # User trial aktif atau sudah punya akses - jangan kasih premium menu, silent return
            return
        # Cek apakah user sudah ada dalam flow pembayaran
        if user_id in user_package_states:
            state = user_package_states[user_id]

            if state.state == "package_selection" and not state.is_expired():
                # User spam command saat package selection aktif
                remaining_time = PACKAGE_SELECTION_TIMEOUT_MINUTES - int((datetime.now() - state.created_at).total_seconds() // 60)
                remaining_time = max(1, remaining_time)  # Minimal 1 menit

                spam_text = f"""💎 Pilih paket premium dulu ☝️
⏰ Sisa waktu: {remaining_time} menit"""

                spam_msg = await message.answer(spam_text)
                state.add_spam_message_id(spam_msg.message_id)
                # Track user spam command (perintah ke-2, ke-3, dst) untuk dihapus
                # Hanya perintah pertama (initial_command_id) yang dipertahankan
                state.add_user_command_id(message.message_id)

                return

            elif state.state == "qr_payment":
                # User sudah dalam tahap QR payment, ini tidak seharusnya terjadi
                # karena routing sudah dihandle di middleware
                logging.warning(f"User {user_id} in QR state but reached package handler - this should not happen")
                return
        
        # Cleanup state lama jika ada
        if user_id in user_package_states:
            await cleanup_package_selection_timeout(message.bot, user_id)
        
        # Buat state baru
        state = PackageState(user_id)
        user_package_states[user_id] = state

        # Track initial user command untuk reference, tapi jangan hapus saat cleanup
        state.initial_command_id = message.message_id

        # Buat pesan pilihan paket
        package_text = """💎 Upgrade ke Premium

Pilih durasi yang cocok:

⭐ 1 Hari - Rp 3.000
⭐ 3 Hari - Rp 7.000
⭐ 7 Hari - Rp 12.000
⭐ 30 Hari - Rp 20.000

🚀 Akses unlimited semua fitur!"""

        # Kirim pesan dengan keyboard
        package_msg = await message.answer(
            package_text,
            reply_markup=create_package_selection_keyboard()
        )

        # Track button message (prioritas hapus pertama)
        state.add_button_message_id(package_msg.message_id)

        # Start timeout task
        state.timeout_task = asyncio.create_task(timeout_handler(message.bot, user_id))
        
    except Exception as e:
        logging.error(f"Error showing package selection to user {user_id}: {e}")
        # Silent error - no user notification


@router.callback_query(F.data.startswith("package_"))
async def handle_package_selection(callback: types.CallbackQuery):
    """Handle pemilihan paket premium"""
    user_id = callback.from_user.id
    username = callback.from_user.username or "Unknown"

    try:
        # Extract package key
        package_key = callback.data.split("_", 1)[1]

        if package_key not in PREMIUM_PACKAGES:
            await callback.answer("❌ Paket tidak valid", show_alert=True)
            return

        # Cek apakah tombol sudah diklik sebelumnya (prevent double click)
        button_key = f"{user_id}_{callback.data}"
        if button_key in clicked_buttons:
            await callback.answer("⚠️ Tombol sudah diklik, tunggu proses selesai", show_alert=True)
            return

        # Mark tombol sebagai sudah diklik
        clicked_buttons[button_key] = datetime.now()

        # Cek state user
        if user_id not in user_package_states:
            await callback.answer("❌ Session expired, ketik command lagi", show_alert=True)
            # Cleanup clicked button tracking
            if button_key in clicked_buttons:
                del clicked_buttons[button_key]
            return

        state = user_package_states[user_id]

        if state.is_expired():
            await callback.answer("❌ Session expired, ketik command lagi", show_alert=True)
            await cleanup_package_selection_timeout(callback.bot, user_id)
            # Cleanup clicked button tracking
            if button_key in clicked_buttons:
                del clicked_buttons[button_key]
            return
        
        # Simpan pilihan paket
        package_info = PREMIUM_PACKAGES[package_key]
        state.package_key = package_key
        state.package_info = package_info
        state.state = "qr_payment"

        # Answer callback
        await callback.answer(f"✅ Paket {package_info['name']} dipilih")

        # Cleanup SEMUA pesan kecuali perintah pertama (prioritas tombol dulu)
        await cleanup_package_selection_on_button_click(callback.bot, user_id)

        # Generate QR payment (akan dihandle oleh QR handler)
        # PENTING: Buat message object yang benar untuk user yang klik tombol
        from .qr_payment_handler import generate_qr_payment_for_user
        await generate_qr_payment_for_user(callback, package_key, package_info)

        # Cleanup clicked button tracking setelah berhasil
        if button_key in clicked_buttons:
            del clicked_buttons[button_key]

    except Exception as e:
        logging.error(f"Error handling package selection for user {user_id}: {e}")
        await callback.answer("❌ Terjadi kesalahan, coba lagi", show_alert=True)

        # Cleanup clicked button tracking jika error
        button_key = f"{user_id}_{callback.data}"
        if button_key in clicked_buttons:
            del clicked_buttons[button_key]
