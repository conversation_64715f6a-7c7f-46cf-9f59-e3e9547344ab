import asyncio
import logging
import time
from typing import Optional
from aiogram import Bot

class BackgroundTaskManager:
    """Manager untuk background tasks"""
    
    def __init__(self):
        self.tasks = {}
        self.running = False
        self.bot: Optional[Bot] = None
    
    def set_bot(self, bot: <PERSON><PERSON>):
        """Set bot instance untuk background tasks"""
        self.bot = bot
    
    async def start_background_tasks(self):
        """<PERSON>lai semua background tasks"""
        if self.running:
            return

        self.running = True
        # logging.info("Starting background tasks...")  # Disabled untuk mengurangi noise

        # Task untuk refresh membership cache setiap 10 menit
        self.tasks['membership_refresh'] = asyncio.create_task(
            self._membership_refresh_task()
        )

        # Task untuk cleanup cache expired setiap 30 menit
        self.tasks['cache_cleanup'] = asyncio.create_task(
            self._cache_cleanup_task()
        )

        # Task untuk cleanup stuck semaphores setiap 10 menit
        self.tasks['semaphore_cleanup'] = asyncio.create_task(
            self._semaphore_cleanup_task()
        )

        # Task untuk auto backup system
        try:
            from pusat_admin.backup_system import init_auto_backup_service
            auto_backup_service = init_auto_backup_service(self.bot)
            await auto_backup_service.start()
            self.tasks['auto_backup'] = auto_backup_service
        except Exception:
            pass

        # logging.info("Background tasks started")  # Disabled untuk mengurangi noise
    
    async def stop_background_tasks(self):
        """Stop semua background tasks"""
        if not self.running:
            return

        self.running = False
        # logging.info("Stopping background tasks...")  # Disabled untuk mengurangi noise

        for task_name, task in self.tasks.items():
            if task_name == 'auto_backup':
                # Special handling untuk auto backup service
                try:
                    if hasattr(task, 'stop'):
                        await task.stop()
                except Exception:
                    pass
            elif not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass  # Disabled logging untuk mengurangi noise

        self.tasks.clear()
        # logging.info("Background tasks stopped")  # Disabled untuk mengurangi noise
    
    async def _membership_refresh_task(self):
        """Background task untuk refresh membership cache"""
        while self.running:
            try:
                # Tunggu 10 menit
                await asyncio.sleep(600)  # 10 menit
                
                if not self.running or not self.bot:
                    break
                
                # Refresh membership cache
                try:
                    from pusat_admin.membership_checker import refresh_membership_cache
                    await refresh_membership_cache(self.bot)
                except ImportError:
                    # Fallback jika file belum ada - disabled logging untuk mengurangi noise
                    pass

                # logging.info("Background membership cache refresh completed")  # Disabled untuk mengurangi noise
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in membership refresh task: {e}")
                # Tunggu 5 menit sebelum retry jika ada error
                await asyncio.sleep(300)
    
    async def _cache_cleanup_task(self):
        """Background task untuk cleanup cache expired"""
        while self.running:
            try:
                # Tunggu 30 menit
                await asyncio.sleep(1800)  # 30 menit
                
                if not self.running:
                    break
                
                # Cleanup expired cache
                try:
                    from pusat_admin.cache_manager import cache_manager
                    cache_manager.clear_expired()
                except ImportError:
                    # Fallback jika file belum ada - disabled logging untuk mengurangi noise
                    pass

                # logging.info("Background cache cleanup completed")  # Disabled untuk mengurangi noise
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in cache cleanup task: {e}")
                # Tunggu 10 menit sebelum retry jika ada error
                await asyncio.sleep(600)

    async def _semaphore_cleanup_task(self):
        """Background task untuk cleanup stuck semaphores"""
        while self.running:
            try:
                # Tunggu 10 menit
                await asyncio.sleep(600)  # 10 menit

                if not self.running:
                    break

                # Cleanup stuck semaphores dan sending mode
                try:
                    from utils.user_isolation import user_isolation_manager
                    # Cleanup semaphores yang tidak aktif untuk menghemat memory
                    await user_isolation_manager.cleanup_inactive_users(max_inactive_users=1000)
                    # Cleanup sending mode tracking
                    await user_isolation_manager.cleanup_sending_mode(max_users=100)
                    # Cleanup cancel flags
                    await user_isolation_manager.cleanup_cancel_flags(max_users=100)
                except ImportError:
                    # Fallback jika file belum ada
                    pass
                except Exception as e:
                    logging.error(f"Error in semaphore cleanup: {e}")

                # logging.info("Background semaphore cleanup completed")  # Disabled untuk mengurangi noise

            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in semaphore cleanup task: {e}")
                # Tunggu 5 menit sebelum retry jika ada error
                await asyncio.sleep(300)



# Instance global
background_task_manager = BackgroundTaskManager()

async def start_background_tasks(bot: Bot):
    """Fungsi helper untuk start background tasks"""
    background_task_manager.set_bot(bot)
    await background_task_manager.start_background_tasks()

async def stop_background_tasks():
    """Fungsi helper untuk stop background tasks"""
    await background_task_manager.stop_background_tasks()
