"""
Kelola User Module - Manajemen user dan pagination
"""

import logging
import math
import time
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_user_menu_keyboard, create_pagination_keyboard, create_back_keyboard
from .utils import log_bot, format_time_ago, truncate_text, clean_text_for_markdown, safe_edit_message, safe_answer_callback
from .database import db
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()

USERS_PER_PAGE = 20  # Sesuai preferensi user


def get_all_users_paginated():
    """Ambil semua user dengan data lengkap untuk pagination"""
    # GUNAKAN DATABASE UTAMA (yang 60 user) - BUKAN active users
    user_data = db.read_dict_data("total_users.txt")
    users = []

    for item in user_data:
        parts = item['parts']
        if len(parts) >= 5:
            try:
                # Ambil data user dengan format: user_id|username|first_name|last_name|timestamp|status
                user_id = str(parts[0]).strip()
                username = str(parts[1] if len(parts) > 1 else "").strip()
                first_name = str(parts[2] if len(parts) > 2 else "").strip()
                last_name = str(parts[3] if len(parts) > 3 else "").strip()

                # Format baru: user_id|username|first_name|last_name|timestamp|status
                timestamp = str(parts[4]).strip() if len(parts) > 4 else ""
                status = str(parts[5]).strip() if len(parts) > 5 else ""

                # Validasi timestamp
                try:
                    test_timestamp = int(timestamp)
                    if not (1600000000 <= test_timestamp <= 2000000000):  # 2020-2033
                        timestamp = str(int(time.time()))  # Fallback ke waktu sekarang
                except (ValueError, TypeError):
                    timestamp = str(int(time.time()))  # Fallback ke waktu sekarang

                # Normalisasi status
                if status.upper() == "BLOCKED":
                    status = "BLOCKED"
                else:
                    status = "NORMAL"



                # Validasi user ID harus numeric
                if not user_id.isdigit():
                    logging.warning(f"Invalid user ID: {user_id}")
                    continue

                # Kasih tanda [BLOCKED] untuk user yang diblokir
                is_blocked = (status == "BLOCKED")
                status_mark = " 🚫[BLOCKED]" if is_blocked else ""

                users.append({
                    "id": user_id,
                    "username": username,
                    "first_name": first_name,
                    "last_name": last_name,
                    "timestamp": timestamp,
                    "status": status,
                    "is_blocked": is_blocked,
                    "status_mark": status_mark
                })

            except Exception as e:
                # Skip data yang tidak valid
                logging.warning(f"Invalid user data skipped: {parts} - Error: {e}")
                continue

    # Sort berdasarkan timestamp terbaru dengan error handling
    try:
        users.sort(key=lambda x: int(x["timestamp"]), reverse=True)
    except ValueError as e:
        logging.error(f"Error sorting users: {e}")
        # Fallback: sort berdasarkan user ID
        users.sort(key=lambda x: x["id"], reverse=True)

    return users


def update_user_status_in_file(user_id: str, new_status: str):
    """Update status user di file total_users.txt"""
    try:
        # Baca semua data
        user_data = db.read_dict_data("total_users.txt")
        updated_lines = []
        user_found = False

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5 and parts[0] == user_id:
                # User ditemukan, update status
                user_found = True

                # Format: user_id|username|first_name|last_name|timestamp|status
                if len(parts) >= 6:
                    # Sudah ada kolom status, update
                    parts[5] = new_status
                else:
                    # Belum ada kolom status, tambah
                    parts.append(new_status)

                updated_line = "|".join(parts)
                updated_lines.append(updated_line)
                # logging.info(f"Updated user {user_id} status to {new_status}")  # Disabled
            else:
                # User lain, tetap sama
                original_line = "|".join(parts)
                updated_lines.append(original_line)

        if user_found:
            # Tulis kembali ke file
            db.write_lines("total_users.txt", updated_lines)
            return True
        else:
            logging.warning(f"User {user_id} not found in total_users.txt")
            return False

    except Exception as e:
        logging.error(f"Error updating user status: {e}")
        return False


def block_user_in_file(user_id: str) -> bool:
    """Blokir user dengan update status di file"""
    return update_user_status_in_file(user_id, "BLOCKED")


def unblock_user_in_file(user_id: str) -> bool:
    """Buka blokir user dengan update status di file (kosongkan status)"""
    return update_user_status_in_file(user_id, "")


def format_user_list_page(users, page, total_pages):
    """Format daftar user untuk satu halaman - REALTIME"""
    try:
        start_idx = (page - 1) * USERS_PER_PAGE
        end_idx = start_idx + USERS_PER_PAGE
        page_users = users[start_idx:end_idx]

        text = f"📋 **DAFTAR USER** ({page}/{total_pages})\n\n"

        for i, user in enumerate(page_users, start=start_idx + 1):
            try:
                # Sanitize username dengan escape markdown
                username = user.get('username', '') or "Tanpa username"

                if username != "Tanpa username":
                    # Clean username dari karakter bermasalah
                    username = clean_text_for_markdown(username)
                    if not username.startswith('@'):
                        username = f"@{username}"
                    username = f"`{username}`"  # Buat username bisa disalin
                else:
                    username = "Tanpa username"

                # Format time dengan error handling
                time_ago = format_time_ago(user.get('timestamp', ''))
                user_id = user.get('id', '')
                status_mark = user.get('status_mark', '')

                text += f"{i}\\. {username}{status_mark}\n"
                text += f"   ID: `{user_id}` - {time_ago}\n\n"
            except Exception as e:
                logging.warning(f"Error formatting user {i}: {e}")
                text += f"{i}\\. User error - waktu tidak diketahui\n"

        text += f"📊 Total: {len(users)} user"
        return text
    except Exception as e:
        logging.error(f"Error formatting user list page: {e}")
        return f"📋 **DAFTAR USER**\n\n❌ Error loading page {page}"


@router.callback_query(F.data == "admin_users")
async def show_user_menu(callback: types.CallbackQuery, state: FSMContext):
    """Menu kelola user"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_manage_users']:
        await callback.answer("❌ Anda tidak memiliki izin kelola user", show_alert=True)
        return

    user_text = "👥 **KELOLA USER**\n\nFitur manajemen yang tersedia:"

    # Gunakan safe functions untuk error handling
    await safe_edit_message(callback, user_text, create_user_menu_keyboard())
    await safe_answer_callback(callback)

    log_admin_activity(username, "Akses menu kelola user")


# Handler user_list dihapus - fitur daftar user sudah tidak digunakan


# Handler user_page pagination dihapus - fitur daftar user sudah tidak digunakan


def get_user_statistics():
    """Ambil statistik user"""
    try:
        users = get_all_users_paginated()
        total_users = len(users)
        
        # Hitung user dengan username
        users_with_username = len([u for u in users if u['username']])
        
        # Hitung user aktif dalam 24 jam terakhir
        current_time = int(time.time())
        day_ago = current_time - 86400
        
        recent_users = len([u for u in users if int(u['timestamp']) > day_ago])
        
        return {
            'total': total_users,
            'with_username': users_with_username,
            'recent_24h': recent_users
        }
    except Exception as e:
        logging.error(f"Error getting user statistics: {e}")
        return {'total': 0, 'with_username': 0, 'recent_24h': 0}
