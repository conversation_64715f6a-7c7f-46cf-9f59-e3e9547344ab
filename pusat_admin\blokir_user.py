"""
Blokir User Module - Blokir dan unblokir user
"""

import logging
import time
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_back_keyboard
from .utils import log_bot, parse_user_ids, parse_unblock_ids, clean_username, safe_edit_message, safe_answer_callback
from .database import db
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

router = Router()


async def block_user_with_message(user_id: str, block_message: str, admin_username: str = "") -> str:
    """Blokir user dengan pesan dan update status di total_users.txt"""
    try:
        from .kelola_user import block_user_in_file, get_all_users_paginated
        from .auth_admin import is_super_admin
        from aiogram import Bot
        from config import BOT_TOKEN

        # Cek apakah target adalah super admin (jika ada di database)
        all_users = get_all_users_paginated()
        user_data = next((user for user in all_users if user['id'] == user_id), None)

        if user_data:
            target_username = user_data.get('username', '')
            if target_username and is_super_admin(target_username):
                # Cek apakah admin yang memblokir adalah super admin
                if admin_username and not is_super_admin(admin_username):
                    logging.warning(f"Admin {admin_username} tried to block super admin {target_username}")
                    return "super_admin_block_denied"

            # Cek apakah sudah diblokir
            if user_data.get('is_blocked', False):
                return "already_blocked"

        # Simpan pesan blokir ke blocked_users.txt
        timestamp = str(int(time.time()))
        block_entry = f"{user_id}|{block_message}|{timestamp}|{admin_username}"

        # Simpan ke blocked_users.txt
        from .database import db
        db.append_line("blocked_users.txt", block_entry)

        # Update status di total_users.txt
        if user_data:
            # User ada di database, update status
            block_result = block_user_in_file(user_id)
        else:
            # User belum ada di database, buat entry baru dengan status blocked
            user_entry = f"{user_id}||||{timestamp}|blocked"
            db.append_line("total_users.txt", user_entry)
            block_result = True

        if block_result:
            # HAPUS dari active_users_cache.txt agar langsung hilang dari user aktif
            try:
                db.delete_by_field("active_users_cache.txt", 0, user_id)
                # logging.info(f"Removed blocked user {user_id} from active_users_cache.txt")  # Disabled
            except Exception as e:
                logging.warning(f"Error removing blocked user from active cache: {e}")

            # Kirim pesan blokir ke user dengan proper session management
            bot = None
            try:
                bot = Bot(token=BOT_TOKEN)
                await bot.send_message(
                    chat_id=int(user_id),
                    text=f"🚫 {block_message}\n\n📞 Untuk membuka blokir, hubungi admin: @KazuhaID1"
                )
                return "success_with_message"
            except Exception as e:
                logging.warning(f"Failed to send block message to {user_id}: {e}")
                return "success_no_message"
            finally:
                # Tutup session untuk menghindari unclosed client session
                if bot:
                    await bot.session.close()
        else:
            return "failed"

    except Exception as e:
        logging.error(f"Error blocking user {user_id}: {e}")
        return "error"


def block_user(user_id: str, admin_username: str = "") -> bool:
    """Fungsi lama untuk kompatibilitas - akan dihapus nanti"""
    try:
        from .kelola_user import block_user_in_file, get_all_users_paginated
        from .auth_admin import is_super_admin

        # Cek apakah user ada di database
        all_users = get_all_users_paginated()
        user_exists = any(user['id'] == user_id for user in all_users)

        if not user_exists:
            logging.warning(f"User {user_id} not found in database")
            return False

        # Cek apakah target adalah super admin
        user_data = next((user for user in all_users if user['id'] == user_id), None)
        if user_data:
            target_username = user_data.get('username', '')
            if target_username and is_super_admin(target_username):
                # Cek apakah admin yang memblokir adalah super admin
                if admin_username and not is_super_admin(admin_username):
                    logging.warning(f"Admin {admin_username} tried to block super admin {target_username}")
                    return "super_admin_block_denied"

        # Cek apakah sudah diblokir
        if user_data and user_data.get('is_blocked', False):
            return False

        # Update status di file total_users.txt
        return block_user_in_file(user_id)
    except Exception as e:
        logging.error(f"Error blocking user {user_id}: {e}")
        return False


def unblock_user(user_id: str) -> bool:
    """Unblokir user dengan update status di total_users.txt dan hapus dari blocked_users.txt"""
    try:
        from .kelola_user import unblock_user_in_file, get_all_users_paginated
        from .database import db

        # Cek apakah user ada di database dan diblokir
        all_users = get_all_users_paginated()
        user_data = next((user for user in all_users if user['id'] == user_id), None)

        if not user_data:
            logging.warning(f"User {user_id} not found in database")
            return False

        if not user_data.get('is_blocked', False):
            return False  # User tidak diblokir

        # Update status di file total_users.txt (kosongkan status)
        unblock_result = unblock_user_in_file(user_id)

        if unblock_result:
            # Hapus dari blocked_users.txt
            try:
                blocked_data = db.read_dict_data("blocked_users.txt")
                updated_lines = []

                for item in blocked_data:
                    parts = item['parts']
                    if len(parts) >= 1 and parts[0] != user_id:
                        # Bukan user yang di-unblock, simpan
                        updated_lines.append("|".join(parts))

                # Tulis kembali tanpa user yang di-unblock
                db.write_lines("blocked_users.txt", updated_lines)
                # logging.info(f"Removed user {user_id} from blocked_users.txt")  # Disabled

            except Exception as e:
                logging.warning(f"Error removing from blocked_users.txt: {e}")

            # TAMBAH kembali ke active_users_cache.txt jika user ada di total_users.txt
            try:
                if user_data:
                    current_timestamp = str(int(time.time()))
                    user_cache_entry = f"{user_id}|{user_data.get('username', '')}|{user_data.get('first_name', '')}|{user_data.get('last_name', '')}|{current_timestamp}"

                    # Cek apakah sudah ada di cache
                    existing_cache = db.find_by_field("active_users_cache.txt", 0, user_id)
                    if existing_cache:
                        # Update existing
                        db.update_by_field("active_users_cache.txt", 0, user_id, user_cache_entry)
                    else:
                        # Add new
                        db.append_line("active_users_cache.txt", user_cache_entry)

                    # logging.info(f"Added unblocked user {user_id} back to active_users_cache.txt")  # Disabled
            except Exception as e:
                logging.warning(f"Error adding unblocked user to active cache: {e}")

        return unblock_result
    except Exception as e:
        logging.error(f"Error unblocking user {user_id}: {e}")
        return False


def is_user_blocked(user_id: str) -> bool:
    """Cek apakah user diblokir berdasarkan status di total_users.txt"""
    try:
        from .database import db

        # Cek langsung dari total_users.txt
        user_data = db.find_by_field("total_users.txt", 0, user_id)
        if user_data and len(user_data['parts']) >= 6:
            status = user_data['parts'][5].strip().lower()
            return status == "blocked"
        return False
    except:
        return False


def get_user_block_message(user_id: str) -> str:
    """Ambil pesan blokir untuk user tertentu"""
    try:
        from .database import db

        # Cek di blocked_users.txt
        block_data = db.find_by_field("blocked_users.txt", 0, user_id)
        if block_data and len(block_data['parts']) >= 2:
            return block_data['parts'][1]  # Pesan blokir

        # Default message jika tidak ada pesan khusus
        return "Akses Anda ke bot telah diblokir oleh admin."
    except:
        return "Akses Anda ke bot telah diblokir oleh admin."


def get_blocked_users_list() -> list:
    """Ambil daftar user yang diblokir dari total_users.txt"""
    try:
        from .kelola_user import get_all_users_paginated
        all_users = get_all_users_paginated()
        blocked_users = [user['id'] for user in all_users if user.get('is_blocked', False)]
        return blocked_users
    except:
        return []


@router.callback_query(F.data == "user_block")
async def user_block_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk blokir user - input ID saja"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_block_users']:
        await callback.answer("❌ Anda tidak memiliki izin blokir user", show_alert=True)
        return

    block_text = """🚫 **BLOKIR USER**

Msukkan ID user yang ingin diblokir:"""

    try:
        await callback.message.edit_text(
            block_text,
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )
        await callback.answer()
    except Exception as e:
        if "message is not modified" in str(e).lower():
            await callback.answer("✅ Menu sudah terbaru")
        else:
            logging.error(f"Error editing block menu: {e}")
            await callback.answer("❌ Error loading menu", show_alert=True)
            return

    await state.set_state(AdminStates.waiting_block_user_id)
    await callback.answer()


@router.message(AdminStates.waiting_block_user_id, F.chat.type == "private")
async def process_block_user_id(message: types.Message, state: FSMContext):
    """Proses input ID user untuk diblokir"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    # Parse multiple IDs (dipisahkan oleh baris)
    user_ids_text = message.text.strip()
    user_ids = []

    for line in user_ids_text.split('\n'):
        user_id = line.strip()
        if user_id:
            # Validasi ID (harus numeric)
            if not user_id.isdigit():
                await message.answer(f"❌ ID tidak valid: {user_id}\nID user harus berupa angka.")
                return
            user_ids.append(user_id)

    if not user_ids:
        await message.answer("❌ Tidak ada ID user yang valid.")
        return

    # Cek user yang sudah diblokir
    already_blocked = []
    valid_ids = []

    for user_id in user_ids:
        if is_user_blocked(user_id):
            already_blocked.append(user_id)
        else:
            valid_ids.append(user_id)

    if not valid_ids:
        blocked_list = '\n'.join(already_blocked)
        await message.answer(f"❌ Semua user sudah diblokir:\n{blocked_list}")
        return

    # Simpan IDs ke state data
    await state.update_data(target_user_ids=valid_ids, already_blocked=already_blocked)

    # Tampilkan pilihan pesan
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📝 Pesan Default", callback_data="block_msg_default")],
        [InlineKeyboardButton(text="✏️ Tulis Pesan Custom", callback_data="block_msg_custom")],
        [InlineKeyboardButton(text="🔙 Kembali", callback_data="admin_users")]
    ])

    # Format tampilan IDs
    if len(valid_ids) == 1:
        ids_display = f"**ID User:** `{valid_ids[0]}`"
    else:
        ids_list = '\n'.join([f"`{uid}`" for uid in valid_ids])
        ids_display = f"**ID Users ({len(valid_ids)}):**\n{ids_list}"

    # Tampilkan info user yang sudah diblokir jika ada
    blocked_info = ""
    if already_blocked:
        blocked_list = '\n'.join([f"`{uid}`" for uid in already_blocked])
        blocked_info = f"\n\n⚠️ **Sudah diblokir ({len(already_blocked)}):**\n{blocked_list}"

    choice_text = f"""🚫 **BLOKIR USER**

{ids_display}{blocked_info}

Pilih jenis pesan blokir:"""

    await message.answer(
        choice_text,
        reply_markup=keyboard,
        parse_mode="Markdown"
    )

    await state.set_state(AdminStates.waiting_block_message_choice)


@router.callback_query(F.data == "block_msg_default")
async def use_default_block_message(callback: types.CallbackQuery, state: FSMContext):
    """Gunakan pesan default untuk blokir"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Ambil data dari state
    data = await state.get_data()
    user_ids = data.get('target_user_ids', [])

    if not user_ids:
        await callback.answer("❌ Data tidak valid", show_alert=True)
        await state.clear()
        return

    # Pesan default
    default_message = "Akses Anda ke bot telah diblokir oleh admin."

    # Proses blokir multiple users dengan pesan default
    await process_multiple_user_block(callback, user_ids, default_message, username, state)


@router.callback_query(F.data == "block_msg_custom")
async def prompt_custom_block_message(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk pesan custom"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    custom_text = """✏️ **PESAN BLOKIR CUSTOM**

Silakan masukkan pesan blokir yang akan dikirim ke user:
"""

    await callback.message.edit_text(
        custom_text,
        reply_markup=create_back_keyboard("admin_users"),
        parse_mode="Markdown"
    )

    await state.set_state(AdminStates.waiting_custom_block_message)
    await callback.answer()


@router.message(AdminStates.waiting_custom_block_message, F.chat.type == "private")
async def process_custom_block_message(message: types.Message, state: FSMContext):
    """Proses pesan custom untuk blokir"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    # Ambil data dari state
    data = await state.get_data()
    user_ids = data.get('target_user_ids', [])

    if not user_ids:
        await message.answer("❌ Data tidak valid")
        await state.clear()
        return

    custom_message = message.text.strip()

    if not custom_message:
        await message.answer("❌ Pesan tidak boleh kosong")
        return

    # Proses blokir multiple users dengan pesan custom
    await process_multiple_user_block_message(message, user_ids, custom_message, username, state)


async def process_user_block(callback_or_message, user_id: str, block_message: str, admin_username: str, state: FSMContext):
    """Helper function untuk proses blokir user"""
    try:
        # Tampilkan loading
        if hasattr(callback_or_message, 'message'):
            # Callback
            loading_msg = await callback_or_message.message.edit_text("🔄 Memblokir user...")
        else:
            # Message
            loading_msg = await callback_or_message.answer("🔄 Memblokir user...")

        # Proses blokir
        result = await block_user_with_message(user_id, block_message, admin_username)

        # Format hasil
        if result == "success_with_message":
            result_text = f"""✅ **User berhasil diblokir**

**ID User:** `{user_id}`
**Pesan:** {block_message}
**Status:** Pesan terkirim ke user"""
        elif result == "success_no_message":
            result_text = f"""⚠️ **User berhasil diblokir**

**ID User:** `{user_id}`
**Pesan:** {block_message}
**Status:** Pesan tidak terkirim (user mungkin memblokir bot)"""
        elif result == "already_blocked":
            result_text = f"""❌ **User sudah diblokir**

**ID User:** `{user_id}`"""
        elif result == "super_admin_block_denied":
            result_text = f"""❌ **Tidak dapat memblokir**

**ID User:** `{user_id}`
**Alasan:** Target adalah super admin"""
        else:
            result_text = f"""❌ **Gagal memblokir user**

**ID User:** `{user_id}`
**Error:** {result}"""

        # Update message
        await loading_msg.edit_text(
            result_text,
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )

        # Log aktivitas
        log_admin_activity(admin_username, f"Blokir user {user_id}: {result}")

    except Exception as e:
        logging.error(f"Error in process_user_block: {e}")
        error_text = f"❌ **Error memblokir user**\n\n{str(e)}"

        if hasattr(callback_or_message, 'message'):
            await callback_or_message.message.edit_text(
                error_text,
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="Markdown"
            )
        else:
            await callback_or_message.answer(
                error_text,
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="Markdown"
            )

    await state.clear()


async def process_user_block_message(message, user_id: str, block_message: str, admin_username: str, state: FSMContext):
    """Helper function untuk proses blokir user dari message"""
    await process_user_block(message, user_id, block_message, admin_username, state)


async def process_multiple_user_block(callback_or_message, user_ids: list, block_message: str, admin_username: str, state: FSMContext):
    """Helper function untuk proses blokir multiple users"""
    try:
        # Tampilkan loading
        if hasattr(callback_or_message, 'message'):
            # Callback
            loading_msg = await callback_or_message.message.edit_text("🔄 Memblokir users...")
        else:
            # Message
            loading_msg = await callback_or_message.answer("🔄 Memblokir users...")

        # Proses blokir setiap user
        success_count = 0
        failed_count = 0
        already_blocked_count = 0
        super_admin_denied_count = 0
        message_failed_count = 0

        results = []

        for user_id in user_ids:
            result = await block_user_with_message(user_id, block_message, admin_username)

            if result == "success_with_message":
                success_count += 1
                results.append(f"✅ `{user_id}` - Berhasil (pesan terkirim)")
            elif result == "success_no_message":
                success_count += 1
                message_failed_count += 1
                results.append(f"⚠️ `{user_id}` - Berhasil (pesan tidak terkirim)")
            elif result == "already_blocked":
                already_blocked_count += 1
                results.append(f"❌ `{user_id}` - Sudah diblokir")
            elif result == "super_admin_block_denied":
                super_admin_denied_count += 1
                results.append(f"❌ `{user_id}` - Super admin")
            else:
                failed_count += 1
                results.append(f"❌ `{user_id}` - Gagal")

        # Format hasil
        total_users = len(user_ids)
        result_text = f"""✅ **BLOKIR SELESAI**

**Total Users:** {total_users}
**Berhasil:** {success_count}
**Gagal:** {failed_count}
**Sudah Diblokir:** {already_blocked_count}"""

        # Tambahkan info tambahan hanya jika ada
        if super_admin_denied_count > 0:
            result_text += f"\n**Super Admin:** {super_admin_denied_count}"
        if message_failed_count > 0:
            result_text += f"\n**Pesan Tidak Terkirim:** {message_failed_count}"

        result_text += f"\n\n**Detail:**\n" + '\n'.join(results[:10])  # Batasi 10 hasil pertama

        if len(results) > 10:
            result_text += f"\n... dan {len(results) - 10} lainnya"

        # Update message
        await loading_msg.edit_text(
            result_text,
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )

        # Log aktivitas
        log_admin_activity(admin_username, f"Blokir {total_users} users: {success_count} berhasil, {failed_count} gagal")

    except Exception as e:
        logging.error(f"Error in process_multiple_user_block: {e}")
        error_text = f"❌ **Error memblokir users**\n\n{str(e)}"

        if hasattr(callback_or_message, 'message'):
            await callback_or_message.message.edit_text(
                error_text,
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="Markdown"
            )
        else:
            await callback_or_message.answer(
                error_text,
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="Markdown"
            )

    await state.clear()


async def process_multiple_user_block_message(message, user_ids: list, block_message: str, admin_username: str, state: FSMContext):
    """Helper function untuk proses blokir multiple users dari message"""
    await process_multiple_user_block(message, user_ids, block_message, admin_username, state)





@router.message(AdminStates.waiting_block_user_ids, F.chat.type == "private")
async def process_block_users_old(message: types.Message, state: FSMContext):
    """Proses blokir user"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    user_inputs = parse_user_ids(message.text)
    
    if not user_inputs:
        await message.answer("❌ Format tidak valid. Masukkan user ID atau username yang valid.")
        return

    # Resolve usernames ke user IDs
    target_user_ids = []
    not_found = []
    
    for user_input in user_inputs:
        if user_input.startswith('@'):
            # Username - cari di database
            username_clean = clean_username(user_input)
            from .kelola_user import get_all_users_paginated
            all_users = get_all_users_paginated()
            found = False
            for user in all_users:
                if user['username'].lower() == username_clean.lower():
                    target_user_ids.append(user['id'])
                    found = True
                    break
            if not found:
                not_found.append(user_input)
        elif user_input.isdigit():
            # User ID langsung
            target_user_ids.append(user_input)
        else:
            not_found.append(user_input)

    if not target_user_ids:
        await message.answer(f"❌ User tidak ditemukan:\n{chr(10).join(not_found)}")
        return

    # Proses blokir
    blocked = 0
    already_blocked = 0
    failed = 0
    super_admin_denied = 0

    for user_id in target_user_ids:
        if is_user_blocked(user_id):
            already_blocked += 1  # Sudah diblokir, tidak dihitung sebagai gagal
        else:
            result = block_user(user_id, username)  # Pass admin username
            if result == "super_admin_block_denied":
                super_admin_denied += 1
            elif result:
                blocked += 1
            else:
                failed += 1

    # Hasil sesuai format yang diminta - user yang sudah diblokir tidak masuk ke gagal
    total_failed = failed + len(not_found)  # Hanya yang benar-benar gagal dan tidak ditemukan
    result_text = f"""✅ **Blokir selesai**

📊 Berhasil: {blocked} user
📊 Gagal: {total_failed} user"""

    # Tambahkan pesan khusus jika ada super admin yang dicoba diblokir
    if super_admin_denied > 0:
        result_text += f"\n\n⚠️ **{super_admin_denied} Super Admin tidak bisa diblokir**"

    # Tambah info jika ada yang sudah diblokir
    if already_blocked > 0:
        result_text += f"\n⚠️ Sudah diblokir: {already_blocked} user"

    await message.answer(
        result_text,
        reply_markup=create_back_keyboard("admin_users"),
        parse_mode="Markdown"
    )

    await state.clear()
    log_admin_activity(username, f"Blokir {blocked} user")


@router.callback_query(F.data == "user_unblock")
async def user_unblock_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk unblokir user"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_block_users']:
        await callback.answer("❌ Anda tidak memiliki izin unblokir user", show_alert=True)
        return
    
    # Tampilkan daftar user yang diblokir dari total_users.txt
    blocked_users = get_blocked_users_list()

    if not blocked_users:
        await callback.message.edit_text(
            "✅ **BUKA BLOKIR**\n\n❌ Tidak ada user yang diblokir",
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )
        await callback.answer()
        return

    # Tidak perlu ambil data user karena hanya tampilkan ID

    unblock_text = f"""✅ **BUKA BLOKIR**
    
Masukkan ID user yang ingin dibuka:

{len(blocked_users)} user diblokir:"""

    # Tampilkan daftar user yang diblokir dengan format: ID saja
    for i, user_id in enumerate(blocked_users[:10], 1):
        # Hanya tampilkan ID saja
        unblock_text += f"\n{i}. `{user_id}`"

    if len(blocked_users) > 10:
        unblock_text += f"\n... dan {len(blocked_users) - 10} user lainnya"

    try:
        await callback.message.edit_text(
            unblock_text,
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )
        await callback.answer()
    except Exception as e:
        if "message is not modified" in str(e).lower():
            await callback.answer("✅ Menu sudah terbaru")
        else:
            logging.error(f"Error editing unblock menu: {e}")
            await callback.answer("❌ Error loading menu", show_alert=True)
            return
    
    await state.set_state(AdminStates.waiting_unblock_user_ids)
    await callback.answer()


@router.message(AdminStates.waiting_unblock_user_ids, F.chat.type == "private")
async def process_unblock_users(message: types.Message, state: FSMContext):
    """Proses unblokir user"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    user_inputs = parse_unblock_ids(message.text)
    
    if not user_inputs:
        await message.answer("❌ Format tidak valid. Masukkan user ID yang valid.")
        return

    # Proses unblokir - Hanya ID yang diterima
    unblocked = 0
    not_blocked = 0
    failed = 0

    for user_input in user_inputs:
        user_input = user_input.strip()
        if user_input.isdigit():
            if not is_user_blocked(user_input):
                not_blocked += 1  # ID tidak ada di daftar blokir
            elif unblock_user(user_input):
                unblocked += 1
            else:
                failed += 1
        else:
            # Hanya ID yang diterima untuk unblokir
            failed += 1

    # Hasil sesuai format yang diminta - pisahkan yang tidak ada di daftar
    result_text = f"""✅ **Buka blokir selesai**

📊 Berhasil: {unblocked} user
📊 Gagal: {failed} user"""

    # Tambah info jika ada yang tidak ada di daftar blokir
    if not_blocked > 0:
        result_text += f"\n⚠️ Tidak ada di daftar: {not_blocked} user"

    await message.answer(
        result_text,
        reply_markup=create_back_keyboard("admin_users"),
        parse_mode="Markdown"
    )

    await state.clear()
    log_admin_activity(username, f"Unblokir {unblocked} user")


@router.callback_query(F.data == "blocked_users_list")
async def show_blocked_users_list(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan daftar user yang diblokir"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions['can_block_users']:
        await callback.answer("❌ Anda tidak memiliki izin lihat user diblokir", show_alert=True)
        return

    # Tampilkan loading
    await callback.message.edit_text("📋 **USER DIBLOKIR**\n\n🔄 Loading...")

    try:
        blocked_users = get_blocked_users_list()

        if not blocked_users:
            await callback.message.edit_text(
                "📋 **USER DIBLOKIR**\n\n✅ Tidak ada user yang diblokir",
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="Markdown"
            )
            await callback.answer()
            return

        # Format daftar user diblokir - SIMPLE
        text = f"📋 **USER DIBLOKIR** ({len(blocked_users)} user)\n\n"

        # Tampilkan maksimal 20 user
        for i, user_id in enumerate(blocked_users[:20], 1):
            text += f"{i}. `{user_id}`\n"

        if len(blocked_users) > 20:
            text += f"\n... dan {len(blocked_users) - 20} user lainnya"

        await callback.message.edit_text(
            text,
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )

        log_admin_activity(username, f"Lihat daftar user diblokir ({len(blocked_users)} user)")

    except Exception as e:
        logging.error(f"Error showing blocked users: {e}")
        await callback.message.edit_text(
            "❌ **Error**\n\nGagal memuat daftar user diblokir.",
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="Markdown"
        )

    await callback.answer()
