"""
Autentikasi dan Permission Management untuk Admin
"""

import logging
import os
import time
from pusat_admin.admin_config import SUPER_ADMIN_USERNAMES
from .database import db


def get_admin_level(username: str) -> str:
    """Ambil level admin dari database"""
    if not username:
        return ""

    # Cek super admin dari config
    if username.lower() in [u.lower() for u in SUPER_ADMIN_USERNAMES]:
        return "super_admin"

    # Cek dari database admin
    admin_data = db.find_by_field("admins.txt", 1, username)  # field 1 = username
    if admin_data and len(admin_data['parts']) >= 3:
        return admin_data['parts'][2]  # field 2 = level

    return ""


def get_admin_level_by_id(user_id: int) -> str:
    """Ambil level admin berdasarkan user ID"""
    # Cek dari database admin berdasarkan user ID terlebih dahulu
    admin_data = db.find_by_field("admins.txt", 0, str(user_id))  # field 0 = user_id
    if admin_data and len(admin_data['parts']) >= 3:
        return admin_data['parts'][2]  # field 2 = level

    # Cek dari database user untuk mendapatkan username berdasarkan ID
    # Coba dari berbagai database user yang mungkin ada
    username_from_id = ""

    # Coba dari total_users.txt
    user_data = db.find_by_field("total_users.txt", 0, str(user_id))
    if user_data and len(user_data['parts']) >= 2:
        username_from_id = user_data['parts'][1]  # field 1 = username

    # Jika tidak ada, coba dari users.txt (fallback)
    if not username_from_id:
        user_data = db.find_by_field("users.txt", 0, str(user_id))
        if user_data and len(user_data['parts']) >= 2:
            username_from_id = user_data['parts'][1]  # field 1 = username

    # Cek apakah username adalah super admin dari config
    if username_from_id and username_from_id in SUPER_ADMIN_USERNAMES:
        return "super_admin"

    # Cek berdasarkan username jika ada
    if username_from_id:
        admin_data = db.find_by_field("admins.txt", 1, username_from_id)  # field 1 = username
        if admin_data and len(admin_data['parts']) >= 3:
            return admin_data['parts'][2]

    return ""


def is_super_admin(username: str) -> bool:
    """Cek apakah user adalah super admin"""
    return get_admin_level(username) == "super_admin"


def is_admin(username: str) -> bool:
    """Cek apakah user adalah admin (termasuk super admin)"""
    level = get_admin_level(username)
    return level in ["admin", "super_admin"]


def is_super_admin_by_id(user_id: int) -> bool:
    """Cek apakah user adalah super admin berdasarkan ID"""
    return get_admin_level_by_id(user_id) == "super_admin"


def is_admin_by_id(user_id: int) -> bool:
    """Cek apakah user adalah admin berdasarkan ID"""
    level = get_admin_level_by_id(user_id)
    return level in ["admin", "super_admin"]


def get_admin_list():
    """Ambil daftar admin dari database dengan level"""
    admins = []

    # Tambahkan super admin dari config
    for username in SUPER_ADMIN_USERNAMES:
        admins.append({
            "user_id": "",  # Tidak ada ID untuk config admin
            "username": username,
            "level": "super_admin",
            "timestamp": "",
            "source": "config"
        })

    # Tambahkan admin dari database
    admin_data = db.read_dict_data("admins.txt")
    for item in admin_data:
        parts = item['parts']
        if len(parts) >= 4:
            user_id = parts[0]
            username = parts[1]
            level = parts[2]
            timestamp = parts[3]
            
            # Skip jika username sudah ada dari config
            if username not in SUPER_ADMIN_USERNAMES:
                admins.append({
                    "user_id": user_id,
                    "username": username,
                    "level": level,
                    "timestamp": timestamp,
                    "source": "database"
                })

    return admins


def save_admin_to_database(user_id: str, username: str, level: str = "admin") -> bool:
    """Simpan admin baru ke database"""
    timestamp = str(int(time.time()))
    admin_entry = f"{user_id}|{username}|{level}|{timestamp}"
    
    # Cek apakah admin sudah ada
    existing = db.find_by_field("admins.txt", 1, username)  # field 1 = username
    if existing:
        # Update existing admin
        return db.update_by_field("admins.txt", 1, username, admin_entry)
    else:
        # Tambah admin baru
        return db.append_line("admins.txt", admin_entry)


def remove_admin_from_database(username: str) -> bool:
    """Hapus admin dari database"""
    # Tidak bisa hapus super admin dari config
    if username in SUPER_ADMIN_USERNAMES:
        return False
    
    return db.delete_by_field("admins.txt", 1, username)  # field 1 = username


def can_manage_admin(current_admin_username: str, target_admin_username: str) -> bool:
    """Cek apakah admin bisa mengelola admin lain"""
    current_level = get_admin_level(current_admin_username)
    target_level = get_admin_level(target_admin_username)
    
    # Super admin bisa mengelola semua
    if current_level == "super_admin":
        return True
    
    # Admin biasa tidak bisa mengelola super admin
    if target_level == "super_admin":
        return False
    
    # Admin biasa bisa mengelola admin biasa lain
    return current_level == "admin"


def log_admin_activity(admin_username: str, activity: str):
    """Log aktivitas admin - disabled for cleaner terminal"""
    pass


def get_admin_permissions(username: str) -> dict:
    """Get permission admin"""
    level = get_admin_level(username)
    
    if level == "super_admin":
        return {
            "can_broadcast": True,
            "can_manage_users": True,
            "can_manage_admins": True,
            "can_control_bot": True,
            "can_view_files": True,
            "can_block_users": True,
            "can_clear_data": True
        }
    elif level == "admin":
        return {
            "can_broadcast": True,
            "can_manage_users": True,
            "can_manage_admins": True,   # Admin biasa bisa kelola admin (dengan batasan)
            "can_control_bot": True,     # Admin biasa bisa kontrol bot
            "can_view_files": True,
            "can_block_users": True,
            "can_clear_data": True,
            "can_block_super_admin": False,  # TIDAK bisa blokir super admin
            "can_remove_super_admin": False  # TIDAK bisa hapus super admin
        }
    else:
        return {
            "can_broadcast": False,
            "can_manage_users": False,
            "can_manage_admins": False,
            "can_control_bot": False,
            "can_view_files": False,
            "can_block_users": False,
            "can_clear_data": False
        }
