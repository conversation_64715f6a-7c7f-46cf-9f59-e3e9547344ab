"""
FSM States untuk semua fitur admin
"""

from aiogram.fsm.state import StatesGroup, State


class AdminStates(StatesGroup):
    """States untuk semua fitur admin"""
    
    # Main menu
    main_menu = State()
    
    # Broadcast states
    waiting_broadcast_message = State()
    
    # Admin management states
    waiting_admin_username = State()
    waiting_remove_admin_username = State()
    
    # User management states
    waiting_user_ids_message = State()
    waiting_message_for_users = State()
    waiting_block_user_ids = State()
    waiting_block_user_id = State()
    waiting_block_message_choice = State()
    waiting_custom_block_message = State()
    waiting_unblock_user_ids = State()
    waiting_check_user_info = State()
    
    # Bot control states
    waiting_maintenance_message = State()
    
    # Search states
    waiting_search_query = State()
    
    # User details states
    viewing_user_details = State()
    viewing_active_users = State()
    viewing_total_users = State()

    # File management states
    managing_files = State()

    # Free premium states
    waiting_free_premium_users = State()
    waiting_free_premium_duration = State()
    waiting_free_premium_confirm = State()
