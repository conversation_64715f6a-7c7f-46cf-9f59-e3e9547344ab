import asyncio
import logging
from typing import Dict, Set

class UserIsolationManager:
    """
    Manager untuk isolasi processing per user menggunakan semaphore.
    Mencegah user melakukan multiple concurrent processing.
    """
    
    def __init__(self):
        self._user_semaphores: Dict[int, asyncio.Semaphore] = {}
        self._lock = asyncio.Lock()
        # Tracking users yang sedang dalam mode pengiriman
        self._users_in_sending_mode: Set[int] = set()
        self._sending_lock = asyncio.Lock()
        # Tracking cancel flags per user
        self._user_cancel_flags: Dict[int, bool] = {}
        self._cancel_lock = asyncio.Lock()
    
    async def get_user_semaphore(self, user_id: int) -> asyncio.Semaphore:
        """
        Ambil atau buat semaphore untuk user tertentu.
        Setiap user hanya bisa melakukan 1 proses file processing pada satu waktu.
        
        Args:
            user_id: ID user Telegram
            
        Returns:
            asyncio.Semaphore: Semaphore untuk user tersebut
        """
        async with self._lock:
            if user_id not in self._user_semaphores:
                # Buat semaphore baru dengan limit 1 (hanya 1 proses per user)
                self._user_semaphores[user_id] = asyncio.Semaphore(1)
                # logging.info(f"Created new semaphore for user {user_id}")  # Reduced logging
            
            return self._user_semaphores[user_id]
    
    async def acquire_user_lock(self, user_id: int) -> bool:
        """
        Coba acquire lock untuk user. Return True jika berhasil, False jika user sedang busy.
        
        Args:
            user_id: ID user Telegram
            
        Returns:
            bool: True jika berhasil acquire, False jika user sedang busy
        """
        semaphore = await self.get_user_semaphore(user_id)
        
        # Coba acquire tanpa blocking
        if semaphore.locked():
            return False
        
        # Acquire dengan timeout sangat kecil untuk non-blocking check
        try:
            await asyncio.wait_for(semaphore.acquire(), timeout=0.001)
            return True
        except asyncio.TimeoutError:
            return False
    
    def release_user_lock(self, user_id: int):
        """
        Release lock untuk user.

        Args:
            user_id: ID user Telegram
        """
        if user_id in self._user_semaphores:
            semaphore = self._user_semaphores[user_id]
            if semaphore.locked():
                semaphore.release()
                # logging.info(f"Released lock for user {user_id}")  # Reduced logging

    def force_release_user_lock(self, user_id: int):
        """
        Force release semaphore untuk user yang stuck (silent operation).

        Args:
            user_id: ID user Telegram
        """
        if user_id in self._user_semaphores:
            semaphore = self._user_semaphores[user_id]
            try:
                # Force release tanpa cek locked() untuk avoid race condition
                semaphore.release()
                # logging.warning(f"Force released stuck semaphore for user {user_id}")  # Reduced logging
            except ValueError:
                # Semaphore sudah released, ignore
                pass
    
    async def cleanup_inactive_users(self, max_inactive_users: int = 1000):
        """
        Cleanup semaphore untuk user yang tidak aktif untuk menghemat memory.
        
        Args:
            max_inactive_users: Maksimal jumlah user semaphore yang disimpan
        """
        async with self._lock:
            if len(self._user_semaphores) > max_inactive_users:
                # Hapus semaphore yang tidak sedang digunakan
                inactive_users = []
                for user_id, semaphore in self._user_semaphores.items():
                    if not semaphore.locked():
                        inactive_users.append(user_id)
                
                # Hapus setengah dari inactive users
                users_to_remove = inactive_users[:len(inactive_users)//2]
                for user_id in users_to_remove:
                    del self._user_semaphores[user_id]
                
                logging.info(f"Cleaned up {len(users_to_remove)} inactive user semaphores")

    async def set_user_sending_mode(self, user_id: int, is_sending: bool):
        """
        Set/unset user dalam mode pengiriman.

        Args:
            user_id: ID user Telegram
            is_sending: True untuk set sending mode, False untuk unset
        """
        async with self._sending_lock:
            if is_sending:
                self._users_in_sending_mode.add(user_id)
                # logging.info(f"User {user_id} entered sending mode")  # Disabled - tidak perlu log
            else:
                self._users_in_sending_mode.discard(user_id)
                # logging.info(f"User {user_id} exited sending mode")  # Disabled - tidak perlu log

    def is_user_in_sending_mode(self, user_id: int) -> bool:
        """
        Check apakah user sedang dalam mode pengiriman.

        Args:
            user_id: ID user Telegram

        Returns:
            bool: True jika user sedang dalam sending mode
        """
        return user_id in self._users_in_sending_mode

    async def cleanup_sending_mode(self, max_users: int = 100):
        """
        Cleanup sending mode tracking untuk menghemat memory.

        Args:
            max_users: Maksimal jumlah user yang di-track
        """
        async with self._sending_lock:
            if len(self._users_in_sending_mode) > max_users:
                # Clear semua sending mode (emergency cleanup)
                cleared_count = len(self._users_in_sending_mode)
                self._users_in_sending_mode.clear()
                logging.warning(f"Emergency cleanup: cleared {cleared_count} users from sending mode")

    async def set_user_cancel_flag(self, user_id: int, cancelled: bool):
        """
        Set/unset cancel flag untuk user.

        Args:
            user_id: ID user Telegram
            cancelled: True untuk set cancel, False untuk unset
        """
        async with self._cancel_lock:
            if cancelled:
                self._user_cancel_flags[user_id] = True
                # logging.info(f"User {user_id} set cancel flag")  # Disabled - tidak perlu log
            else:
                self._user_cancel_flags.pop(user_id, None)
                # logging.info(f"User {user_id} cleared cancel flag")  # Disabled - tidak perlu log

    def is_user_cancelled(self, user_id: int) -> bool:
        """
        Check apakah user sudah cancel pengiriman.

        Args:
            user_id: ID user Telegram

        Returns:
            bool: True jika user sudah cancel
        """
        return self._user_cancel_flags.get(user_id, False)

    async def cleanup_cancel_flags(self, max_users: int = 100):
        """
        Cleanup cancel flags untuk menghemat memory.

        Args:
            max_users: Maksimal jumlah cancel flags yang di-track
        """
        async with self._cancel_lock:
            if len(self._user_cancel_flags) > max_users:
                # Clear semua cancel flags (emergency cleanup)
                cleared_count = len(self._user_cancel_flags)
                self._user_cancel_flags.clear()
                logging.warning(f"Emergency cleanup: cleared {cleared_count} cancel flags")

# Global instance
user_isolation_manager = UserIsolationManager()

async def with_user_isolation(user_id: int, func, *args, **kwargs):
    """
    Decorator function untuk menjalankan fungsi dengan user isolation.
    
    Args:
        user_id: ID user Telegram
        func: Function yang akan dijalankan
        *args, **kwargs: Arguments untuk function
        
    Returns:
        Tuple[bool, Any]: (success, result)
    """
    # Coba acquire lock
    if not await user_isolation_manager.acquire_user_lock(user_id):
        return False, "User sedang memproses file lain. Tunggu hingga selesai."
    
    try:
        # Jalankan function
        result = await func(*args, **kwargs)
        return True, result
    finally:
        # Selalu release lock
        user_isolation_manager.release_user_lock(user_id)

def is_user_busy(user_id: int) -> bool:
    """
    Check apakah user sedang busy processing.

    Args:
        user_id: ID user Telegram

    Returns:
        bool: True jika user sedang busy
    """
    if user_id not in user_isolation_manager._user_semaphores:
        return False

    return user_isolation_manager._user_semaphores[user_id].locked()

def force_release_user_lock(user_id: int):
    """
    Global function untuk force release semaphore user yang stuck.

    Args:
        user_id: ID user Telegram
    """
    user_isolation_manager.force_release_user_lock(user_id)

# Global functions untuk sending mode
async def set_user_sending_mode(user_id: int, is_sending: bool):
    """
    Global function untuk set/unset user sending mode.

    Args:
        user_id: ID user Telegram
        is_sending: True untuk set sending mode, False untuk unset
    """
    await user_isolation_manager.set_user_sending_mode(user_id, is_sending)

def is_user_in_sending_mode(user_id: int) -> bool:
    """
    Global function untuk check apakah user dalam sending mode.

    Args:
        user_id: ID user Telegram

    Returns:
        bool: True jika user sedang dalam sending mode
    """
    return user_isolation_manager.is_user_in_sending_mode(user_id)

# Global functions untuk cancel flags
async def set_user_cancel_flag(user_id: int, cancelled: bool):
    """
    Global function untuk set/unset cancel flag.

    Args:
        user_id: ID user Telegram
        cancelled: True untuk set cancel, False untuk unset
    """
    await user_isolation_manager.set_user_cancel_flag(user_id, cancelled)

def is_user_cancelled(user_id: int) -> bool:
    """
    Global function untuk check apakah user sudah cancel.

    Args:
        user_id: ID user Telegram

    Returns:
        bool: True jika user sudah cancel
    """
    return user_isolation_manager.is_user_cancelled(user_id)
