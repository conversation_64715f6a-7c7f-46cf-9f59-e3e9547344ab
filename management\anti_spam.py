"""
Anti-Spam System untuk Grup
Deteksi dan hapus pesan duplikat dari user yang sama dalam 5 menit
- <PERSON>esan pertama: di<PERSON>inkan
- <PERSON>esan duplikat dari user sama dalam 5 menit: dihapus
- Pesan sama dari user berbeda: diizinkan
- Admin grup: pengecualian (boleh duplikat)
"""

import hashlib
import json
import os
import time
from typing import Dict, Optional
from aiogram import Router, types
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError
from utils.text_cleaner import ultimate_text_cleaner, extract_clean_text_from_message

router = Router()

# Konfigurasi anti-spam
SPAM_CONFIG = {
    "time_window": 300,        # 5 menit = 300 detik
    "case_sensitive": False,   # Tidak case-sensitive ("Halo" = "halo")
    "ignore_whitespace": True, # Ignore extra spaces
    "min_length": 1,          # Min 1 karakter untuk di-track (semua pesan)
    "cleanup_interval": 600,   # Cleanup setiap 10 menit
    "admin_immunity": True,    # Admin bebas duplikat
}

# Path database
DATABASE_DIR = "management/database"
SPAM_TRACKING_FILE = os.path.join(DATABASE_DIR, "spam_tracking.txt")

# Ensure database directory exists
os.makedirs(DATABASE_DIR, exist_ok=True)

class AntiSpamManager:
    """Manager untuk anti-spam duplicate message detection"""
    
    def __init__(self):
        self.tracking_data: Dict[str, Dict[str, float]] = {}
        self.last_cleanup = time.time()
        self._load_tracking_data()
    
    def _load_tracking_data(self):
        """Load tracking data dari file"""
        try:
            if os.path.exists(SPAM_TRACKING_FILE):
                with open(SPAM_TRACKING_FILE, 'r', encoding='utf-8') as f:
                    self.tracking_data = json.load(f)
                # Cleanup data expired saat load
                self._cleanup_expired_data()
        except Exception as e:
            # Silent error - tidak log untuk menjaga terminal rapi
            self.tracking_data = {}
    
    def _save_tracking_data(self):
        """Save tracking data ke file"""
        try:
            with open(SPAM_TRACKING_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.tracking_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # Silent error - tidak log untuk menjaga terminal rapi
            pass
    
    def _cleanup_expired_data(self):
        """Cleanup data yang sudah expired"""
        current_time = time.time()
        time_window = SPAM_CONFIG["time_window"]
        
        # Cleanup per user
        users_to_remove = []
        for user_id, messages in self.tracking_data.items():
            # Cleanup expired messages untuk user ini
            expired_hashes = []
            for msg_hash, timestamp in messages.items():
                if current_time - timestamp > time_window:
                    expired_hashes.append(msg_hash)
            
            # Remove expired messages
            for msg_hash in expired_hashes:
                del messages[msg_hash]
            
            # Remove user jika tidak ada message yang di-track
            if not messages:
                users_to_remove.append(user_id)
        
        # Remove empty users
        for user_id in users_to_remove:
            del self.tracking_data[user_id]
        
        self.last_cleanup = current_time
    
    def _normalize_message(self, text: str, entities: list = None) -> str:
        """
        Enhanced message normalization dengan comprehensive text cleaning

        Args:
            text: Text yang akan dinormalisasi
            entities: List of MessageEntity dari Telegram

        Returns:
            str: Text yang sudah dinormalisasi
        """
        if not text:
            return ""

        # Enhanced text cleaning untuk menghapus semua formatting
        cleaned_text = ultimate_text_cleaner(text, entities)

        # Convert case jika tidak case-sensitive
        if not SPAM_CONFIG["case_sensitive"]:
            cleaned_text = cleaned_text.lower()

        # Remove extra whitespace jika ignore_whitespace
        if SPAM_CONFIG["ignore_whitespace"]:
            cleaned_text = " ".join(cleaned_text.split())

        return cleaned_text.strip()
    
    def _hash_message(self, text: str, entities: list = None) -> str:
        """
        Generate hash untuk message dengan enhanced normalization

        Args:
            text: Text yang akan di-hash
            entities: List of MessageEntity dari Telegram

        Returns:
            str: Hash dari normalized text
        """
        normalized = self._normalize_message(text, entities)
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()[:16]
    
    def is_duplicate_message(self, user_id: int, text: str, entities: list = None) -> bool:
        """
        Enhanced duplicate detection dengan comprehensive text cleaning

        Args:
            user_id: ID user Telegram
            text: Text message
            entities: List of MessageEntity dari Telegram

        Returns:
            bool: True jika duplikat, False jika tidak
        """
        # Skip jika text kosong (min_length = 1, jadi semua pesan di-track)
        if not text or len(text.strip()) < SPAM_CONFIG["min_length"]:
            return False

        # Cleanup berkala
        current_time = time.time()
        if current_time - self.last_cleanup > SPAM_CONFIG["cleanup_interval"]:
            self._cleanup_expired_data()

        # Generate hash message dengan enhanced cleaning
        msg_hash = self._hash_message(text, entities)
        user_id_str = str(user_id)

        # Cek apakah user sudah ada di tracking
        if user_id_str not in self.tracking_data:
            # User baru - tidak duplikat
            self.tracking_data[user_id_str] = {msg_hash: current_time}
            self._save_tracking_data()
            return False

        user_messages = self.tracking_data[user_id_str]

        # Cek apakah hash message sudah ada dalam time window
        if msg_hash in user_messages:
            last_time = user_messages[msg_hash]
            if current_time - last_time <= SPAM_CONFIG["time_window"]:
                # Duplikat dalam time window
                return True

        # Tidak duplikat - simpan message baru
        user_messages[msg_hash] = current_time
        self._save_tracking_data()
        return False
    
    def get_stats(self) -> Dict:
        """Get statistik anti-spam"""
        total_users = len(self.tracking_data)
        total_messages = sum(len(messages) for messages in self.tracking_data.values())
        
        return {
            "total_users_tracked": total_users,
            "total_messages_tracked": total_messages,
            "time_window_minutes": SPAM_CONFIG["time_window"] // 60,
            "last_cleanup": self.last_cleanup
        }

# Global instance
anti_spam_manager = AntiSpamManager()

async def is_group_admin(message: types.Message, user_id: int) -> bool:
    """
    Mengecek apakah user adalah admin di grup
    
    Args:
        message (types.Message): Message object untuk akses bot
        user_id (int): ID user yang akan dicek
        
    Returns:
        bool: True jika user adalah admin, False jika tidak
    """
    try:
        chat_member = await message.bot.get_chat_member(message.chat.id, user_id)
        return chat_member.status in ["administrator", "creator"]
    except Exception as e:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False

async def delete_message_safely(message: types.Message) -> bool:
    """
    Menghapus message dengan error handling
    
    Args:
        message (types.Message): Message yang akan dihapus
        
    Returns:
        bool: True jika berhasil dihapus, False jika gagal
    """
    try:
        await message.delete()
        return True
    except TelegramBadRequest as e:
        if "message to delete not found" in str(e).lower():
            # Message sudah dihapus
            return False
        elif "not enough rights" in str(e).lower():
            # Bot tidak punya permission untuk hapus - silent
            return False
        else:
            # Silent error - tidak log untuk menjaga terminal rapi
            return False
    except TelegramForbiddenError:
        # Bot tidak bisa hapus pesan - mungkin di-kick atau di-ban dari grup - silent
        return False
    except Exception as e:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False

def extract_text_from_message(message: types.Message) -> Optional[str]:
    """
    Extract text dari message (text atau caption)
    
    Args:
        message: Message object
        
    Returns:
        str: Text dari message, atau None jika tidak ada
    """
    if message.text:
        return message.text
    elif message.caption:
        return message.caption
    return None

async def check_duplicate_spam(message: types.Message) -> bool:
    """
    Enhanced duplicate spam detection dengan comprehensive text cleaning

    Args:
        message: Message object

    Returns:
        bool: True jika message dihapus karena spam, False jika diizinkan
    """
    # Enhanced text extraction dari message
    enhanced_text = extract_clean_text_from_message(message)
    if not enhanced_text:
        return False  # Tidak ada text, tidak bisa spam

    user_id = message.from_user.id

    # Cek apakah user adalah admin grup (pengecualian)
    if SPAM_CONFIG["admin_immunity"]:
        is_admin = await is_group_admin(message, user_id)
        if is_admin:
            return False  # Admin bebas dari anti-spam

    # Enhanced duplicate detection dengan entities
    # Untuk message utama, gunakan text dan entities yang sesuai
    if message.text:
        is_duplicate = anti_spam_manager.is_duplicate_message(user_id, message.text, message.entities)
    elif message.caption:
        is_duplicate = anti_spam_manager.is_duplicate_message(user_id, message.caption, message.caption_entities)
    else:
        # Fallback untuk enhanced text
        is_duplicate = anti_spam_manager.is_duplicate_message(user_id, enhanced_text)

    if is_duplicate:
        # Hapus pesan duplikat
        success = await delete_message_safely(message)
        if success:
            # Silent delete - tidak ada log untuk menjaga terminal tetap rapi
            pass
        return True

    return False

def get_anti_spam_stats() -> Dict:
    """
    Get statistik anti-spam untuk admin panel
    
    Returns:
        dict: Statistik anti-spam
    """
    return anti_spam_manager.get_stats()
