"""
QR Payment Handler - Menangani QR Payment dan Status Checking
"""

import logging
import asyncio
import random
from datetime import datetime, timedelta
from aiogram import Router, types, F
from aiogram.types import FSInputFile, InlineKeyboardMarkup, InlineKeyboardButton
from .config_payment import QRIS_IMAGE_PATH, QRIS_TIMEOUT_MINUTES, PREMIUM_PACKAGES
from .payment_service import PaymentService
from .premium_service import PremiumService

router = Router()

# Import akan dilakukan secara dinamis untuk menghindari circular import

# Global storage untuk QR payment tracking
qr_payment_states = {}

class QRPaymentState:
    """Class untuk tracking QR payment state"""
    def __init__(self, user_id: int, package_key: str, package_info: dict):
        self.user_id = user_id
        self.package_key = package_key
        self.package_info = package_info
        self.payment_id = None
        self.amount = None
        self.created_at = datetime.now()
        self.message_ids = []
        self.qr_message_ids = []  # Track QR messages (prioritas hapus pertama)
        self.spam_message_ids = []  # Track spam response messages
        self.user_command_ids = []  # Track user commands (ke-2, ke-3, dst untuk dihapus)
        self.initial_command_id = None  # Track initial user command (PRESERVED)
        self.timeout_task = None

    def add_message_id(self, message_id: int):
        """Tambah message ID untuk tracking"""
        if message_id not in self.message_ids:
            self.message_ids.append(message_id)

    def add_qr_message_id(self, message_id: int):
        """Tambah QR message ID untuk prioritas cleanup"""
        if message_id not in self.qr_message_ids:
            self.qr_message_ids.append(message_id)
            self.add_message_id(message_id)

    def add_spam_message_id(self, message_id: int):
        """Tambah spam response message ID"""
        if message_id not in self.spam_message_ids:
            self.spam_message_ids.append(message_id)
            self.add_message_id(message_id)

    def add_user_command_id(self, message_id: int):
        """Tambah user command ID (ke-2, ke-3, dst untuk dihapus)"""
        if message_id not in self.user_command_ids:
            self.user_command_ids.append(message_id)
            self.add_message_id(message_id)

    def is_expired(self) -> bool:
        """Cek apakah QR payment sudah expired (5 menit)"""
        return datetime.now() - self.created_at > timedelta(minutes=QRIS_TIMEOUT_MINUTES)


async def qr_timeout_handler(bot, user_id: int):
    """Handler untuk timeout QR payment (5 menit) - hapus SEMUA pesan"""
    try:
        await asyncio.sleep(QRIS_TIMEOUT_MINUTES * 60)  # 5 menit

        if user_id in qr_payment_states:
            qr_state = qr_payment_states[user_id]
            payment_id = qr_state.payment_id

            # Cleanup SEMUA QR messages dan spam messages (prioritas QR dulu)
            await cleanup_qr_messages_timeout(bot, user_id)

            # Kirim pesan timeout
            timeout_text = f"""⏰ Pembayaran di batalkan

❌ QRIS sudah expired
🔄 Ketik perintah untuk QRIS baru
🆔 ID Pay: `{payment_id}`"""

            await bot.send_message(chat_id=user_id, text=timeout_text, parse_mode="Markdown")

    except asyncio.CancelledError:
        # Task dibatalkan, normal behavior
        pass
    except Exception as e:
        logging.error(f"Error in QR timeout handler for user {user_id}: {e}")


async def cleanup_qr_messages_timeout(bot, user_id: int):
    """Hapus SEMUA pesan QR dan spam messages saat timeout (5 menit) - prioritas QR dulu"""
    try:
        if user_id in qr_payment_states:
            qr_state = qr_payment_states[user_id]

            # Prioritas 1: Hapus QR dulu
            messages_to_delete = []

            # Kumpulkan QR messages (prioritas pertama)
            for message_id in qr_state.qr_message_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan spam messages
            for message_id in qr_state.spam_message_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan user commands (ke-2, ke-3, dst)
            for message_id in qr_state.user_command_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan sisa messages lain
            for message_id in qr_state.message_ids:
                if (message_id != qr_state.initial_command_id and
                    message_id not in messages_to_delete):
                    messages_to_delete.append(message_id)

            # Batch delete semua messages
            if messages_to_delete:
                await batch_delete_messages(bot, user_id, messages_to_delete)

            # Cancel timeout task jika ada
            if qr_state.timeout_task and not qr_state.timeout_task.done():
                qr_state.timeout_task.cancel()

            # Hapus dari tracking
            del qr_payment_states[user_id]

    except Exception as e:
        logging.error(f"Error cleaning up QR messages timeout for user {user_id}: {e}")


async def cleanup_qr_messages_on_payment_success(bot, user_id: int):
    """Hapus SEMUA pesan QR saat pembayaran berhasil - prioritas QR dulu"""
    try:
        if user_id in qr_payment_states:
            qr_state = qr_payment_states[user_id]

            # Prioritas 1: Hapus QR dulu
            messages_to_delete = []

            # Kumpulkan QR messages (prioritas pertama)
            for message_id in qr_state.qr_message_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan spam messages
            for message_id in qr_state.spam_message_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan user commands (ke-2, ke-3, dst)
            for message_id in qr_state.user_command_ids:
                if message_id != qr_state.initial_command_id:
                    messages_to_delete.append(message_id)

            # Kumpulkan sisa messages lain
            for message_id in qr_state.message_ids:
                if (message_id != qr_state.initial_command_id and
                    message_id not in messages_to_delete):
                    messages_to_delete.append(message_id)

            # Batch delete semua messages
            if messages_to_delete:
                await batch_delete_messages(bot, user_id, messages_to_delete)

            # Cancel timeout task jika ada
            if qr_state.timeout_task and not qr_state.timeout_task.done():
                qr_state.timeout_task.cancel()

            # Hapus dari tracking
            del qr_payment_states[user_id]

    except Exception as e:
        logging.error(f"Error cleaning up QR messages on payment success for user {user_id}: {e}")


async def batch_delete_messages(bot, user_id: int, message_ids: list):
    """Batch delete multiple messages dengan error handling"""
    try:
        if not message_ids:
            return

        # Coba batch delete dulu (lebih efisien)
        try:
            if len(message_ids) > 1:
                await bot.delete_messages(chat_id=user_id, message_ids=message_ids)
                return
        except Exception as e:
            # Check if it's a "message not found" error for batch delete
            if "message to delete not found" not in str(e).lower():
                logging.warning(f"Batch delete failed for user {user_id}: {e}")

        # Fallback ke individual delete
        for message_id in message_ids:
            try:
                await bot.delete_message(chat_id=user_id, message_id=message_id)
            except Exception as e:
                # Only log non-"not found" errors
                if "message to delete not found" not in str(e).lower():
                    logging.warning(f"Failed to delete message {message_id} for user {user_id}: {e}")

    except Exception as e:
        logging.error(f"Error in batch delete messages for user {user_id}: {e}")


async def generate_qr_payment(message: types.Message, package_key: str, package_info: dict):
    """Generate QR payment untuk paket yang dipilih"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    # Generate QR payment for user

    try:
        # Cleanup QR state lama jika ada
        if user_id in qr_payment_states:
            await cleanup_qr_messages_timeout(message.bot, user_id)
        
        # Buat QR payment state baru
        qr_state = QRPaymentState(user_id, package_key, package_info)
        qr_payment_states[user_id] = qr_state

        # Set initial command dari package selection jika ada
        try:
            from .package_selection_handler import user_package_states
            if user_id in user_package_states:
                package_state = user_package_states[user_id]
                qr_state.initial_command_id = package_state.initial_command_id
        except ImportError:
            pass
        
        # Generate payment service
        payment_service = PaymentService()
        
        # Generate unique amount berdasarkan harga paket
        base_price = package_info['price']
        random_suffix = random.randint(1, 999)
        unique_amount = base_price + random_suffix
        
        # Generate payment ID (9 digit)
        payment_id = f"{random.randint(100000000, 999999999)}"
        
        # Simpan ke state
        qr_state.payment_id = payment_id
        qr_state.amount = unique_amount
        
        # Payment text
        payment_text = f"""💎 **Upgrade Premium**

💰 Total bayar: Rp {unique_amount:,}
⏰ Batas waktu: {QRIS_TIMEOUT_MINUTES} menit
🚀 Premium: {package_info['name']}
🆔 ID Pay: `{payment_id}`

⚠️ _Bayar tepat_ ***Rp {unique_amount:,}*** ⚠️"""
        
        # Create keyboard dengan check payment button
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(
                    text="🔍 Cek Status Pembayaran",
                    callback_data=f"check_payment:{payment_id}"
                )]
            ]
        )
        
        # Send QRIS image - try dynamic first, fallback to static
        import os
        from payment.config_payment import USE_DYNAMIC_QR, FALLBACK_TO_STATIC, QR_IMAGE_SIZE

        qris_sent = False

        # Try dynamic QR first if enabled
        if USE_DYNAMIC_QR:
            try:
                from payment.qris_generator import generate_payment_qr

                # Generate dynamic QR dengan auto-fill amount
                qr_buffer = generate_payment_qr(unique_amount, QR_IMAGE_SIZE)

                if qr_buffer:
                    # Send dynamic QR
                    qris_message = await message.answer_photo(
                        photo=types.BufferedInputFile(qr_buffer.getvalue(), filename="payment_qr.png"),
                        caption=payment_text,
                        parse_mode="Markdown",
                        reply_markup=keyboard
                    )
                    qr_state.add_qr_message_id(qris_message.message_id)
                    qris_sent = True
                    # Silent QR delivery
                else:
                    logging.warning(f"Dynamic QR generation failed for user {user_id}")

            except Exception as e:
                logging.error(f"Error generating dynamic QR: {e}")

        # Fallback to static QR if dynamic failed or disabled
        if not qris_sent and FALLBACK_TO_STATIC and os.path.exists(QRIS_IMAGE_PATH):
            try:
                photo_file = FSInputFile(QRIS_IMAGE_PATH)
                qris_message = await message.answer_photo(
                    photo=photo_file,
                    caption=payment_text,
                    parse_mode="Markdown",
                    reply_markup=keyboard
                )
                qr_state.add_qr_message_id(qris_message.message_id)
                qris_sent = True
                logging.info(f"Static QR sent to user {user_id}: Rp {unique_amount}")

            except Exception as e:
                logging.error(f"Error sending static QR: {e}")

        # Final fallback to text only
        if not qris_sent:
            qris_message = await message.answer(
                payment_text,
                parse_mode="Markdown",
                reply_markup=keyboard
            )
            qr_state.add_qr_message_id(qris_message.message_id)
            logging.warning(f"Text-only payment sent to user {user_id}: Rp {unique_amount}")
            qr_state.add_qr_message_id(qris_message.message_id)
            logging.warning(f"QRIS image not found: {QRIS_IMAGE_PATH}")
        
        # Save pending payment
        payment_service.create_pending_payment(
            payment_id=payment_id,
            user_id=user_id,
            amount=unique_amount,
            qris_message_id=qris_message.message_id
        )
        
        # Add to payment history
        try:
            from .payment_history_service import PaymentHistoryService
            history_service = PaymentHistoryService()
            history_service.add_payment_history(
                payment_id=payment_id,
                user_id=user_id,
                amount=unique_amount,
                status="pending"
            )
        except ImportError:
            logging.warning("PaymentHistoryService not available")
        
        # Start timeout task
        qr_state.timeout_task = asyncio.create_task(qr_timeout_handler(message.bot, user_id))

        # PENTING: Cleanup package selection state jika ada (user sudah masuk ke QR state)
        try:
            from .package_selection_handler import user_package_states
            if user_id in user_package_states:
                package_state = user_package_states[user_id]
                # Cancel timeout task dari package state
                if package_state.timeout_task and not package_state.timeout_task.done():
                    package_state.timeout_task.cancel()
                # Hapus package state
                del user_package_states[user_id]
                # Package state cleaned up
        except ImportError:
            pass



    except Exception as e:
        logging.error(f"Error generating QR payment for user {user_id}: {e}")
        # Silent error - no user notification


async def generate_qr_payment_for_user(callback: types.CallbackQuery, package_key: str, package_info: dict):
    """Generate QR payment untuk user yang klik tombol (dari callback)"""
    user_id = callback.from_user.id  # Gunakan callback.from_user.id, bukan message.from_user.id
    username = callback.from_user.username or "Unknown"

    # Generate QR payment for user

    try:
        # Cleanup QR state lama jika ada
        if user_id in qr_payment_states:
            await cleanup_qr_messages_timeout(callback.bot, user_id)

        # Buat QR payment state baru
        qr_state = QRPaymentState(user_id, package_key, package_info)
        qr_payment_states[user_id] = qr_state

        # Set initial command dari package selection jika ada
        try:
            from .package_selection_handler import user_package_states
            if user_id in user_package_states:
                package_state = user_package_states[user_id]
                qr_state.initial_command_id = package_state.initial_command_id
        except ImportError:
            pass

        # Generate payment service
        payment_service = PaymentService()

        # Generate unique amount berdasarkan harga paket
        base_price = package_info['price']
        random_suffix = random.randint(1, 999)
        unique_amount = base_price + random_suffix

        # Generate payment ID (9 digit)
        payment_id = f"{random.randint(100000000, 999999999)}"

        # Simpan ke state
        qr_state.payment_id = payment_id
        qr_state.amount = unique_amount

        # Payment text
        payment_text = f"""💎 **Upgrade Premium**

💰 Total bayar: Rp {unique_amount:,}
⏰ Batas waktu: {QRIS_TIMEOUT_MINUTES} menit
🚀 Premium: {package_info['name']}
🆔 ID Pay: `{payment_id}`

⚠️ _Bayar tepat_ ***Rp {unique_amount:,}*** ⚠️"""

        # Create keyboard dengan check payment button
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(
                    text="🔍 Cek Status Pembayaran",
                    callback_data=f"check_payment:{payment_id}"
                )]
            ]
        )

        # Send QRIS image - try dynamic first, fallback to static
        import os
        from payment.config_payment import USE_DYNAMIC_QR, FALLBACK_TO_STATIC, QR_IMAGE_SIZE

        qris_sent = False

        # Try dynamic QR first if enabled
        if USE_DYNAMIC_QR:
            try:
                from payment.qris_generator import generate_payment_qr

                # Generate dynamic QR dengan auto-fill amount
                qr_buffer = generate_payment_qr(unique_amount, QR_IMAGE_SIZE)

                if qr_buffer:
                    # Send dynamic QR
                    qris_message = await callback.message.answer_photo(
                        photo=types.BufferedInputFile(qr_buffer.getvalue(), filename="payment_qr.png"),
                        caption=payment_text,
                        parse_mode="Markdown",
                        reply_markup=keyboard
                    )
                    qr_state.add_qr_message_id(qris_message.message_id)
                    qris_sent = True
                    # Silent QR delivery
                else:
                    logging.warning(f"Dynamic QR generation failed for user {user_id}")

            except Exception as e:
                logging.error(f"Error generating dynamic QR: {e}")

        # Fallback to static QR if dynamic failed or disabled
        if not qris_sent and FALLBACK_TO_STATIC and os.path.exists(QRIS_IMAGE_PATH):
            try:
                photo_file = FSInputFile(QRIS_IMAGE_PATH)
                qris_message = await callback.message.answer_photo(
                    photo=photo_file,
                    caption=payment_text,
                    parse_mode="Markdown",
                    reply_markup=keyboard
                )
                qr_state.add_qr_message_id(qris_message.message_id)
                qris_sent = True
                logging.info(f"Static QR sent to user {user_id}: Rp {unique_amount}")

            except Exception as e:
                logging.error(f"Error sending static QR: {e}")

        # Final fallback to text only
        if not qris_sent:
            qris_message = await callback.message.answer(
                payment_text,
                parse_mode="Markdown",
                reply_markup=keyboard
            )
            qr_state.add_qr_message_id(qris_message.message_id)
            logging.warning(f"Text-only payment sent to user {user_id}: Rp {unique_amount}")

        # Save pending payment dengan USER ID yang benar
        payment_service.create_pending_payment(
            payment_id=payment_id,
            user_id=user_id,  # Gunakan user_id dari callback
            amount=unique_amount,
            qris_message_id=qris_message.message_id
        )

        # Add to payment history
        try:
            from .payment_history_service import PaymentHistoryService
            history_service = PaymentHistoryService()
            history_service.add_payment_history(
                payment_id=payment_id,
                user_id=user_id,  # Gunakan user_id dari callback
                amount=unique_amount,
                status="pending"
            )
        except ImportError:
            # Silent import - tidak perlu warning
            pass

        # Start timeout task
        qr_state.timeout_task = asyncio.create_task(qr_timeout_handler(callback.bot, user_id))

        # PENTING: Cleanup package selection state jika ada (user sudah masuk ke QR state)
        try:
            from .package_selection_handler import user_package_states
            if user_id in user_package_states:
                package_state = user_package_states[user_id]
                # Cancel timeout task dari package state
                if package_state.timeout_task and not package_state.timeout_task.done():
                    package_state.timeout_task.cancel()
                # Hapus package state
                del user_package_states[user_id]
                # Package state cleaned up
        except ImportError:
            pass



    except Exception as e:
        logging.error(f"Error generating QR payment for user {user_id}: {e}")
        # Silent error - no user notification


async def handle_spam_during_qr(message: types.Message):
    """Handle spam command saat QR payment aktif"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    try:
        # Cek apakah user ada dalam QR payment state
        if user_id in qr_payment_states:
            qr_state = qr_payment_states[user_id]

            if not qr_state.is_expired():
                # QR masih aktif, kirim pesan "Bayar dulu ya"
                remaining_time = QRIS_TIMEOUT_MINUTES - int((datetime.now() - qr_state.created_at).total_seconds() // 60)
                remaining_time = max(1, remaining_time)  # Minimal 1 menit

                spam_text = f"""⏳ Bayar dulu, ya...

💰 Total bayar: Rp {qr_state.amount:,}
⏰ Sisa waktu: {remaining_time} menit
🆔 ID Pay: `{qr_state.payment_id}`"""

                spam_msg = await message.answer(spam_text, parse_mode="Markdown")
                qr_state.add_spam_message_id(spam_msg.message_id)
                # Track user spam command (perintah ke-2, ke-3, dst) untuk dihapus
                # Hanya perintah pertama yang dipertahankan
                qr_state.add_user_command_id(message.message_id)

                return

        # Jika tidak ada QR state, cek apakah ada pending payment
        from payment.payment_service import PaymentService
        payment_service = PaymentService()
        pending_payments = payment_service.get_pending_payments()
        user_pending = [p for p in pending_payments if p['user_id'] == user_id]

        if user_pending:
            # User ada pending payment tapi tidak ada QR state, kirim pesan generic
            pending_payment = user_pending[0]

            # Calculate remaining time - use import datetime to avoid scope issues
            import datetime as dt
            created_time = dt.datetime.strptime(pending_payment['created_at'], "%Y-%m-%d %H:%M:%S")
            created_time = created_time.replace(tzinfo=dt.timezone.utc)
            current_time = dt.datetime.now(dt.timezone.utc)
            elapsed_seconds = (current_time - created_time).total_seconds()
            remaining_seconds = max(0, 300 - elapsed_seconds)  # 5 minutes = 300 seconds
            remaining_minutes = max(1, int(remaining_seconds // 60))

            spam_text = f"""⏳ Bayar dulu, ya...

💰 Total bayar: Rp {pending_payment['amount']:,}
⏰ Sisa waktu: {remaining_minutes} menit
🆔 ID Pay: `{pending_payment['payment_id']}`"""

            await message.answer(spam_text, parse_mode="Markdown")
            return

        # Jika tidak ada QR state dan tidak ada pending payment, fallback ke package selection
        logging.warning(f"User {user_id} routed to QR handler but no QR state or pending payment found")
        from .package_selection_handler import show_package_selection
        await show_package_selection(message)

    except Exception as e:
        logging.error(f"Error handling spam during QR for user {user_id}: {e}")

# Router handler dihapus karena routing sudah dihandle di middleware

@router.callback_query(F.data.startswith("check_payment:"))
async def check_payment_callback(callback: types.CallbackQuery):
    """Handle check payment button"""
    try:
        # Extract payment ID from callback data
        payment_id = callback.data.split(":", 1)[1]
        user_id = callback.from_user.id
        
        # Check if payment exists and is still pending
        payment_service = PaymentService()
        pending_payments = payment_service.get_pending_payments()
        
        # Find the payment
        payment_found = None
        for payment in pending_payments:
            if payment['payment_id'] == payment_id and payment['user_id'] == user_id:
                payment_found = payment
                break
        
        if not payment_found:
            # Payment not found or already processed
            premium_service = PremiumService()
            is_premium, premium_info = premium_service.check_premium_status(user_id)
            
            if is_premium:
                await callback.answer("Pembayaran berhasil! Premium sudah aktif.", show_alert=True)
            else:
                await callback.answer("Pembayaran tidak ditemukan atau sudah expired.", show_alert=True)
            return
        
        # Payment still pending
        await callback.answer(
            f"Pembayaran masih pending.\n\n"
            f"Total bayar: Rp {payment_found['amount']:,}\n"
            f"Status update setelah pembayaran.",
            show_alert=True
        )
        
    except Exception as e:
        logging.error(f"Error checking payment: {e}")
        await callback.answer("Terjadi kesalahan saat mengecek pembayaran.", show_alert=True)
