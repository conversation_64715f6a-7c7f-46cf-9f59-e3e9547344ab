"""
Payment Statistics Module - Statistik Pembayaran untuk Admin Panel
"""

import logging
import json
import os
from datetime import datetime
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .keyboards import create_payment_stats_keyboard, create_back_keyboard, create_trial_users_keyboard, create_reset_trial_confirm_keyboard
from .utils import log_bot, safe_edit_message, safe_answer_callback
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions
from .trial_users_handler import get_trial_users_handler

router = Router()

# Path ke database payment
PAYMENT_DIR = os.path.join(os.path.dirname(__file__), "..", "payment", "database")
TRANSACTIONS_DB = os.path.join(PAYMENT_DIR, "transactions.txt")
PENDING_PAYMENTS_DB = os.path.join(PAYMENT_DIR, "pending_payments.txt")
PREMIUM_USERS_DB = os.path.join(PAYMENT_DIR, "premium_users.txt")
FAILED_USERS_DB = os.path.join(PAYMENT_DIR, "failed_users.txt")
TRIAL_USERS_DB = os.path.join(PAYMENT_DIR, "trial_users.txt")

# Import payment services - Disabled untuk menghindari import error
# try:
#     from payment.services.failed_payment_tracker import FailedPaymentTracker
#     logging.info("Successfully imported FailedPaymentTracker")
# except ImportError as e:
#     # Fallback jika import gagal
#     logging.warning(f"Could not import FailedPaymentTracker: {e}")
#     FailedPaymentTracker = None

# Disable FailedPaymentTracker untuk sementara
FailedPaymentTracker = None

class PaymentStatsService:
    """Service untuk menghitung statistik pembayaran"""

    def __init__(self):
        self.ensure_files_exist()
        # Initialize failed payment tracker jika tersedia
        self.failed_tracker = FailedPaymentTracker() if FailedPaymentTracker else None
    
    def ensure_files_exist(self):
        """Pastikan semua file database ada"""
        os.makedirs(PAYMENT_DIR, exist_ok=True)
        
        files_to_check = [TRANSACTIONS_DB, PENDING_PAYMENTS_DB, PREMIUM_USERS_DB, FAILED_USERS_DB]
        for file_path in files_to_check:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    pass  # Create empty file
    
    def count_successful_users(self) -> int:
        """Hitung jumlah user yang berhasil bayar"""
        try:
            successful_users = set()
            
            if os.path.exists(TRANSACTIONS_DB):
                with open(TRANSACTIONS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                transaction = json.loads(line)
                                if transaction.get('status') == 'completed':
                                    successful_users.add(transaction.get('user_id'))
                            except json.JSONDecodeError:
                                continue
            
            return len(successful_users)
        except Exception as e:
            logging.error(f"Error counting successful users: {e}")
            return 0
    
    def count_pending_users(self) -> int:
        """Hitung jumlah user yang sedang pending (belum expired)"""
        try:
            pending_users = set()
            current_time = datetime.now()
            
            if os.path.exists(PENDING_PAYMENTS_DB):
                with open(PENDING_PAYMENTS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                payment = json.loads(line)
                                if payment.get('status') == 'pending':
                                    # Check if not expired (5 minutes timeout)
                                    created_time = datetime.strptime(payment['created_at'], "%Y-%m-%d %H:%M:%S")
                                    time_diff = (current_time - created_time).total_seconds()
                                    
                                    if time_diff <= 300:  # 5 minutes = 300 seconds
                                        pending_users.add(payment.get('user_id'))
                                    else:
                                        # Move expired payment to failed
                                        self.add_failed_user(payment.get('user_id'))
                            except (json.JSONDecodeError, ValueError, KeyError):
                                continue
            
            return len(pending_users)
        except Exception as e:
            logging.error(f"Error counting pending users: {e}")
            return 0
    
    def count_failed_users(self) -> int:
        """Hitung jumlah user yang gagal bayar (unique users)"""
        try:
            # Gunakan failed_tracker jika tersedia
            if self.failed_tracker:
                return self.failed_tracker.count_failed_users()

            # Fallback ke method manual
            failed_users = set()

            if os.path.exists(FAILED_USERS_DB):
                with open(FAILED_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                user_data = json.loads(line)
                                failed_users.add(user_data.get('user_id'))
                            except json.JSONDecodeError:
                                continue

            return len(failed_users)
        except Exception as e:
            logging.error(f"Error counting failed users: {e}")
            return 0

    def count_total_failed_payments(self) -> int:
        """Hitung total pembayaran yang gagal (termasuk retry)"""
        try:
            total_failed = 0

            if os.path.exists(FAILED_USERS_DB):
                with open(FAILED_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                user_data = json.loads(line)
                                failed_count = user_data.get('failed_count', 1)
                                total_failed += failed_count
                            except json.JSONDecodeError:
                                continue

            return total_failed
        except Exception as e:
            logging.error(f"Error counting total failed payments: {e}")
            return 0
    
    def add_failed_user(self, user_id: int):
        """Tambahkan user ke daftar failed (jika belum ada)"""
        try:
            if not user_id:
                return
            
            # Check if user already in failed list
            existing_failed = set()
            if os.path.exists(FAILED_USERS_DB):
                with open(FAILED_USERS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                user_data = json.loads(line)
                                existing_failed.add(user_data.get('user_id'))
                            except json.JSONDecodeError:
                                continue
            
            # Add if not exists
            if user_id not in existing_failed:
                failed_user_data = {
                    "user_id": user_id,
                    "failed_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "failed"
                }
                
                with open(FAILED_USERS_DB, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(failed_user_data) + '\n')
                
                # logging.info(f"Added user {user_id} to failed list")  # Disabled untuk mengurangi noise
        
        except Exception as e:
            logging.error(f"Error adding failed user: {e}")
    
    def calculate_success_rate(self, successful: int, total_payments: int) -> float:
        """Hitung success rate dari total pembayaran"""
        try:
            if total_payments == 0:
                return 0.0
            return round((successful / total_payments) * 100, 1)
        except Exception as e:
            logging.error(f"Error calculating success rate: {e}")
            return 0.0
    
    def get_payment_statistics(self) -> dict:
        """Ambil semua statistik pembayaran"""
        try:
            successful = self.count_successful_users()
            pending = self.count_pending_users()
            failed = self.count_failed_users()
            total_failed_payments = self.count_total_failed_payments()
            total_payments = successful + total_failed_payments
            success_rate = self.calculate_success_rate(successful, total_payments)

            return {
                'successful': successful,
                'pending': pending,
                'failed': failed,
                'total_payments': total_payments,
                'success_rate': success_rate
            }
        except Exception as e:
            logging.error(f"Error getting payment statistics: {e}")
            return {
                'successful': 0,
                'pending': 0,
                'failed': 0,
                'total_payments': 0,
                'success_rate': 0.0
            }


def count_trial_users() -> int:
    """Hitung jumlah user trial di database"""
    try:
        if not os.path.exists(TRIAL_USERS_DB):
            return 0

        count = 0
        with open(TRIAL_USERS_DB, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:  # Skip empty lines
                    count += 1

        return count
    except Exception as e:
        logging.error(f"Error counting trial users: {e}")
        return 0


def reset_all_trial_users() -> int:
    """Reset semua trial users (hapus semua data dari file). Return jumlah user yang dihapus."""
    try:
        # Count users before reset
        user_count = count_trial_users()

        # Truncate file (hapus semua isi)
        with open(TRIAL_USERS_DB, 'w', encoding='utf-8') as f:
            f.write("")  # Kosongkan file

        logging.info(f"Reset {user_count} trial users from database")
        return user_count
    except Exception as e:
        logging.error(f"Error resetting trial users: {e}")
        return 0




@router.callback_query(F.data == "payment_stats")
async def show_payment_stats(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan statistik pembayaran"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission (opsional, bisa disesuaikan)
    permissions = get_admin_permissions(username)
    if not permissions.get('can_view_files', True):  # Default allow jika tidak ada setting khusus
        await callback.answer("❌ Anda tidak memiliki izin melihat statistik pembayaran", show_alert=True)
        return
    
    try:
        # Gunakan PaymentHistoryService untuk data yang lebih akurat
        try:
            from payment.payment_history_service import PaymentHistoryService
            history_service = PaymentHistoryService()
            stats = history_service.get_payment_statistics()

            # Hitung berdasarkan status dari payment history
            status_counts = stats.get('status_counts', {})
            successful = status_counts.get('completed', 0)
            pending = status_counts.get('pending', 0)
            failed = status_counts.get('failed', 0) + status_counts.get('expired', 0)  # Gabung failed + expired
            total_payments = stats.get('total_payments', 0)

            # Hitung success rate
            success_rate = round((successful / total_payments) * 100, 1) if total_payments > 0 else 0.0

        except ImportError:
            # Fallback ke PaymentStatsService lama
            stats_service = PaymentStatsService()
            stats_data = stats_service.get_payment_statistics()
            successful = stats_data['successful']
            pending = stats_data['pending']
            failed = stats_data['failed']
            total_payments = stats_data['total_payments']
            success_rate = stats_data['success_rate']

        stats_text = f"""📊 STATISTIK PEMBAYARAN

✅ Berhasil: {successful}
⏳ Pending: {pending}
❌ Gagal: {failed}

📈 Total: {total_payments}
💯 Sukses: {success_rate}%"""
        
        await safe_edit_message(callback, stats_text, create_payment_stats_keyboard())
        await safe_answer_callback(callback)
        
        log_admin_activity(username, "Lihat statistik pembayaran")
        log_bot(f"Statistik pembayaran: Berhasil={successful}, Pending={pending}, Gagal={failed}")
        
    except Exception as e:
        logging.error(f"Error showing payment stats: {e}")
        error_text = "❌ Gagal memuat statistik pembayaran"
        await safe_edit_message(callback, error_text, create_back_keyboard())
        await safe_answer_callback(callback)


@router.callback_query(F.data == "trial_users")
async def show_trial_users(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan daftar trial users"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions.get('can_view_files', True):
        await callback.answer("❌ Anda tidak memiliki izin melihat trial users", show_alert=True)
        return

    try:
        # Show loading message in chat (no buttons)
        loading_text = "⏳ Memuat data..."
        await callback.message.edit_text(loading_text)

        # Get trial users handler
        handler = get_trial_users_handler(callback.bot)
        await handler.show_trial_users(callback, page=1)

        log_admin_activity(username, "Lihat trial users")

    except Exception as e:
        logging.error(f"Error showing trial users: {e}")
        error_text = "❌ Gagal memuat daftar trial users"
        await callback.message.edit_text(
            error_text,
            reply_markup=create_back_keyboard()
        )


@router.callback_query(F.data.startswith("trial_users_page:"))
async def show_trial_users_page(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan halaman trial users tertentu"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        error_text = "❌ Akses ditolak"
        await callback.message.edit_text(error_text, reply_markup=create_back_keyboard())
        return

    try:
        # Extract page number
        page = int(callback.data.split(":", 1)[1])

        # Langsung tampilkan halaman tanpa loading message (data sudah di-cache)
        handler = get_trial_users_handler(callback.bot)
        await handler.show_trial_users(callback, page=page)

        log_admin_activity(username, f"Lihat trial users halaman {page}")

    except Exception as e:
        logging.error(f"Error showing trial users page: {e}")
        error_text = "❌ Gagal memuat halaman"
        await callback.message.edit_text(
            error_text,
            reply_markup=create_trial_users_keyboard(1, 1, False, False)
        )


@router.callback_query(F.data == "trial_users_refresh")
async def refresh_trial_users(callback: types.CallbackQuery, state: FSMContext):
    """Refresh daftar trial users"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        error_text = "❌ Akses ditolak"
        await callback.message.edit_text(error_text, reply_markup=create_back_keyboard())
        return

    try:
        # Show refresh message in chat (no buttons)
        refresh_text = "🔄 Refresh..."
        await callback.message.edit_text(refresh_text)

        # Get trial users handler
        handler = get_trial_users_handler(callback.bot)
        await handler.show_trial_users(callback, page=1, is_refresh=True)

        log_admin_activity(username, "Refresh trial users")

    except Exception as e:
        logging.error(f"Error refreshing trial users: {e}")
        error_text = "❌ Gagal refresh data trial users"
        await callback.message.edit_text(
            error_text,
            reply_markup=create_trial_users_keyboard(1, 1, False, False)
        )


@router.callback_query(F.data == "reset_trial_users")
async def reset_trial_users(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan konfirmasi reset trial users"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions.get('can_view_files', True):
        await callback.answer("❌ Anda tidak memiliki izin reset trial users", show_alert=True)
        return

    try:
        # Answer callback immediately
        await callback.answer("🔄 Memuat data trial...")

        # Count current trial users
        trial_count = count_trial_users()

        # Show confirmation
        confirm_text = f"""🔄 **RESET TRIAL USERS**

📊 Total user trial: {trial_count} user
"""

        await callback.message.edit_text(
            confirm_text,
            reply_markup=create_reset_trial_confirm_keyboard(),
            parse_mode="Markdown"
        )

        log_admin_activity(username, f"Lihat konfirmasi reset trial ({trial_count} users)")

    except Exception as e:
        logging.error(f"Error showing reset trial confirmation: {e}")
        error_text = "❌ Gagal memuat konfirmasi reset"
        await callback.message.edit_text(
            error_text,
            reply_markup=create_payment_stats_keyboard()
        )


@router.callback_query(F.data == "confirm_reset_trial")
async def confirm_reset_trial(callback: types.CallbackQuery, state: FSMContext):
    """Execute reset trial users"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    try:
        # Answer callback immediately
        await callback.answer("🔄 Mereset trial users...")

        # Show processing message
        await callback.message.edit_text(
            "🔄 **MERESET TRIAL USERS**\n\n⏳ Memproses...",
            parse_mode="Markdown"
        )

        # Execute reset
        deleted_count = reset_all_trial_users()

        # Show success message
        success_text = f"""✅ **RESET BERHASIL**

🗑️ Data trial di hapus
📊 {deleted_count} berhasil direset
"""

        await callback.message.edit_text(
            success_text,
            reply_markup=create_payment_stats_keyboard(),
            parse_mode="Markdown"
        )

        log_admin_activity(username, f"Reset {deleted_count} trial users")
        log_bot(f"Admin {username} reset {deleted_count} trial users")

    except Exception as e:
        logging.error(f"Error resetting trial users: {e}")
        error_text = "❌ **ERROR RESET**\n\nGagal mereset trial users."
        await callback.message.edit_text(
            error_text,
            reply_markup=create_payment_stats_keyboard(),
            parse_mode="Markdown"
        )



