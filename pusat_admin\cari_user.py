"""
Cari User Module - Pencarian user
"""

import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_back_keyboard
from .utils import log_bot, parse_search_queries, format_time_ago, truncate_text, safe_edit_message, safe_answer_callback
from .database import db
from .auth_admin import is_admin, log_admin_activity

router = Router()


def search_users(query: str):
    """Cari user berdasarkan query - username/nama/ID dengan support Unicode"""
    # Gunakan database utama semua user (total_users.txt yang 60 user)
    user_data = db.read_dict_data("total_users.txt")
    all_users = []

    # Parse data dari total_users.txt
    for item in user_data:
        parts = item['parts']
        if len(parts) >= 5:
            all_users.append({
                "id": parts[0],
                "username": parts[1],
                "first_name": parts[2],
                "last_name": parts[3],
                "timestamp": parts[4]
            })

    # Normalisasi query - hapus @ dan strip whitespace, tapi jangan lower() untuk Unicode
    original_query = query.strip()
    if original_query.startswith("@"):
        original_query = original_query[1:]

    # Buat query lowercase untuk pencarian ASCII
    query_lower = original_query.lower()

    results = []
    for user in all_users:
        user_id = str(user.get('id', ''))
        username = str(user.get('username', ''))
        first_name = str(user.get('first_name', ''))
        last_name = str(user.get('last_name', ''))

        # Cari berdasarkan ID, username, atau nama (case-insensitive dan Unicode-friendly)
        match_found = False

        # Cek ID (exact atau partial match)
        if original_query in user_id or query_lower in user_id.lower():
            match_found = True

        # Cek username (case-insensitive dan Unicode)
        elif (original_query in username or
              query_lower in username.lower() or
              original_query.lower() in username.lower()):
            match_found = True

        # Cek first_name (case-insensitive dan Unicode)
        elif (original_query in first_name or
              query_lower in first_name.lower() or
              original_query.lower() in first_name.lower()):
            match_found = True

        # Cek last_name (case-insensitive dan Unicode)
        elif (original_query in last_name or
              query_lower in last_name.lower() or
              original_query.lower() in last_name.lower()):
            match_found = True

        # Cek kombinasi nama lengkap (first_name + last_name dengan berbagai separator)
        elif first_name and last_name:
            full_name_space = f"{first_name} {last_name}"  # dengan spasi
            full_name_no_space = f"{first_name}{last_name}"  # tanpa spasi

            if (original_query in full_name_space or
                original_query in full_name_no_space or
                query_lower in full_name_space.lower() or
                query_lower in full_name_no_space.lower()):
                match_found = True

        if match_found:
            results.append({
                "id": user.get('id', ''),
                "username": user.get('username', ''),
                "first_name": user.get('first_name', ''),
                "last_name": user.get('last_name', ''),
                "timestamp": user.get('timestamp', '')
            })

    return results


@router.callback_query(F.data == "user_search")
async def user_search_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt untuk cari user"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    search_text = """🔍 **CARI USER**

Masukkan username/nama/ID user:"""

    # Gunakan safe functions untuk error handling
    await safe_edit_message(callback, search_text, create_back_keyboard("admin_users"))
    await safe_answer_callback(callback)

    await state.set_state(AdminStates.waiting_search_query)


@router.message(AdminStates.waiting_search_query, F.chat.type == "private")
async def process_user_search(message: types.Message, state: FSMContext):
    """Proses pencarian user"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    # Parse multiple queries (dipisah baris)
    queries = parse_search_queries(message.text)

    if not queries:
        await message.answer("❌ Query pencarian tidak boleh kosong.")
        return

    # Validasi minimal 2 karakter untuk setiap query
    for query in queries:
        if len(query) < 2:
            await message.answer("❌ Query pencarian minimal 2 karakter.")
            return

    # Tampilkan loading
    loading_msg = await message.answer("🔍 **CARI USER**\n\nSedang mencari user dalam database...")

    try:
        all_results = []
        found_user_ids = set()  # Untuk menghindari duplikasi

        # Cari untuk setiap query
        for query in queries:
            results = search_users(query)
            for user in results:
                if user['id'] not in found_user_ids:
                    all_results.append(user)
                    found_user_ids.add(user['id'])

        if not all_results:
            query_text = ", ".join([f"`{q}`" for q in queries])
            await loading_msg.edit_text(
                f"🔍 <b>HASIL PENCARIAN</b>\n\n❌ Tidak ditemukan: {query_text}",
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="HTML"
            )
        else:
            # Format hasil pencarian dengan tampilan yang rapi dan sejajar
            result_text = f"🔍 <b>HASIL PENCARIAN</b>\n\nDitemukan: {len(all_results)} user\n\n"

            # Tampilkan maksimal 10 hasil dengan format sejajar
            for i, user in enumerate(all_results[:10], 1):
                # Siapkan data dengan fallback
                username = user.get('username', '').strip()
                first_name = user.get('first_name', '').strip()
                last_name = user.get('last_name', '').strip()
                user_id = user.get('id', '').strip()

                # Format nama lengkap
                full_name = f"{first_name} {last_name}".strip()
                if not full_name:
                    full_name = "Tanpa Nama"

                # Format username - kosongkan jika tidak ada
                if username:
                    username_display = f"@{username}"
                else:
                    username_display = "Tanpa Username"

                # Format dengan padding untuk alignment yang rapi
                # Urutan: ID → NAMA → USERNAME
                result_text += f"{i}. 🆔 <b>ID:</b> <code>{user_id}</code>\n"
                result_text += f"    👤 <b>NAMA:</b> <b>{full_name}</b>\n"
                result_text += f"    🌐 <b>USERNAME:</b> <b>{username_display}</b>\n\n"

            if len(all_results) > 10:
                result_text += f"... dan {len(all_results) - 10} user lainnya"

            await loading_msg.edit_text(
                result_text,
                reply_markup=create_back_keyboard("admin_users"),
                parse_mode="HTML"
            )

        query_log = ", ".join(queries)
        log_admin_activity(username, f"Cari user: {query_log} ({len(all_results)} hasil)")

    except Exception as e:
        logging.error(f"Error searching users: {e}")
        await loading_msg.edit_text(
            "❌ <b>Error</b>\n\nGagal melakukan pencarian user.",
            reply_markup=create_back_keyboard("admin_users"),
            parse_mode="HTML"
        )

    await state.clear()
