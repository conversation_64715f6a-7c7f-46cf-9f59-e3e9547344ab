@echo off
echo ========================================
echo       Payment API Tester
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python tidak ditemukan! Install Python terlebih dahulu.
    pause
    exit /b 1
)

echo ✅ Python ditemukan
echo.

REM Install dependencies
echo 📦 Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Gagal install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed
echo.

REM Run the test
echo 🚀 Starting Payment API Test...
echo.
python test_mutasi.py

echo.
echo ========================================
echo Test selesai. Tekan Enter untuk keluar.
pause >nul
