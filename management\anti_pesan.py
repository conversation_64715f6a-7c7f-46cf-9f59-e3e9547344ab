"""
Anti-Pesan System untuk Grup
Hapus otomatis pesan yang mengandung kata-kata terlarang di grup
- Kata terlarang: set, sets, data, file, files
- Hanya admin grup yang bisa kirim pesan dengan kata-kata tersebut
- User biasa yang kirim akan dihapus otomatis
"""

import re
from aiogram import Router, types, F
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError
from utils.text_cleaner import ultimate_text_cleaner, extract_clean_text_from_message

router = Router()

# Konfigurasi kata-kata terlarang
BLOCKED_KEYWORDS = ["set", "sets", "data", "file", "files"]

KEYWORDS_CONFIG = {
    "case_sensitive": False,     # Tidak case-sensitive ("SET" = "set")
    "whole_word_only": True,     # Hanya whole word (bukan substring)
    "admin_immunity": True,      # Admin grup kebal dari blocking
    "check_reply_messages": True, # Cek juga reply message
    "check_captions": True       # Cek juga caption media
}

def contains_blocked_keywords(text: str) -> bool:
    """
    Cek apakah text mengandung kata-kata terlarang

    Args:
        text (str): Text yang akan dicek

    Returns:
        bool: True jika mengandung kata terlarang, False jika tidak
    """
    if not text:
        return False

    # Clean text dari formatting untuk deteksi yang akurat
    cleaned_text = ultimate_text_cleaner(text)

    # Convert ke lowercase jika tidak case-sensitive
    if not KEYWORDS_CONFIG["case_sensitive"]:
        cleaned_text = cleaned_text.lower()
        keywords_to_check = [keyword.lower() for keyword in BLOCKED_KEYWORDS]
    else:
        keywords_to_check = BLOCKED_KEYWORDS

    # Cek dengan whole word boundary jika diaktifkan
    if KEYWORDS_CONFIG["whole_word_only"]:
        # Gunakan word boundary untuk memastikan whole word
        for keyword in keywords_to_check:
            pattern = r'\b' + re.escape(keyword) + r'\b'
            if re.search(pattern, cleaned_text):
                return True
        return False
    else:
        # Cek substring biasa
        for keyword in keywords_to_check:
            if keyword in cleaned_text:
                return True
        return False

async def is_group_admin(message: types.Message, user_id: int) -> bool:
    """
    Mengecek apakah user adalah admin di grup
    
    Args:
        message (types.Message): Message object untuk akses bot
        user_id (int): ID user yang akan dicek
        
    Returns:
        bool: True jika user adalah admin, False jika tidak
    """
    try:
        chat_member = await message.bot.get_chat_member(message.chat.id, user_id)
        return chat_member.status in ["administrator", "creator"]
    except Exception:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False

async def delete_message_safely(message: types.Message) -> bool:
    """
    Menghapus message dengan error handling
    
    Args:
        message (types.Message): Message yang akan dihapus
        
    Returns:
        bool: True jika berhasil dihapus, False jika gagal
    """
    try:
        await message.delete()
        return True
    except TelegramBadRequest as e:
        if "message to delete not found" in str(e).lower():
            # Message sudah dihapus
            pass
        elif "not enough rights" in str(e).lower():
            # Bot tidak punya permission untuk hapus
            pass
        else:
            # Error lain
            pass
        return False
    except TelegramForbiddenError as e:
        # Bot di-kick dari grup atau tidak punya akses
        return False
    except Exception as e:
        # Error umum lainnya
        return False

def extract_all_text_from_message(message: types.Message) -> str:
    """
    Extract semua text dari message (text, caption, reply)
    
    Args:
        message (types.Message): Message object
        
    Returns:
        str: Gabungan semua text yang ditemukan
    """
    all_texts = []
    
    # Text utama
    if message.text:
        all_texts.append(message.text)
    
    # Caption (untuk media)
    if KEYWORDS_CONFIG["check_captions"] and message.caption:
        all_texts.append(message.caption)
    
    # Reply message
    if KEYWORDS_CONFIG["check_reply_messages"] and message.reply_to_message:
        if message.reply_to_message.text:
            all_texts.append(message.reply_to_message.text)
        if message.reply_to_message.caption:
            all_texts.append(message.reply_to_message.caption)
    
    # Enhanced cleaning menggunakan utility yang sudah ada
    return extract_clean_text_from_message(message) if all_texts else ""

@router.message(F.chat.type.in_(["group", "supergroup"]) & (F.text | F.caption))
async def anti_pesan_handler(message: types.Message):
    """
    Handler untuk mendeteksi dan menghapus pesan dengan kata-kata terlarang
    Hanya admin grup yang bisa kirim pesan dengan kata-kata terlarang
    """
    # Extract semua text dari message
    all_text = extract_all_text_from_message(message)
    if not all_text:
        return  # Tidak ada text untuk dicek

    # Cek apakah mengandung kata-kata terlarang
    has_blocked_keywords = contains_blocked_keywords(all_text)
    if not has_blocked_keywords:
        return  # Tidak ada kata terlarang, lanjutkan

    # Cek apakah pengirim adalah admin grup
    user_id = message.from_user.id
    is_admin = await is_group_admin(message, user_id)

    # Jika admin dan admin immunity aktif, biarkan pesan
    if KEYWORDS_CONFIG["admin_immunity"] and is_admin:
        return  # Admin boleh kirim kata-kata terlarang

    # Jika bukan admin, hapus pesan
    success = await delete_message_safely(message)
    if success:
        # Silent delete - tidak ada log untuk menjaga terminal tetap rapi
        pass

def get_blocked_keywords() -> list:
    """
    Get daftar kata-kata terlarang
    
    Returns:
        list: Daftar kata-kata terlarang
    """
    return BLOCKED_KEYWORDS.copy()

def get_keywords_config() -> dict:
    """
    Get konfigurasi keywords system
    
    Returns:
        dict: Konfigurasi keywords
    """
    return KEYWORDS_CONFIG.copy()

def update_blocked_keywords(new_keywords: list) -> bool:
    """
    Update daftar kata-kata terlarang
    
    Args:
        new_keywords (list): Daftar kata-kata baru
        
    Returns:
        bool: True jika berhasil update
    """
    global BLOCKED_KEYWORDS
    try:
        BLOCKED_KEYWORDS = new_keywords.copy()
        return True
    except Exception:
        return False

def add_blocked_keyword(keyword: str) -> bool:
    """
    Tambah kata terlarang baru
    
    Args:
        keyword (str): Kata yang akan ditambah
        
    Returns:
        bool: True jika berhasil ditambah
    """
    try:
        if keyword not in BLOCKED_KEYWORDS:
            BLOCKED_KEYWORDS.append(keyword)
            return True
        return False  # Sudah ada
    except Exception:
        return False

def remove_blocked_keyword(keyword: str) -> bool:
    """
    Hapus kata terlarang

    Args:
        keyword (str): Kata yang akan dihapus

    Returns:
        bool: True jika berhasil dihapus
    """
    try:
        if keyword in BLOCKED_KEYWORDS:
            BLOCKED_KEYWORDS.remove(keyword)
            return True
        return False  # Tidak ada
    except Exception:
        return False

def get_anti_pesan_stats() -> dict:
    """
    Get statistik anti-pesan untuk admin panel

    Returns:
        dict: Statistik anti-pesan system
    """
    return {
        "total_blocked_keywords": len(BLOCKED_KEYWORDS),
        "blocked_keywords": BLOCKED_KEYWORDS.copy(),
        "config": KEYWORDS_CONFIG.copy(),
        "system_status": "active"
    }

def toggle_keywords_config(config_key: str, value: bool = None) -> bool:
    """
    Toggle atau set konfigurasi keywords system

    Args:
        config_key (str): Key konfigurasi yang akan diubah
        value (bool, optional): Value baru. Jika None, akan di-toggle

    Returns:
        bool: Status baru dari konfigurasi
    """
    global KEYWORDS_CONFIG

    if config_key in KEYWORDS_CONFIG and isinstance(KEYWORDS_CONFIG[config_key], bool):
        if value is None:
            # Toggle
            KEYWORDS_CONFIG[config_key] = not KEYWORDS_CONFIG[config_key]
        else:
            # Set value
            KEYWORDS_CONFIG[config_key] = value

        return KEYWORDS_CONFIG[config_key]

    return False

def is_keyword_blocked(keyword: str) -> bool:
    """
    Cek apakah kata tertentu ada dalam daftar blocked

    Args:
        keyword (str): Kata yang akan dicek

    Returns:
        bool: True jika kata diblokir
    """
    if not KEYWORDS_CONFIG["case_sensitive"]:
        return keyword.lower() in [k.lower() for k in BLOCKED_KEYWORDS]
    else:
        return keyword in BLOCKED_KEYWORDS
