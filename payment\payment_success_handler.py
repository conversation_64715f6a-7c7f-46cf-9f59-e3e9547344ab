"""
Payment Success Handler - Menangani konfirmasi pembayaran berhasil
"""

import logging
from datetime import datetime, timedelta
from aiogram import Bo<PERSON>

from .premium_service import PremiumService
from .config_payment import PREMIUM_PACKAGES

# Import dari handlers lain
from .qr_payment_handler import qr_payment_states, cleanup_qr_messages_on_payment_success
from .package_selection_handler import user_package_states, cleanup_package_selection_timeout


async def handle_payment_success(bot: Bot, payment_data: dict, mutasi_data: dict):
    """
    Handle pembayaran berhasil
    
    Args:
        bot: Bot instance
        payment_data: Data pembayaran dari pending_payments
        mutasi_data: Data mutasi dari payment gateway
    """
    user_id = payment_data['user_id']
    payment_id = payment_data['payment_id']
    amount = payment_data['amount']
    
    try:
        # Processing payment success
        
        # Ambil informasi paket dari QR state
        package_info = None
        package_key = None
        
        if user_id in qr_payment_states:
            qr_state = qr_payment_states[user_id]
            package_info = qr_state.package_info
            package_key = qr_state.package_key
        
        # Jika tidak ada di QR state, coba deteksi dari amount
        if not package_info:
            package_key, package_info = detect_package_from_amount(amount)
        
        if not package_info:
            logging.error(f"Cannot determine package for payment {payment_id}, amount: {amount}")
            package_info = PREMIUM_PACKAGES['30d']  # Default ke 30 hari
            package_key = '30d'
        
        # Aktivasi premium
        premium_service = PremiumService()
        duration_seconds = package_info['duration_seconds']

        # Deteksi metode pembayaran dari mutasi
        payment_method = detect_payment_method(mutasi_data)

        success = premium_service.activate_premium_with_duration(
            user_id=user_id,
            transaction_id=payment_id,
            payment_method=payment_method,
            duration_seconds=duration_seconds
        )
        
        if success:
            # Cleanup semua pesan sebelumnya
            await cleanup_all_user_messages(bot, user_id)
            
            # Hitung tanggal berakhir dengan WIB display
            from datetime import timezone
            current_utc = datetime.now(timezone.utc)
            expired_utc = current_utc + timedelta(seconds=duration_seconds)

            # Format WIB untuk display ke user
            premium_service = PremiumService()
            expired_date_str = premium_service.format_utc_to_wib_display(expired_utc)
            
            # Kirim konfirmasi pembayaran berhasil
            success_text = f"""✅ Pembayaran Berhasil

🎉 Premium: {package_info['name']} aktif
💰 Total bayar: Rp {amount:,}
🏦 Metode: {payment_method}
📅 Berakhir: {expired_date_str}
🆔 ID Pay: `{payment_id}`

🚀 Semua fitur sudah aktif!"""
            
            await bot.send_message(chat_id=user_id, text=success_text, parse_mode="Markdown")
            
            # Update payment history status
            try:
                from .payment_history_service import PaymentHistoryService
                history_service = PaymentHistoryService()
                history_service.update_payment_status(payment_id, "completed")
            except ImportError:
                # Silent import - tidak perlu warning
                pass
            
            # Show payment success (important notification)
            print(f"✅ Premium activated: user {user_id}, package {package_key}, payment {payment_id}")
            
        else:
            logging.error(f"Failed to activate premium for user {user_id}")
            # Silent failure - no user notification
        
    except Exception as e:
        logging.error(f"Error handling payment success for user {user_id}: {e}")
        # Silent error - no user notification


async def cleanup_all_user_messages(bot: Bot, user_id: int):
    """Cleanup semua pesan user dari semua handler saat pembayaran berhasil - prioritas QR dulu"""
    try:
        # Cleanup dari QR payment (prioritas QR dulu, hapus semua KECUALI command pertama)
        await cleanup_qr_messages_on_payment_success(bot, user_id)

        # Cleanup dari package selection (hapus semua KECUALI command pertama)
        await cleanup_package_selection_timeout(bot, user_id)

    except Exception as e:
        logging.error(f"Error cleaning up all messages for user {user_id}: {e}")


def detect_package_from_amount(amount: int) -> tuple:
    """
    Deteksi paket berdasarkan amount pembayaran
    
    Args:
        amount: Jumlah pembayaran
        
    Returns:
        tuple: (package_key, package_info) atau (None, None) jika tidak ditemukan
    """
    try:
        # Cari paket yang sesuai dengan range amount (base_price + 1-999)
        for package_key, package_info in PREMIUM_PACKAGES.items():
            base_price = package_info['price']
            min_amount = base_price + 1
            max_amount = base_price + 999
            
            if min_amount <= amount <= max_amount:
                # Package detected from amount
                return package_key, package_info
        
        logging.warning(f"No package detected for amount {amount}")
        return None, None
        
    except Exception as e:
        logging.error(f"Error detecting package from amount {amount}: {e}")
        return None, None


def detect_payment_method(mutasi_data: dict) -> str:
    """
    Deteksi metode pembayaran dari data mutasi

    Args:
        mutasi_data: Data mutasi dari payment gateway

    Returns:
        str: Nama metode pembayaran (bank/e-wallet yang digunakan)
    """
    try:
        # Detect payment method from mutasi data

        # Coba ambil dari berbagai field yang mungkin ada
        method_fields = ['brand_name', 'sender_bank', 'bank_name', 'method', 'payment_method', 'bank', 'source', 'description', 'remark', 'note']

        detected_methods = []

        for field in method_fields:
            if field in mutasi_data and mutasi_data[field]:
                value = str(mutasi_data[field]).strip()
                if value:
                    detected_methods.append(f"{field}: {value}")
                    method = value.upper()

                    # Prioritas deteksi: Spesifik dulu, baru general
                    # Bank detection (spesifik dulu untuk avoid conflict)
                    if 'DANAMON' in method:
                        return 'Danamon'
                    elif 'MANDIRI' in method:
                        return 'Mandiri'
                    elif 'BCA' in method:
                        return 'BCA'
                    elif 'BNI' in method:
                        return 'BNI'
                    elif 'BRI' in method:
                        return 'BRI'
                    elif 'CIMB' in method:
                        return 'CIMB'
                    elif 'PERMATA' in method:
                        return 'Permata'
                    elif 'BSI' in method or 'SYARIAH' in method:
                        return 'BSI'

                    # E-wallet detection (setelah bank untuk avoid DANA conflict dengan DANAMON)
                    elif 'DANA' in method:
                        return 'DANA'
                    elif 'OVO' in method:
                        return 'OVO'
                    elif 'GOPAY' in method or 'GOJEK' in method:
                        return 'GOPAY'
                    elif 'SHOPEEPAY' in method or 'SHOPEE' in method:
                        return 'ShopeePay'
                    elif 'LINKAJA' in method or 'LINK AJA' in method:
                        return 'LinkAja'

                    # Jika ada nama yang tidak dikenali tapi bukan QRIS
                    elif 'QRIS' not in method and len(method) > 2:
                        # Kembalikan nama asli jika tidak dikenali
                        clean_method = value.title()  # Proper case
                        return clean_method

        # Default jika tidak ada yang cocok
        return 'QRIS'

    except Exception as e:
        logging.error(f"Error detecting payment method: {e}")
        return 'QRIS'


# Import timedelta yang diperlukan
from datetime import timedelta
