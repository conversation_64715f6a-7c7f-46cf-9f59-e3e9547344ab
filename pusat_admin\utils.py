"""
Utility functions untuk semua fitur admin
"""

import logging
import time
from datetime import datetime
from aiogram import types


def log_user(message: types.Message):
    """Log user activity - disabled for admin features"""
    pass


def log_bot(text: str):
    """Log bot activity - disabled for admin features"""
    pass


def format_time_ago(timestamp_str):
    """Format timestamp menjadi 'X waktu yang lalu' - REALTIME DETAIL"""
    try:
        # Validasi input
        if not timestamp_str or not str(timestamp_str).strip():
            return "waktu tidak diketahui"

        # Konversi ke integer dengan validasi
        timestamp = int(str(timestamp_str).strip())
        current_time = int(time.time())
        diff = current_time - timestamp

        # Validasi timestamp tidak negatif dan tidak terlalu jauh di masa depan
        if diff < -86400:  # Lebih dari 1 hari di masa depan
            return "waktu tidak valid"
        elif diff < 0:
            return "baru saja"
        elif diff < 60:
            # Detik
            if diff <= 5:
                return "baru saja"
            else:
                return f"{diff} detik lalu"
        elif diff < 3600:
            # Menit
            minutes = diff // 60
            return f"{minutes} menit lalu"
        elif diff < 86400:
            # Jam
            hours = diff // 3600
            return f"{hours} jam lalu"
        else:
            # Hari
            days = diff // 86400
            if days > 365:
                return "lebih dari 1 tahun lalu"
            return f"{days} hari lalu"
    except (ValueError, TypeError, OverflowError) as e:
        logging.warning(f"Error formatting timestamp '{timestamp_str}': {e}")
        return "waktu tidak diketahui"


def format_file_size(size_bytes):
    """Format ukuran file menjadi human readable"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def clean_username(username):
    """Bersihkan username dari karakter @"""
    if not username:
        return ""
    return username.lstrip("@")


def validate_user_input(text, min_length=1, max_length=1000):
    """Validasi input user"""
    if not text or not text.strip():
        return False, "Input tidak boleh kosong"
    
    text = text.strip()
    if len(text) < min_length:
        return False, f"Input minimal {min_length} karakter"
    
    if len(text) > max_length:
        return False, f"Input maksimal {max_length} karakter"
    
    return True, text


def parse_user_ids(text):
    """Parse user IDs dari text input - untuk blokir (ID dan username)"""
    user_ids = []
    lines = text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line.isdigit():
            user_ids.append(line)
        elif line.startswith('@'):
            # Username, akan dicari ID-nya nanti
            user_ids.append(line)

    return user_ids


def parse_unblock_ids(text):
    """Parse user IDs untuk unblokir - hanya ID yang diterima"""
    user_ids = []
    lines = text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line.isdigit():
            user_ids.append(line)
        # Tidak menerima username untuk unblokir

    return user_ids


def parse_search_queries(text):
    """Parse queries untuk pencarian - menerima semua jenis input"""
    queries = []
    lines = text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line:  # Menerima semua input yang tidak kosong
            queries.append(line)

    return queries


def truncate_text(text, max_length=100):
    """Potong text jika terlalu panjang"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def escape_markdown(text):
    """Escape karakter markdown"""
    escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in escape_chars:
        text = text.replace(char, f'\\{char}')
    return text


def clean_text_for_markdown(text):
    """Bersihkan text untuk markdown dan telegram - IMPROVED"""
    if not text:
        return ""

    try:
        # Convert to string
        text = str(text).strip()

        # Jika text kosong setelah strip, return kosong
        if not text:
            return ""

        # Strategi cleaning yang lebih permisif:
        # 1. Pertahankan karakter ASCII yang aman
        # 2. Ganti karakter Unicode dengan representasi yang aman
        # 3. Hapus karakter yang benar-benar bermasalah untuk Telegram

        cleaned = ""
        for char in text:
            if char.isascii():
                # Karakter ASCII - cek apakah aman
                if char.isalnum() or char in ' -_@:.()[]{}':
                    cleaned += char
                elif char in '*`~#+=|!<>':
                    # Escape markdown characters
                    cleaned += f'\\{char}'
                else:
                    # Skip karakter ASCII yang bermasalah
                    continue
            else:
                # Karakter non-ASCII (Unicode) - handle dengan lebih baik
                if '\u0030' <= char <= '\u0039':  # Unicode digits 0-9
                    # Convert Unicode digits ke ASCII digits
                    cleaned += str(ord(char) - ord('\u0030'))
                elif char.isalpha():
                    # Skip huruf Unicode yang stylized - terlalu banyak noise
                    continue
                # Skip emoji dan simbol Unicode lainnya

        # Bersihkan spasi berlebih
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned)  # Multiple spaces jadi single space
        cleaned = cleaned.strip()

        # Limit length
        if len(cleaned) > 30:
            cleaned = cleaned[:27] + "..."

        # Jika hasil cleaning kosong atau hanya karakter tidak informatif, return kosong
        # Biarkan logic di level atas yang handle fallback ke user ID
        if not cleaned or len(cleaned.replace(' ', '').replace('-', '').replace('_', '')) < 2:
            return ""

        return cleaned

    except Exception as e:
        logging.warning(f"Error cleaning text '{text}': {e}")
        return ""


def get_current_timestamp():
    """Get current timestamp as string"""
    return str(int(time.time()))


async def safe_edit_message(callback, text, reply_markup=None, parse_mode="Markdown"):
    """Safely edit message dengan error handling untuk callback expired"""
    try:
        await callback.message.edit_text(
            text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True
    except Exception as e:
        if "message is not modified" in str(e).lower():
            # Message sama, tidak perlu edit
            return True
        elif "can't parse entities" in str(e).lower() and parse_mode == "HTML":
            # HTML parsing error, fallback to plain text
            logging.warning(f"HTML parse failed, using plain text: {e}")
            try:
                fallback_text = text.replace("<code>", "").replace("</code>", "")
                await callback.message.edit_text(
                    fallback_text,
                    reply_markup=reply_markup
                )
                return True
            except Exception as e2:
                logging.error(f"Error editing message with fallback: {e2}")
                return False
        else:
            logging.error(f"Error editing message: {e}")
            return False


async def safe_answer_callback(callback, text=None, show_alert=False):
    """Safely answer callback dengan error handling untuk callback expired"""
    try:
        await callback.answer(text, show_alert=show_alert)
        return True
    except Exception as e:
        if "query is too old" in str(e).lower() or "query ID is invalid" in str(e).lower():
            # Callback sudah expired, abaikan
            logging.warning(f"Callback expired: {e}")
            return False
        else:
            logging.error(f"Error answering callback: {e}")
            return False


async def safe_send_html_message(message, text, reply_markup=None):
    """Safely send HTML message with fallback to plain text"""
    try:
        await message.answer(text, reply_markup=reply_markup, parse_mode="HTML")
        return True
    except Exception as e:
        if "can't parse entities" in str(e).lower():
            # HTML parsing error, fallback to plain text
            logging.warning(f"HTML parse failed, using plain text: {e}")
            try:
                fallback_text = text.replace("<code>", "").replace("</code>", "")
                await message.answer(fallback_text, reply_markup=reply_markup)
                return True
            except Exception as e2:
                logging.error(f"Error sending fallback message: {e2}")
                return False
        else:
            logging.error(f"Error sending HTML message: {e}")
            return False


def update_admin_timestamp(callback):
    """Update timestamp admin saat aktivitas di panel"""
    try:
        from .data_sync import sync_active_user

        user_id = str(callback.from_user.id)
        username_data = callback.from_user.username or ''
        first_name = callback.from_user.first_name or ''
        last_name = callback.from_user.last_name or ''
        timestamp = str(int(time.time()))

        # Update timestamp admin ke active users
        sync_active_user(user_id, username_data, first_name, last_name, timestamp)
        return True
    except Exception as e:
        logging.warning(f"Error updating admin timestamp: {e}")
        return False


def format_datetime(timestamp_str):
    """Format timestamp menjadi datetime string"""
    try:
        timestamp = int(timestamp_str)
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime("%d/%m/%Y %H:%M:%S")
    except:
        return "Tanggal tidak valid"


def count_lines_in_file(file_path):
    """Hitung jumlah baris dalam file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except:
        return 0


def get_file_size(file_path):
    """Get ukuran file dalam bytes"""
    try:
        import os
        return os.path.getsize(file_path)
    except:
        return 0


def format_rupiah(amount):
    """Format angka menjadi format rupiah"""
    try:
        # Convert to int if it's a float
        if isinstance(amount, float):
            amount = int(amount)

        # Format with thousand separators
        return f"Rp {amount:,}".replace(",", ".")
    except:
        return f"Rp {amount}"


def create_progress_bar(current, total, length=10):
    """Buat progress bar sederhana"""
    if total == 0:
        return "█" * length
    
    filled = int(length * current / total)
    bar = "█" * filled + "░" * (length - filled)
    percentage = int(100 * current / total)
    return f"{bar} {percentage}%"


def split_long_message(text, max_length=4000):
    """Split pesan panjang menjadi beberapa bagian"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while text:
        if len(text) <= max_length:
            parts.append(text)
            break
        
        # Cari tempat yang baik untuk memotong
        cut_pos = text.rfind('\n', 0, max_length)
        if cut_pos == -1:
            cut_pos = text.rfind(' ', 0, max_length)
        if cut_pos == -1:
            cut_pos = max_length
        
        parts.append(text[:cut_pos])
        text = text[cut_pos:].lstrip()
    
    return parts


def clean_user_database():
    """Bersihkan database user dari data yang corrupt"""
    try:
        from .database import db

        # Baca semua data
        user_data = db.read_dict_data("users.txt")
        clean_data = []
        corrupted_count = 0

        for item in user_data:
            parts = item['parts']
            if len(parts) >= 5:
                try:
                    # Validasi user ID (harus numeric)
                    user_id = str(parts[0]).strip()
                    if not user_id.isdigit():
                        corrupted_count += 1
                        continue

                    # Cari timestamp yang valid dari semua field
                    timestamp = None
                    for i in range(len(parts) - 1, -1, -1):
                        try:
                            test_timestamp = int(str(parts[i]).strip())
                            # Validasi timestamp (harus dalam range yang masuk akal)
                            if 1600000000 <= test_timestamp <= 2000000000:  # 2020-2033
                                timestamp = str(test_timestamp)
                                break
                        except (ValueError, TypeError):
                            continue

                    if timestamp is None:
                        corrupted_count += 1
                        continue

                    # Ambil data dengan format standar
                    username = clean_text_for_markdown(str(parts[1] if len(parts) > 1 else ""))
                    first_name = clean_text_for_markdown(str(parts[2] if len(parts) > 2 else ""))
                    last_name = clean_text_for_markdown(str(parts[3] if len(parts) > 3 else ""))

                    # Data valid, tambahkan ke clean_data dengan format standar
                    clean_data.append(f"{user_id}|{username}|{first_name}|{last_name}|{timestamp}")

                except Exception as e:
                    logging.warning(f"Error processing user data: {parts} - {e}")
                    corrupted_count += 1
                    continue
            else:
                corrupted_count += 1

        # Tulis kembali data yang bersih
        if clean_data:
            db.write_lines("users.txt", clean_data)

        logging.info(f"Database cleaned: {len(clean_data)} valid users, {corrupted_count} corrupted entries removed")
        return len(clean_data), corrupted_count

    except Exception as e:
        logging.error(f"Error cleaning user database: {e}")
        return 0, 0


def validate_user_data(user_id, username, first_name, last_name, timestamp):
    """Validasi data user sebelum disimpan"""
    try:
        # Validasi user_id
        if not str(user_id).strip().isdigit():
            return False, "Invalid user ID"

        # Validasi timestamp
        int(str(timestamp).strip())

        # Sanitize text fields
        username = str(username or "").strip()
        first_name = str(first_name or "").strip()
        last_name = str(last_name or "").strip()

        # Remove problematic characters
        for field in [username, first_name, last_name]:
            if any(ord(char) > 127 for char in field):
                # Contains non-ASCII characters, clean them
                field = ''.join(char for char in field if ord(char) <= 127)

        return True, "Valid"

    except (ValueError, TypeError):
        return False, "Invalid data format"
