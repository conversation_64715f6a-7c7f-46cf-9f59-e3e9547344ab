"""
Keyboard management untuk cancel functionality
"""

from aiogram import types

def get_cancel_keyboard():
    """
    Keyboard khusus saat bot dalam sending mode.
    Hanya 1 tombol /cancel.
    """
    return types.ReplyKeyboardMarkup(
        keyboard=[
            [
                types.KeyboardButton(text="/cancel"),
            ],
        ],
        resize_keyboard=True,
        one_time_keyboard=False  # Jangan hilang setelah dipencet
    )

def get_normal_keyboard():
    """
    Keyboard normal saat bot tidak dalam sending mode.
    """
    return types.ReplyKeyboardMarkup(
        keyboard=[
            [
                types.KeyboardButton(text="/to_vcf"),
                types.KeyboardButton(text="/to_txt"),
                types.KeyboardButton(text="/admin"),
                types.KeyboardButton(text="/manual"),
            ],
            [
                types.KeyboardButton(text="/add"),
                types.KeyboardButton(text="/delete"),
                types.KeyboardButton(text="/renamectc"),
                types.KeyboardButton(text="/renamefile"),
            ],
            [
                types.KeyboardButton(text="/merge"),
                types.KeyboardButton(text="/split"),
                types.KeyboardButton(text="/count"),
                types.KeyboardButton(text="/nodup"),
            ],
            [
                types.KeyboardButton(text="/getname"),
                types.KeyboardButton(text="/generate"),
                types.KeyboardButton(text="/setting"),
                types.KeyboardButton(text="/status"),
            ],
            [
                types.KeyboardButton(text="/help"),
            ],
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )

async def set_cancel_keyboard(message):
    """
    Set keyboard ke mode cancel.
    """
    import asyncio
    from aiogram.exceptions import TelegramRetryAfter

    # Retry 3x untuk set cancel keyboard
    for attempt in range(3):
        try:
            await message.answer("📨 Mengirim file...",
                               reply_markup=get_cancel_keyboard())
            break
        except TelegramRetryAfter as e:
            if attempt < 2:
                await asyncio.sleep(e.retry_after + 1)
        except Exception:
            # Error lain, stop retry
            break

# Function ini sudah tidak dipakai - keyboard restore sekarang pakai pesan yang sudah ada
