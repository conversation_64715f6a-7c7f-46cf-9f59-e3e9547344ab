import logging
from typing import Dict, List
import time

class SmartLogger:
    """
    Smart logger untuk mengurangi spam log saat user upload multiple files.
    <PERSON><PERSON><PERSON>lkan log upload dan menampilkan summary.
    """
    
    def __init__(self):
        self._user_uploads: Dict[int, List[str]] = {}
        self._last_log_time: Dict[int, float] = {}
        self._batch_timeout = 1.0  # 1 detik timeout untuk batch (lebih responsif)
    
    def log_user_file_upload(self, user_id: int, filename: str):
        """
        Log user file upload dengan smart batching.
        
        Args:
            user_id: ID user Telegram
            filename: Nama file yang diupload
        """
        current_time = time.time()
        
        # Initialize user data jika belum ada
        if user_id not in self._user_uploads:
            self._user_uploads[user_id] = []
            self._last_log_time[user_id] = current_time
        
        # Tambah file ke batch
        self._user_uploads[user_id].append(filename)
        
        # Cek apakah perlu flush batch (timeout atau file pertama)
        time_since_last = current_time - self._last_log_time[user_id]
        
        if len(self._user_uploads[user_id]) == 1:
            # File pertama - log langsung
            # logging.info(f"user: kirim file {filename}")  # Disabled - tidak perlu log
            self._last_log_time[user_id] = current_time
        elif time_since_last > self._batch_timeout:
            # Timeout - flush batch sebelumnya dan mulai baru
            self._flush_user_batch(user_id, exclude_current=True)
            # logging.info(f"user: kirim file {filename}")  # Disabled - tidak perlu log
            self._user_uploads[user_id] = [filename]
            self._last_log_time[user_id] = current_time
        else:
            # Masih dalam batch - update waktu saja
            self._last_log_time[user_id] = current_time
    
    def _flush_user_batch(self, user_id: int, exclude_current: bool = False):
        """
        Flush batch upload untuk user tertentu.
        
        Args:
            user_id: ID user Telegram
            exclude_current: Jika True, jangan include file terakhir
        """
        if user_id not in self._user_uploads or not self._user_uploads[user_id]:
            return
        
        files = self._user_uploads[user_id]
        if exclude_current and len(files) > 1:
            files = files[:-1]  # Exclude file terakhir
        
        if len(files) > 1:
            # Log summary untuk multiple files
            first_file = files[0]
            last_file = files[-1]
            total = len(files)
            # logging.info(f"user: kirim {total} file ({first_file} ... {last_file})")  # Disabled
        
        # Clear batch
        if exclude_current and len(self._user_uploads[user_id]) > 1:
            self._user_uploads[user_id] = [self._user_uploads[user_id][-1]]
        else:
            self._user_uploads[user_id] = []
    
    def flush_all_batches(self):
        """
        Flush semua batch yang pending.
        Biasanya dipanggil saat user selesai upload atau pindah command.
        """
        for user_id in list(self._user_uploads.keys()):
            self._flush_user_batch(user_id)
    
    def flush_user_batch(self, user_id: int):
        """
        Flush batch untuk user tertentu.
        Dipanggil saat user selesai upload atau pindah command.
        
        Args:
            user_id: ID user Telegram
        """
        self._flush_user_batch(user_id)
    
    def log_user_text(self, user_id: int, text: str):
        """
        Log user text message. Flush batch upload jika ada.
        
        Args:
            user_id: ID user Telegram
            text: Text message dari user
        """
        # Flush batch upload dulu jika ada
        self.flush_user_batch(user_id)
        
        # Log text message - DISABLED
        # logging.info(f"user: {text}")  # Disabled
    
    def cleanup_old_batches(self, max_age_seconds: int = 300):
        """
        Cleanup batch yang sudah lama tidak aktif.
        
        Args:
            max_age_seconds: Maksimal umur batch dalam detik (default 5 menit)
        """
        current_time = time.time()
        users_to_remove = []
        
        for user_id, last_time in self._last_log_time.items():
            if current_time - last_time > max_age_seconds:
                self._flush_user_batch(user_id)
                users_to_remove.append(user_id)
        
        # Remove old users
        for user_id in users_to_remove:
            if user_id in self._user_uploads:
                del self._user_uploads[user_id]
            if user_id in self._last_log_time:
                del self._last_log_time[user_id]

# Global instance
smart_logger = SmartLogger()

def log_user_smart(message, user_id: int = None):
    """
    Smart logging untuk user messages.
    
    Args:
        message: Message object dari aiogram
        user_id: Optional user ID (akan diambil dari message jika tidak ada)
    """
    if user_id is None:
        user_id = message.from_user.id
    
    if getattr(message, "document", None):
        # File upload
        filename = message.document.file_name
        smart_logger.log_user_file_upload(user_id, filename)
    else:
        # Text message
        text = message.text
        smart_logger.log_user_text(user_id, text)

def flush_user_logs(user_id: int):
    """
    Flush pending logs untuk user tertentu.
    Dipanggil saat user selesai upload atau ganti command.
    
    Args:
        user_id: ID user Telegram
    """
    smart_logger.flush_user_batch(user_id)

def log_bot(text: str):
    """
    Standard bot logging - DISABLED.

    Args:
        text: Text yang akan dilog
    """
    # logging.info(f"bot: {text}")  # Disabled
    pass
