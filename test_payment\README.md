# Payment API Tester

Tool untuk test API payment dan cek mutasi secara langsung.

## 📋 Fitur

- ✅ Test refresh API key
- ✅ Test cek mutasi
- ✅ Test full flow (refresh + mutasi)
- ✅ Logging detail untuk debugging
- ✅ Error handling lengkap

## 🚀 Cara Penggunaan

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Jalankan Test
```bash
python test_mutasi.py
```

### 3. Pilih Mode Test
- **Option 1**: Test refresh key saja
- **Option 2**: Test cek mutasi saja (pastikan key sudah di-refresh)
- **Option 3**: Test full flow (recommended)

## 📊 Output yang Diharapkan

### Refresh Key Berhasil:
```
🔄 Refreshing API key...
Status: 200
✅ Key refresh berhasil!
Merchant ID: *********
Key: abc123xyz
```

### Cek Mutasi Berhasil:
```
🔍 Checking mutasi...
Status: 200
✅ Mutasi berhasil diambil!
Jumlah mutasi: 2

📋 Detail Mutasi:
--- Mutasi 1 ---
Amount: Rp 7000
Brand: DANA
Issuer Ref: ref123
Buyer Ref: buyer456
Date: 2024-01-01 12:00:00
```

## 🔧 Konfigurasi

API credentials sudah dikonfigurasi sesuai dengan sistem payment bot:

- **Base URL**: `https://api.wahdx.co`
- **Token Key**: `f0f7444a811741d9289ccbae9db9a481de0a1581a5b7d99dec93437549cf26c6`
- **Orkut Username**: `WARstoreUP`
- **Orkut Token**: `2528455:NJ7MwxG2PgDurdWmoyYsl68Svk3qbaFc`

## ⚠️ Catatan Penting

1. **Key Expiry**: API key expire dalam 1 menit, jadi harus refresh sebelum cek mutasi
2. **Rate Limiting**: Jangan terlalu sering hit API untuk avoid rate limit
3. **Error Handling**: Script sudah handle berbagai error case
4. **Logging**: Semua request/response di-log untuk debugging

## 🐛 Troubleshooting

### Error 401 Unauthorized
- Cek TokenKey di header
- Pastikan orkut credentials benar

### Error 500 Internal Server Error
- API server sedang down
- Coba lagi beberapa saat

### Key Refresh Gagal
- Cek koneksi internet
- Pastikan credentials orkut benar

### Mutasi Kosong
- Normal jika tidak ada transaksi baru
- Coba lagi setelah ada pembayaran

## 📝 Log Format

Script akan menampilkan:
- URL endpoint yang dipanggil
- Payload yang dikirim
- HTTP status code
- Response dari server
- Parsing result

Ini memudahkan debugging jika ada masalah dengan API.
