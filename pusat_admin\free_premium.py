"""
Free Premium Module - Gratis Premium untuk Admin
"""

import logging
import time
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import (create_duration_keyboard, create_confirm_free_premium_keyboard,
                       create_payment_stats_keyboard, create_back_keyboard)
from .utils import log_bot, safe_edit_message, safe_answer_callback, safe_send_html_message
from .auth_admin import is_admin, log_admin_activity, get_admin_permissions

# Global tracker untuk mencegah duplicate processing
processing_sessions = {
    'free_premium': {},      # user_id: timestamp
    'duration_selection': {} # user_id: timestamp
}

router = Router()

# Import premium service
try:
    from payment.premium_service import PremiumService
    from payment.payment_history_service import PaymentHistoryService
except ImportError:
    PremiumService = None
    PaymentHistoryService = None
    # Silent import - tidak perlu warning


def parse_user_ids(text: str) -> list:
    """Parse user IDs dari text input"""
    user_ids = []
    lines = text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and line.isdigit():
            user_ids.append(int(line))

    return user_ids


def escape_html(text: str) -> str:
    """Escape HTML special characters"""
    if not text:
        return ""
    return (text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace('"', "&quot;")
                .replace("'", "&#x27;"))


def get_user_info_from_db(user_id: int) -> dict:
    """Ambil informasi user dari database"""
    try:
        from pusat_admin.database import db

        # Cari user di total_users.txt
        user_data = db.find_by_field("total_users.txt", 0, str(user_id))
        if user_data and len(user_data['parts']) >= 4:
            username = user_data['parts'][1].strip()
            first_name = user_data['parts'][2].strip()
            last_name = user_data['parts'][3].strip()

            # Format username dengan escape HTML
            display_username = f"@{escape_html(username)}" if username else ""

            # Format full name dengan escape HTML
            full_name = f"{escape_html(first_name)} {escape_html(last_name)}".strip()
            if not full_name:
                full_name = f"User {user_id}"

            return {
                "found": True,
                "username": display_username,
                "full_name": full_name,
                "display": display_username if display_username else full_name
            }
    except Exception as e:
        logging.error(f"Error getting user info for {user_id}: {e}")

    # User not found
    return {
        "found": False,
        "username": "",
        "full_name": f"User {user_id}",
        "display": f"User {user_id}"
    }


def validate_and_format_users(user_ids: list) -> tuple:
    """Validasi user IDs dan format daftar user"""
    found_users = []
    not_found_users = []

    for user_id in user_ids:
        user_info = get_user_info_from_db(user_id)
        if user_info['found']:
            found_users.append((user_id, user_info))
        else:
            not_found_users.append(user_id)

    # Format daftar user yang ditemukan
    user_list = []
    for i, (user_id, user_info) in enumerate(found_users, 1):
        user_list.append(f"{i}. <code>{user_id}</code> {user_info['display']}")

    return found_users, not_found_users, "\n".join(user_list)


def get_duration_text(duration_key: str) -> str:
    """Convert duration key ke text"""
    duration_map = {
        "1d": "1 Hari",
        "3d": "3 Hari",
        "7d": "7 Hari",
        "30d": "30 Hari"
    }
    return duration_map.get(duration_key, "1 Hari")


def get_duration_seconds(duration_key: str) -> int:
    """Convert duration key ke seconds (sistem detik)"""
    duration_map = {
        "1d": 86400,      # 24 jam
        "3d": 259200,     # 72 jam
        "7d": 604800,     # 168 jam
        "30d": 2592000    # 720 jam
    }
    return duration_map.get(duration_key, 86400)


@router.callback_query(F.data == "free_premium")
async def show_free_premium_form(callback: types.CallbackQuery, state: FSMContext):
    """Tampilkan form gratis premium"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return
    
    # Cek permission
    permissions = get_admin_permissions(username)
    if not permissions.get('can_manage_users', True):
        await callback.answer("❌ Anda tidak memiliki izin memberikan premium gratis", show_alert=True)
        return
    
    form_text = """🎁 GRATIS PREMIUM

👤 Masukan User ID:
"""
    
    await safe_edit_message(callback, form_text, create_back_keyboard())
    await state.set_state(AdminStates.waiting_free_premium_users)
    await safe_answer_callback(callback)
    
    log_admin_activity(username, "Akses form gratis premium")


@router.message(AdminStates.waiting_free_premium_users)
async def receive_user_ids(message: types.Message, state: FSMContext):
    """Terima input User IDs"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        return
    
    # Parse user IDs
    user_ids = parse_user_ids(message.text)

    if not user_ids:
        await message.answer("""❌ Format tidak valid

Masukkan User ID yang valid (angka saja, satu per baris):

Contoh:
123456789
987654321""")
        return

    # Validasi dan format user list
    found_users, not_found_users, user_list = validate_and_format_users(user_ids)

    # Jika ada user yang tidak ditemukan
    if not_found_users:
        not_found_list = "\n".join(f"• {uid}" for uid in not_found_users)
        error_text = f"""❌ User tidak ditemukan

User ID tidak ada :
{not_found_list}"""
        await message.answer(error_text)
        return

    # Jika semua user ditemukan, simpan ke state
    valid_user_ids = [user_id for user_id, _ in found_users]
    await state.update_data(user_ids=valid_user_ids)

    # Tampilkan daftar user yang ditemukan
    duration_text = f"""🎁 GRATIS PREMIUM

👤 User ID ditemukan:
{user_list}

⏰ Durasi:"""

    await safe_send_html_message(message, duration_text, create_duration_keyboard())
    await state.set_state(AdminStates.waiting_free_premium_duration)


@router.callback_query(F.data.startswith("duration_"))
async def select_duration(callback: types.CallbackQuery, state: FSMContext):
    """Pilih durasi premium - HANYA 1 KALI PROSES"""
    username = callback.from_user.username or ""
    user_id = callback.from_user.id

    # ===== PROTECTION: HANYA BISA PROSES 1 KALI =====
    if user_id in processing_sessions['duration_selection']:
        await callback.answer("⏳ Sedang diproses...", show_alert=True)
        return

    # SET FLAG IMMEDIATELY
    processing_sessions['duration_selection'][user_id] = time.time()

    try:
        if not is_admin(username):
            await callback.answer("❌ Akses ditolak", show_alert=True)
            return

        # Extract duration
        duration_key = callback.data.split("_", 1)[1]
        duration_text = get_duration_text(duration_key)

        # Get user IDs from state
        data = await state.get_data()
        user_ids = data.get('user_ids', [])

        if not user_ids:
            await callback.answer("❌ Data tidak valid", show_alert=True)
            await state.clear()
            return

        # Simpan duration ke state
        await state.update_data(duration_key=duration_key, duration_text=duration_text)

        # Tampilkan konfirmasi
        confirm_text = f"""🎁 GRATIS PREMIUM

⏰ Durasi: {duration_text}

Konfirmasi premium gratis?"""

        await safe_edit_message(callback, confirm_text, create_confirm_free_premium_keyboard(), parse_mode="HTML")
        await state.set_state(AdminStates.waiting_free_premium_confirm)
        await safe_answer_callback(callback)

    finally:
        # Clear flag setelah selesai
        processing_sessions['duration_selection'].pop(user_id, None)


@router.callback_query(F.data == "confirm_free_premium")
async def confirm_free_premium(callback: types.CallbackQuery, state: FSMContext):
    """Konfirmasi dan berikan premium gratis - HANYA 1 KALI PROSES"""
    username = callback.from_user.username or ""
    user_id = callback.from_user.id

    # ===== PROTECTION: HANYA BISA PROSES 1 KALI =====
    if user_id in processing_sessions['free_premium']:
        # Klik kedua, ketiga, dst -> DIABAIKAN
        await callback.answer("⏳ Sedang diproses, tunggu sebentar...", show_alert=True)
        return

    # SET FLAG IMMEDIATELY - Klik pertama
    processing_sessions['free_premium'][user_id] = time.time()

    # Kasih feedback langsung ke user
    await callback.answer("⏳ Memproses premium gratis...", show_alert=True)

    # ===== VALIDATION =====
    if not is_admin(username):
        processing_sessions['free_premium'].pop(user_id, None)  # Clear flag
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    if PremiumService is None:
        processing_sessions['free_premium'].pop(user_id, None)  # Clear flag
        await callback.answer("❌ Service premium tidak tersedia", show_alert=True)
        return

    # Get data from state
    data = await state.get_data()
    user_ids = data.get('user_ids', [])
    duration_key = data.get('duration_key', '1d')
    duration_text = data.get('duration_text', '1 Hari')

    if not user_ids:
        processing_sessions['free_premium'].pop(user_id, None)  # Clear flag
        await callback.answer("❌ Data tidak valid", show_alert=True)
        await state.clear()
        return

    # Update message immediately
    await safe_edit_message(callback, "⏳ Memproses premium gratis...", None)
    
    try:
        # Process each user
        premium_service = PremiumService()
        history_service = PaymentHistoryService() if PaymentHistoryService else None
        
        success_count = 0
        failed_users = []
        
        for user_id_target in user_ids:  # Rename untuk avoid confusion
            try:
                # Generate fake transaction ID for free premium
                import random
                transaction_id = f"FREE_{random.randint(100000000, 999999999)}"

                # Calculate duration in seconds (sistem detik)
                duration_seconds = get_duration_seconds(duration_key)

                # Extend premium duration (TAMBAH waktu, bukan replace)
                success = premium_service.extend_premium_duration(
                    user_id=user_id_target,
                    transaction_id=transaction_id,
                    payment_method="Admin Gratis",
                    additional_seconds=duration_seconds
                )
                
                if success:
                    success_count += 1
                    
                    # Add to payment history if available
                    if history_service:
                        history_service.add_payment_history(
                            payment_id=transaction_id,
                            user_id=user_id_target,
                            amount=0,  # Free
                            status="completed"
                        )
                    
                    # Send notification to user
                    try:
                        from aiogram import Bot
                        from config import BOT_TOKEN

                        bot = Bot(token=BOT_TOKEN)

                        # Get actual expire date dari database (setelah extend)
                        is_premium, premium_info = premium_service.check_premium_status(user_id_target)
                        if is_premium and premium_info:
                            # Parse expire date dari database
                            expired_utc_str = premium_info['expired_date']
                            expired_utc = premium_service.get_utc_datetime_from_string(expired_utc_str)

                            # Format WIB untuk display ke user
                            expired_date_str = premium_service.format_utc_to_wib_display(expired_utc)
                        else:
                            # Fallback jika gagal get dari database
                            from datetime import datetime, timedelta, timezone
                            current_utc = datetime.now(timezone.utc)
                            expired_utc = current_utc + timedelta(seconds=duration_seconds)
                            expired_date_str = premium_service.format_utc_to_wib_display(expired_utc)

                        notification_text = f"""🎉 Premium Gratis Aktif!

🎁 Durasi: {duration_text}
📅 Berakhir: {expired_date_str}
🆔 ID: `{transaction_id}`
👨‍💼 Dari: @KazuhaID1

✨ Selamat menikmati fitur premium!"""

                        await bot.send_message(
                            chat_id=user_id_target,
                            text=notification_text,
                            parse_mode="Markdown"
                        )
                        await bot.session.close()
                    except Exception as e:
                        logging.warning(f"Failed to notify user {user_id_target}: {e}")

                else:
                    failed_users.append(user_id_target)

            except Exception as e:
                logging.error(f"Error activating premium for user {user_id_target}: {e}")
                failed_users.append(user_id_target)
        
        # Format result message
        result_text = f"""✅ PREMIUM GRATIS BERHASIL

👥 Berhasil: {success_count} user
⏰ Durasi: {duration_text}
🎁 Oleh: @{username}"""

        if failed_users:
            result_text += f"\n\n❌ Gagal: {len(failed_users)} user\nID: {', '.join(str(uid) for uid in failed_users)}"
        
        await safe_edit_message(callback, result_text, create_payment_stats_keyboard(), parse_mode="HTML")

        # Log activity
        log_admin_activity(username, f"Berikan premium gratis: {success_count} user, durasi {duration_text}")
        log_bot(f"Free premium granted by {username}: {success_count} users, duration {duration_text}")

    except Exception as e:
        logging.error(f"Error processing free premium: {e}")
        error_text = "❌ Gagal memproses premium gratis"
        await safe_edit_message(callback, error_text, create_payment_stats_keyboard(), parse_mode="HTML")

    finally:
        # ===== ALWAYS CLEAR FLAG =====
        processing_sessions['free_premium'].pop(user_id, None)
        await state.clear()
