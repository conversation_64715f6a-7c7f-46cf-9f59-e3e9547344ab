"""
Trial Handler - Handle aktivasi trial untuk new user dan existing user
"""

import logging
from aiogram import Router, types, F
from aiogram.filters import CommandStart
from .trial_service import TrialService
from .trial_notification_service import get_trial_notification_service

router = Router()


class TrialHandler:
    """Handler untuk trial system"""
    
    def __init__(self):
        self.trial_service = TrialService()
    
    async def handle_new_user_onboarding(self, message: types.Message):
        """
        CRITICAL: Handle new user yang belum join grup/channel
        """
        try:
            user_id = message.from_user.id
            
            # Check membership
            has_joined = await self._check_required_memberships(user_id, message.bot)
            
            if not has_joined:
                # Force join message
                join_message = """👋 Selamat datang!

📢 Untuk menggunakan bot, silakan join:
• Grup: @your_group  
• Channel: @your_channel

✅ Setelah join, ketik perintah apapun untuk mulai trial gratis 3 jam!"""
                
                await message.answer(join_message)
                return False
            
            # User sudah join, add ke total_user.txt
            self._add_user_to_total_users(user_id, message.from_user)
            
            # Show welcome message
            await self._show_welcome_with_trial_info(message)
            return True
            
        except Exception as e:
            logging.error(f"Error in new user onboarding for {user_id}: {e}")
            return False
    
    async def handle_trial_activation_for_new_user(self, message: types.Message):
        """
        CRITICAL: Activate trial untuk new user saat kasih command pertama
        """
        try:
            user_id = message.from_user.id
            
            # Check if user exists in total_user.txt
            if not self.trial_service._user_exists_in_total_users(user_id):
                # User belum di total_user.txt, suruh join dulu
                await self.handle_new_user_onboarding(message)
                return False
            
            # Check eligibility
            if not self.trial_service.is_eligible_for_trial(user_id):
                # User tidak eligible
                await self._handle_ineligible_user(message)
                return False
            
            # Activate trial
            success = self.trial_service.activate_trial(user_id, trial_type="auto")
            
            if success:
                # Get trial info
                is_active, trial_info = self.trial_service.check_trial_status(user_id)
                
                if is_active and trial_info:
                    # Send trial activated notification
                    notification_service = get_trial_notification_service()
                    if notification_service:
                        await notification_service.send_trial_activated_notification(user_id, trial_info)
                
                return True
            else:
                # Trial activation failed
                await message.answer("❌ Gagal mengaktifkan trial. Silakan coba lagi.")
                return False
                
        except Exception as e:
            logging.error(f"Error activating trial for new user {user_id}: {e}")
            return False
    
    async def handle_trial_activation_for_existing_user(self, message: types.Message):
        """
        CRITICAL: Activate trial untuk existing user via /start
        """
        try:
            user_id = message.from_user.id

            # Check apakah sudah punya trial aktif
            is_active, trial_info = self.trial_service.check_trial_status(user_id)
            if is_active:
                # User sudah punya trial aktif, tidak perlu aktivasi lagi - SILENT
                return True

            # Check eligibility
            if not self.trial_service.is_eligible_for_trial(user_id):
                # User tidak eligible, tidak perlu show message
                return False

            # Activate trial
            success = self.trial_service.activate_trial(user_id, trial_type="manual")

            if success:
                # Get trial info
                is_active, trial_info = self.trial_service.check_trial_status(user_id)

                if is_active and trial_info:
                    # Send trial activated notification
                    notification_service = get_trial_notification_service()
                    if notification_service:
                        await notification_service.send_trial_activated_notification(user_id, trial_info)

                return True
            else:
                # Trial activation failed - silent fail
                return False

        except Exception as e:
            logging.error(f"Error activating trial for existing user {user_id}: {e}")
            return False
    
    async def _check_required_memberships(self, user_id: int, bot) -> bool:
        """Check apakah user sudah join grup dan channel yang required"""
        try:
            from management.membership import check_membership
            
            # Check membership
            in_group, in_channel = await check_membership(bot, user_id)
            
            # User must be in BOTH group and channel
            return in_group and in_channel
            
        except Exception as e:
            logging.error(f"Error checking membership for user {user_id}: {e}")
            return True  # Default allow jika error
    
    def _add_user_to_total_users(self, user_id: int, user_info):
        """Add user baru ke total_user.txt"""
        try:
            from datetime import datetime, timezone

            # Format: user_id|username|firstname|lastname|join_date
            username = user_info.username or ""
            first_name = user_info.first_name or ""
            last_name = user_info.last_name or ""
            join_date = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

            user_line = f"{user_id}|{username}|{first_name}|{last_name}|{join_date}\n"

            with open("pusat_admin/database/total_users.txt", 'a', encoding='utf-8') as f:
                f.write(user_line)

        except Exception as e:
            logging.error(f"Error adding user {user_id} to total users: {e}")
    
    async def _show_welcome_with_trial_info(self, message: types.Message):
        """Show welcome message dengan info trial"""
        try:
            welcome_message = """🎉 Selamat datang!

✅ Anda sudah terdaftar di sistem
🎁 Ketik perintah apapun untuk mulai trial gratis 3 jam

💎 Fitur yang bisa dicoba:
• /to_vcf - Convert ke VCF
• /to_txt - Convert ke TXT  
• /count - Hitung nomor
• /merge - Gabung file
• Dan masih banyak lagi!

⚡ Mulai sekarang juga!"""
            
            await message.answer(welcome_message)
            
        except Exception as e:
            logging.error(f"Error showing welcome message: {e}")
    
    async def _handle_ineligible_user(self, message: types.Message):
        """Handle user yang tidak eligible untuk trial"""
        try:
            user_id = message.from_user.id
            
            # Check specific reason
            if self.trial_service._is_admin_or_super_admin(user_id):
                await message.answer("👑 Anda adalah admin, akses unlimited sudah aktif!")
                return
            
            if self.trial_service._has_active_premium(user_id):
                await message.answer("💎 Premium Anda masih aktif!")
                return
            
            if self.trial_service.has_used_trial_before(user_id):
                # User sudah pernah trial, redirect ke premium
                from .package_selection_handler import show_package_selection
                await show_package_selection(message)
                return
            
            # Default message
            await message.answer("❌ Anda tidak eligible untuk trial. Silakan hubungi admin.")
            
        except Exception as e:
            logging.error(f"Error handling ineligible user {user_id}: {e}")


# Global instance
trial_handler = TrialHandler()


# Helper functions untuk dipakai di handler lain
async def activate_trial_for_new_user(message: types.Message) -> bool:
    """Helper function untuk activate trial new user"""
    return await trial_handler.handle_trial_activation_for_new_user(message)


async def activate_trial_for_existing_user(message: types.Message) -> bool:
    """Helper function untuk activate trial existing user"""
    return await trial_handler.handle_trial_activation_for_existing_user(message)


def is_user_eligible_for_trial(user_id: int) -> bool:
    """Helper function untuk check eligibility"""
    return trial_handler.trial_service.is_eligible_for_trial(user_id)


def check_trial_status(user_id: int):
    """Helper function untuk check trial status"""
    return trial_handler.trial_service.check_trial_status(user_id)
