"""
Manual Payment Verification System
Untuk backup jika auto verification tidak berfungsi
"""
import asyncio
import logging
from datetime import datetime
from aiogram import Bot
from aiogram.types import Message
from .payment_service import PaymentService
from .premium_service import PremiumService

class ManualPaymentVerification:
    def __init__(self, bot: Bot):
        self.bot = bot
        self.payment_service = PaymentService()
        self.premium_service = PremiumService()
    
    async def verify_payment_by_amount(self, amount: int, user_id: int = None) -> dict:
        """
        Manual verification berdasarkan nominal pembayaran
        """
        try:
            # Get pending payments
            pending_payments = self.payment_service.get_pending_payments()
            
            # Find matching payment by amount
            matched_payment = None
            for payment in pending_payments:
                if payment['amount'] == amount:
                    if user_id and payment['user_id'] != user_id:
                        continue  # Skip if user_id specified but doesn't match
                    matched_payment = payment
                    break
            
            if not matched_payment:
                return {
                    'success': False,
                    'message': f'Tidak ditemukan pending payment dengan nominal Rp {amount:,}'
                }
            
            # Create mock mutation data
            mock_mutasi = {
                'amount': str(amount),
                'date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'type': 'CR',
                'brand_name': 'MANUAL_VERIFICATION',
                'issuer_reff': f'MANUAL_{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'buyer_reff': f'USER_{matched_payment["user_id"]}'
            }
            
            # Process payment
            await self._process_manual_payment(matched_payment, mock_mutasi)
            
            return {
                'success': True,
                'message': f'Payment berhasil diverifikasi untuk user {matched_payment["user_id"]}',
                'payment': matched_payment
            }
            
        except Exception as e:
            logging.error(f"Error in manual verification: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }
    
    async def _process_manual_payment(self, payment: dict, mutasi: dict):
        """Process manual payment verification"""
        try:
            # Save transaction
            self.payment_service.save_transaction(payment, mutasi)
            
            # Remove from pending
            self.payment_service.remove_pending_payment(payment['payment_id'])
            
            # Cleanup amount lock
            self.payment_service.cleanup_amount_lock(payment['amount'])
            
            # Activate premium
            await self.premium_service.activate_premium(
                user_id=payment['user_id'],
                duration_seconds=payment['duration_seconds'],
                payment_method='MANUAL_VERIFICATION',
                transaction_id=mutasi['issuer_reff']
            )
            
            # Send success message to user
            try:
                success_message = (
                    "✅ <b>PEMBAYARAN BERHASIL DIVERIFIKASI!</b>\n\n"
                    f"💰 <b>Nominal:</b> Rp {payment['amount']:,}\n"
                    f"📦 <b>Paket:</b> {payment['package_name']}\n"
                    f"⏰ <b>Durasi:</b> {payment['duration_text']}\n"
                    f"🔑 <b>Status:</b> Premium Aktif\n\n"
                    "🎉 Selamat! Akun Anda telah diupgrade ke Premium.\n"
                    "Silakan gunakan semua fitur premium yang tersedia."
                )
                
                await self.bot.send_message(
                    chat_id=payment['user_id'],
                    text=success_message,
                    parse_mode='HTML'
                )
                
            except Exception as e:
                logging.error(f"Error sending success message: {e}")
            
        except Exception as e:
            logging.error(f"Error processing manual payment: {e}")
            raise
    
    async def list_pending_payments(self) -> list:
        """Get list of all pending payments"""
        try:
            pending_payments = self.payment_service.get_pending_payments()
            return pending_payments
        except Exception as e:
            logging.error(f"Error getting pending payments: {e}")
            return []
    
    async def verify_payment_by_user(self, user_id: int) -> dict:
        """
        Manual verification berdasarkan user ID
        """
        try:
            # Get pending payments for user
            pending_payments = self.payment_service.get_pending_payments()
            user_payments = [p for p in pending_payments if p['user_id'] == user_id]
            
            if not user_payments:
                return {
                    'success': False,
                    'message': f'Tidak ditemukan pending payment untuk user {user_id}'
                }
            
            if len(user_payments) > 1:
                return {
                    'success': False,
                    'message': f'User {user_id} memiliki {len(user_payments)} pending payments. Gunakan verify_payment_by_amount.',
                    'payments': user_payments
                }
            
            # Process single payment
            payment = user_payments[0]
            result = await self.verify_payment_by_amount(payment['amount'], user_id)
            return result
            
        except Exception as e:
            logging.error(f"Error in user verification: {e}")
            return {
                'success': False,
                'message': f'Error: {str(e)}'
            }

# Global instance
manual_verifier = None

def get_manual_verifier(bot: Bot) -> ManualPaymentVerification:
    """Get global manual verifier instance"""
    global manual_verifier
    if manual_verifier is None:
        manual_verifier = ManualPaymentVerification(bot)
    return manual_verifier

# Admin command handlers
async def handle_manual_verify_amount(message: Message, amount: int):
    """Handler untuk command manual verify by amount"""
    try:
        verifier = get_manual_verifier(message.bot)
        result = await verifier.verify_payment_by_amount(amount)
        
        if result['success']:
            await message.reply(f"✅ {result['message']}")
        else:
            await message.reply(f"❌ {result['message']}")
            
    except Exception as e:
        await message.reply(f"❌ Error: {str(e)}")

async def handle_manual_verify_user(message: Message, user_id: int):
    """Handler untuk command manual verify by user"""
    try:
        verifier = get_manual_verifier(message.bot)
        result = await verifier.verify_payment_by_user(user_id)
        
        if result['success']:
            await message.reply(f"✅ {result['message']}")
        else:
            await message.reply(f"❌ {result['message']}")
            
    except Exception as e:
        await message.reply(f"❌ Error: {str(e)}")

async def handle_list_pending(message: Message):
    """Handler untuk list pending payments"""
    try:
        verifier = get_manual_verifier(message.bot)
        pending = await verifier.list_pending_payments()
        
        if not pending:
            await message.reply("📝 Tidak ada pending payments")
            return
        
        text = "📝 <b>PENDING PAYMENTS:</b>\n\n"
        for i, payment in enumerate(pending, 1):
            text += (
                f"{i}. User: {payment['user_id']}\n"
                f"   Amount: Rp {payment['amount']:,}\n"
                f"   Package: {payment['package_name']}\n"
                f"   Created: {payment['created_at']}\n\n"
            )
        
        await message.reply(text, parse_mode='HTML')
        
    except Exception as e:
        await message.reply(f"❌ Error: {str(e)}")
