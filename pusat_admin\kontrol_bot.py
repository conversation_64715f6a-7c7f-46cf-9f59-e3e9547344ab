"""
Maintenance Module - Fitur maintenance bot
"""

import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext

from .states import AdminStates
from .keyboards import create_maintenance_menu_keyboard, create_back_keyboard
from .utils import log_bot
from .database import db
from .auth_admin import is_admin, is_super_admin, log_admin_activity, get_admin_permissions

router = Router()


def get_maintenance_status():
    """Ambil status maintenance"""
    try:
        lines = db.read_lines("bot_status.txt")
        if lines and lines[0].strip().lower() == "maintenance":
            return True
        return False
    except:
        return False


def set_maintenance_status(status: bool):
    """Set status maintenance"""
    try:
        status_text = "maintenance" if status else "active"
        return db.write_lines("bot_status.txt", [status_text])
    except:
        return False


def get_maintenance_message():
    """Ambil pesan maintenance"""
    try:
        lines = db.read_lines("maintenance.txt")
        if lines:
            # Join semua baris dengan newline untuk support multi-line
            return "\n".join(lines)
        return "sabar gua tester dulu"
    except:
        return "sabar gua tester dulu"


def set_maintenance_message(message: str):
    """Set pesan maintenance"""
    try:
        # Split message by newline untuk support multi-line
        lines = message.split('\n')
        return db.write_lines("maintenance.txt", lines)
    except:
        return False


@router.callback_query(F.data == "admin_maintenance")
async def show_maintenance_menu(callback: types.CallbackQuery, state: FSMContext):
    """Menu maintenance utama"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    # Ambil status maintenance
    is_maintenance = get_maintenance_status()
    maintenance_msg = get_maintenance_message()

    status_emoji = "🔴" if is_maintenance else "🟢"
    status_text = "AKTIF" if is_maintenance else "NONAKTIF"

    maintenance_text = f"""🔧 **MAINTENANCE**

📊 {status_emoji} Mode: {status_text}

📝 **Pesan:**
{maintenance_msg}"""

    try:
        await callback.message.edit_text(
            maintenance_text,
            reply_markup=create_maintenance_menu_keyboard(),
            parse_mode="Markdown"
        )
    except Exception as e:
        if "message is not modified" in str(e).lower():
            # Pesan sudah sama, tidak perlu edit
            pass
        else:
            logging.error(f"Error editing maintenance menu: {e}")

    await callback.answer()





@router.callback_query(F.data == "maintenance_on")
async def activate_maintenance(callback: types.CallbackQuery, state: FSMContext):
    """Aktifkan maintenance mode"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    success = set_maintenance_status(True)
    
    if success:
        await callback.answer("✅ Maintenance ON")
        log_admin_activity(username, "Aktifkan maintenance mode")
    else:
        await callback.answer("❌ Gagal aktifkan")
    
    # Refresh menu dengan delay kecil untuk menghindari conflict
    import asyncio
    await asyncio.sleep(0.1)
    await show_maintenance_menu(callback, state)


@router.callback_query(F.data == "maintenance_off")
async def deactivate_maintenance(callback: types.CallbackQuery, state: FSMContext):
    """Nonaktifkan maintenance mode"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    success = set_maintenance_status(False)
    
    if success:
        await callback.answer("✅ Maintenance OFF")
        log_admin_activity(username, "Nonaktifkan maintenance mode")
    else:
        await callback.answer("❌ Gagal nonaktifkan")
    
    # Refresh menu dengan delay kecil untuk menghindari conflict
    import asyncio
    await asyncio.sleep(0.1)
    await show_maintenance_menu(callback, state)


@router.callback_query(F.data == "maintenance_edit")
async def edit_maintenance_message_prompt(callback: types.CallbackQuery, state: FSMContext):
    """Prompt edit pesan maintenance"""
    username = callback.from_user.username or ""
    if not is_admin(username):
        await callback.answer("❌ Akses ditolak", show_alert=True)
        return

    current_message = get_maintenance_message()

    edit_text = f"""✏️ **PESAN MAINTENANCE**

Pesan saat ini:
{current_message}

Masukkan pesan baru:
"""

    try:
        await callback.message.edit_text(
            edit_text,
            reply_markup=create_back_keyboard("admin_maintenance"),
            parse_mode="Markdown"
        )
    except Exception as e:
        if "message is not modified" in str(e).lower():
            # Pesan sudah sama, tidak perlu edit
            pass
        else:
            logging.error(f"Error editing maintenance edit menu: {e}")

    await state.set_state(AdminStates.waiting_maintenance_message)
    await callback.answer()


@router.message(AdminStates.waiting_maintenance_message, F.chat.type == "private")
async def process_maintenance_message(message: types.Message, state: FSMContext):
    """Proses pesan maintenance baru"""
    username = message.from_user.username or ""
    if not is_admin(username):
        await message.answer("❌ Akses ditolak")
        await state.clear()
        return

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    new_message = message.text.strip()
    if not new_message:
        await message.answer("Pesan tidak boleh kosong. Masukkan pesan maintenance:")
        return

    # Simpan pesan baru
    success = set_maintenance_message(new_message)

    if success:
        await message.answer(
            f"""✅ **PESAN MAINTENANCE DIUPDATE**

**Pesan baru:**
{new_message}""",
            reply_markup=create_maintenance_menu_keyboard(),
            parse_mode="Markdown"
        )
        log_bot(f"Pesan maintenance diupdate oleh @{username}")
        log_admin_activity(username, "Update pesan maintenance")
    else:
        await message.answer("❌ Gagal simpan pesan")

    await state.clear()



