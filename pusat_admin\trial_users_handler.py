"""
Trial Users Handler - Handler untuk menampilkan daftar trial users
"""

import logging
from typing import List, Dict, Tuple
from aiogram import types
from aiogram.types import InlineKeyboardMarkup
from .keyboards import create_trial_users_keyboard


class TrialUsersHandler:
    """Handler untuk mengelola tampilan trial users"""
    
    def __init__(self, bot):
        self.bot = bot
        self.users_per_page = 50  # 50 user per halaman
        self._cached_users = None  # Cache untuk data users
    
    async def show_trial_users(self, callback: types.CallbackQuery, page: int = 1, is_refresh: bool = False):
        """Tampilkan daftar trial users dengan pagination"""
        try:
            # Use cache if available and not refresh
            if self._cached_users is None or is_refresh:
                trial_users = await self._get_all_trial_users()

                # Get usernames from total_users.txt (FAST - no API calls)
                self._get_usernames_from_database(trial_users)

                self._cached_users = trial_users  # Cache the data with usernames
            else:
                trial_users = self._cached_users

            if not trial_users:
                empty_message = "📊 Trial Users\n\n❌ Tidak ada user yang pernah menggunakan trial."
                try:
                    await callback.message.edit_text(
                        empty_message,
                        reply_markup=create_trial_users_keyboard(1, 1, False, False)
                    )
                    # No popup for refresh - data already updated in message
                except Exception as edit_error:
                    if "message is not modified" in str(edit_error).lower():
                        # Message already shows current state, no popup needed
                        pass
                    else:
                        raise edit_error
                return
            
            # Calculate pagination
            total_users = len(trial_users)
            total_pages = (total_users + self.users_per_page - 1) // self.users_per_page
            start_idx = (page - 1) * self.users_per_page
            end_idx = min(start_idx + self.users_per_page, total_users)
            
            # Get users for current page (usernames already cached)
            page_users = trial_users[start_idx:end_idx]

            # Count active and expired users
            active_count = sum(1 for user in trial_users if user['status'] == 'active')
            expired_count = total_users - active_count

            # Build message with new format
            message = f"📊 Trial Users {total_users}\n"
            message += f"🟢 {active_count} | 🔴 {expired_count}\n"
            message += f"hal {page}/{total_pages}\n\n"

            for i, user_data in enumerate(page_users, start_idx + 1):
                status_emoji = "🟢" if user_data['status'] == 'active' else "🔴"
                username = user_data['display_name']
                start_time = user_data['activated_display']
                end_time = user_data['expired_display']

                message += f"{i}. {status_emoji} {username}\n"
                message += f"{start_time} → {end_time}\n\n"
            
            # Create keyboard
            has_prev = page > 1
            has_next = page < total_pages
            keyboard = create_trial_users_keyboard(page, total_pages, has_prev, has_next)
            
            # Check if message content is different to avoid "message is not modified" error
            try:
                await callback.message.edit_text(
                    message,
                    reply_markup=keyboard
                )
                # No popup needed - data is already updated in message
            except Exception as edit_error:
                if "message is not modified" in str(edit_error).lower():
                    # Message content is the same, no popup needed
                    pass
                else:
                    # Other error, re-raise
                    raise edit_error
            
        except Exception as e:
            error_message = "❌ Terjadi kesalahan saat mengambil data trial users."
            try:
                await callback.message.edit_text(
                    error_message,
                    reply_markup=create_trial_users_keyboard(1, 1, False, False)
                )
            except Exception as edit_error:
                pass
    
    async def _get_all_trial_users(self) -> List[Dict]:
        """Get semua trial users dari database - FAST VERSION"""
        try:
            trial_users = []

            # Read trial database - simple and fast
            try:
                with open("payment/database/trial_users.txt", 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue

                        parts = line.split('|')
                        if len(parts) >= 4:
                            try:
                                user_id = int(parts[0])
                                activated_date = parts[1].strip()
                                expired_date = parts[2].strip()
                                status = parts[3].strip()

                                # Use User ID first for speed, get real name later
                                display_name = f"User {user_id}"
                                activated_display = self._format_date_display(activated_date)
                                expired_display = self._format_date_display(expired_date)

                                trial_users.append({
                                    'user_id': user_id,
                                    'display_name': display_name,
                                    'activated_date': activated_date,
                                    'expired_date': expired_date,
                                    'activated_display': activated_display,
                                    'expired_display': expired_display,
                                    'status': status
                                })

                            except (ValueError, IndexError):
                                # Skip invalid lines silently
                                continue

            except FileNotFoundError:
                return []
            except Exception as e:
                return []

            # Sort by user_id (simple sort)
            trial_users.sort(key=lambda x: x['user_id'], reverse=True)

            return trial_users

        except Exception as e:
            return []

    def _get_usernames_from_database(self, trial_users):
        """Get usernames from total_users.txt - INSTANT, no API calls"""
        try:
            # Read total_users.txt untuk mapping user_id -> username/nama
            user_mapping = {}
            try:
                with open("pusat_admin/database/total_users.txt", 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if not line:
                            continue

                        parts = line.split('|')
                        if len(parts) >= 4:
                            try:
                                user_id = int(parts[0])
                                username = parts[1].strip()
                                first_name = parts[2].strip()
                                last_name = parts[3].strip()

                                # Priority: username > full_name > first_name
                                if username:
                                    display_name = f"@{username}"  # Username dikasih @
                                elif first_name or last_name:
                                    full_name = f"{first_name} {last_name}".strip()
                                    display_name = full_name  # Nama pengguna TANPA @
                                else:
                                    display_name = f"User {user_id}"  # Fallback

                                user_mapping[user_id] = display_name

                            except (ValueError, IndexError):
                                continue

            except FileNotFoundError:
                return

            # Update display names untuk trial users
            for user_data in trial_users:
                user_id = user_data['user_id']
                if user_id in user_mapping:
                    user_data['display_name'] = user_mapping[user_id]
                # Jika tidak ada di mapping, tetap pakai "User {id}"

        except Exception as e:
            pass

    async def _get_usernames_for_all_users(self, trial_users):
        """Get usernames for ALL users once - no fallback to User ID"""
        import asyncio

        # Process in batches of 20 for better performance
        batch_size = 20
        total_batches = (len(trial_users) + batch_size - 1) // batch_size

        for i in range(0, len(trial_users), batch_size):
            batch = trial_users[i:i + batch_size]
            batch_num = i // batch_size + 1

            # Create tasks for this batch
            tasks = []
            for user_data in batch:
                task = asyncio.create_task(
                    self._get_user_display_name_force(user_data['user_id'])
                )
                tasks.append((user_data, task))

            # Wait for batch with longer timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                    timeout=10.0  # 10 second per batch
                )

                # Update display names for successful results
                for (user_data, task), result in zip(tasks, results):
                    if not isinstance(result, Exception):
                        user_data['display_name'] = result
                    # If failed, will show "Unknown User" instead of User ID

            except asyncio.TimeoutError:
                continue

    async def _get_usernames_for_page(self, page_users):
        """Get usernames for current page users - optimized for 50 users"""
        import asyncio
        logging.info(f"Getting usernames for {len(page_users)} users...")

        # Process in batches of 10 for better performance
        batch_size = 10
        for i in range(0, len(page_users), batch_size):
            batch = page_users[i:i + batch_size]

            # Create tasks for this batch
            tasks = []
            for user_data in batch:
                task = asyncio.create_task(
                    self._get_user_display_name_fast(user_data['user_id'])
                )
                tasks.append((user_data, task))

            # Wait for batch with short timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                    timeout=2.0  # 2 second per batch
                )

                # Update display names for successful results
                for (user_data, task), result in zip(tasks, results):
                    if not isinstance(result, Exception):
                        user_data['display_name'] = result

            except asyncio.TimeoutError:
                logging.info(f"Batch {i//batch_size + 1} timed out, keeping User IDs")
                continue

        logging.info("Username fetching completed")

    async def _get_user_display_name_force(self, user_id: int) -> str:
        """Get display name untuk user - PASTI dapat nama karena first_name wajib di Telegram"""
        import asyncio
        import logging

        max_retries = 5  # Lebih banyak retry
        for attempt in range(max_retries):
            try:
                user = await asyncio.wait_for(
                    self.bot.get_chat(user_id),
                    timeout=3.0  # Timeout lebih lama
                )

                # Priority: username > full_name > first_name
                # first_name PASTI ada karena wajib saat registrasi Telegram
                if user.username:
                    return f"@{user.username}"
                elif user.full_name and user.full_name.strip():
                    return user.full_name.strip()
                elif user.first_name:  # PASTI ada
                    return user.first_name.strip()
                else:
                    # Ini seharusnya tidak pernah terjadi
                    logging.error(f"User {user_id} tidak punya first_name - ini aneh!")
                    return f"User {user_id}"  # Fallback untuk debug

            except asyncio.TimeoutError:
                logging.warning(f"Timeout getting user {user_id}, attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5)  # Wait before retry
                    continue
                else:
                    # Timeout semua attempt - kemungkinan user block bot atau delete account
                    logging.error(f"All attempts failed for user {user_id} - user might have blocked bot")
                    return f"User {user_id}"  # Untuk debug

            except Exception as e:
                logging.warning(f"Error getting user {user_id}: {e}, attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5)  # Wait before retry
                    continue
                else:
                    # Error semua attempt
                    logging.error(f"All attempts failed for user {user_id}: {e}")
                    return f"User {user_id}"  # Untuk debug

        # Seharusnya tidak pernah sampai sini
        return f"User {user_id}"

    async def _get_user_display_name_fast(self, user_id: int) -> str:
        """Get display name untuk user - FAST VERSION dengan timeout sangat pendek"""
        try:
            # Try to get user info from Telegram dengan timeout sangat pendek
            user = await self.bot.get_chat(user_id)

            # Priority: username > full_name > first_name > user_id
            if user.username:
                return f"@{user.username}"
            elif user.full_name and user.full_name.strip():
                return user.full_name.strip()
            elif user.first_name and user.first_name.strip():
                return user.first_name.strip()
            else:
                return f"User {user_id}"

        except Exception as e:
            # Fallback jika tidak bisa get user info
            return f"User {user_id}"

    async def _get_user_display_name(self, user_id: int) -> str:
        """Get display name untuk user - prioritas username > nama > user_id"""
        try:
            import asyncio

            # Try to get user info from Telegram dengan timeout sangat pendek
            user = await asyncio.wait_for(
                self.bot.get_chat(user_id),
                timeout=0.5  # 0.5 second timeout untuk speed
            )

            # Priority: username > full_name > first_name > user_id
            if user.username:
                return f"@{user.username}"
            elif user.full_name and user.full_name.strip():
                return user.full_name.strip()
            elif user.first_name and user.first_name.strip():
                return user.first_name.strip()
            else:
                return f"User {user_id}"

        except asyncio.TimeoutError:
            # Timeout getting user info
            return f"User {user_id}"
        except Exception as e:
            # Fallback jika tidak bisa get user info (user blocked bot, deleted account, etc)
            return f"User {user_id}"
    
    def _format_date_display(self, date_str: str) -> str:
        """Format tanggal untuk display WIB - MM-DD HH:MM WIB"""
        try:
            from datetime import datetime, timezone, timedelta

            # Parse UTC datetime: "2025-06-09 01:23:45"
            dt = datetime.strptime(date_str.strip(), "%Y-%m-%d %H:%M:%S")
            utc_dt = dt.replace(tzinfo=timezone.utc)

            # Convert ke WIB (+7 jam)
            wib_dt = utc_dt + timedelta(hours=7)

            # Format: "MM-DD HH:MM WIB"
            return wib_dt.strftime("%m-%d %H:%M")

        except Exception:
            # Fallback jika format tidak sesuai
            return date_str


# Global instance
trial_users_handler = None

def get_trial_users_handler(bot):
    """Get trial users handler instance"""
    global trial_users_handler
    if trial_users_handler is None:
        trial_users_handler = TrialUsersHandler(bot)
    return trial_users_handler
