import json
import logging
import os
from datetime import datetime, timezone
from typing import List, Dict, Optional
from .config_payment import PAYMENT_HISTORY_DB


class PaymentHistoryService:
    """Service untuk mengelola riwayat pembayaran"""
    
    def __init__(self):
        # Ensure database file exists
        os.makedirs(os.path.dirname(PAYMENT_HISTORY_DB), exist_ok=True)
        if not os.path.exists(PAYMENT_HISTORY_DB):
            with open(PAYMENT_HISTORY_DB, 'w', encoding='utf-8') as f:
                pass  # Create empty file
    
    def get_user_info(self, user_id: int) -> Dict[str, str]:
        """Ambil informasi user dari database total_users.txt"""
        try:
            from pusat_admin.database import db
            
            # Cari user di total_users.txt
            user_data = db.find_by_field("total_users.txt", 0, str(user_id))
            if user_data and len(user_data['parts']) >= 4:
                username = user_data['parts'][1].strip()
                first_name = user_data['parts'][2].strip()
                last_name = user_data['parts'][3].strip()
                
                # Format username
                display_username = f"@{username}" if username else ""
                
                # Format full name
                full_name = f"{first_name} {last_name}".strip()
                if not full_name:
                    full_name = f"User {user_id}"
                
                return {
                    "username": display_username,
                    "full_name": full_name
                }
        except Exception as e:
            logging.error(f"Error getting user info for {user_id}: {e}")
        
        # Fallback
        return {
            "username": "",
            "full_name": f"User {user_id}"
        }
    
    def add_payment_history(self, payment_id: str, user_id: int, amount: int, status: str = "pending"):
        """Tambah riwayat pembayaran baru"""
        try:
            # Get user info
            user_info = self.get_user_info(user_id)
            
            # Create history entry
            history_entry = {
                "payment_id": payment_id,
                "user_id": user_id,
                "username": user_info["username"],
                "full_name": user_info["full_name"],
                "amount": amount,
                "created_at": datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S"),
                "status": status
            }
            
            # Append to file
            with open(PAYMENT_HISTORY_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(history_entry) + '\n')
            
            # Payment history added
            
        except Exception as e:
            logging.error(f"Error adding payment history: {e}")
    
    def update_payment_status(self, payment_id: str, new_status: str):
        """Update status pembayaran"""
        try:
            histories = self.get_all_payment_histories()
            updated = False
            
            # Update status
            for history in histories:
                if history.get('payment_id') == payment_id:
                    history['status'] = new_status
                    history['updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    updated = True
                    break
            
            if updated:
                # Rewrite file
                with open(PAYMENT_HISTORY_DB, 'w', encoding='utf-8') as f:
                    for history in histories:
                        f.write(json.dumps(history) + '\n')
                
                # logging.info(f"Updated payment status: {payment_id} -> {new_status}")  # Disabled untuk mengurangi noise
            
        except Exception as e:
            logging.error(f"Error updating payment status: {e}")
    
    def get_all_payment_histories(self) -> List[Dict]:
        """Ambil semua riwayat pembayaran"""
        try:
            histories = []
            
            if os.path.exists(PAYMENT_HISTORY_DB):
                with open(PAYMENT_HISTORY_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                history = json.loads(line)
                                histories.append(history)
                            except json.JSONDecodeError:
                                continue
            
            # Sort by created_at descending (newest first)
            histories.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return histories
            
        except Exception as e:
            logging.error(f"Error getting payment histories: {e}")
            return []
    
    def get_payment_histories_paginated(self, page: int = 1, per_page: int = 100) -> Dict:
        """Ambil riwayat pembayaran dengan pagination"""
        try:
            all_histories = self.get_all_payment_histories()
            total_count = len(all_histories)
            
            # Calculate pagination
            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            
            paginated_histories = all_histories[start_index:end_index]
            
            total_pages = (total_count + per_page - 1) // per_page
            
            return {
                "histories": paginated_histories,
                "current_page": page,
                "total_pages": total_pages,
                "total_count": total_count,
                "per_page": per_page,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
            
        except Exception as e:
            logging.error(f"Error getting paginated histories: {e}")
            return {
                "histories": [],
                "current_page": 1,
                "total_pages": 0,
                "total_count": 0,
                "per_page": per_page,
                "has_next": False,
                "has_prev": False
            }
    
    def get_payment_statistics(self) -> Dict:
        """Ambil statistik pembayaran dari history"""
        try:
            histories = self.get_all_payment_histories()
            
            total_payments = len(histories)
            total_amount = sum(h.get('amount', 0) for h in histories)
            
            # Count by status
            status_counts = {}
            for history in histories:
                status = history.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                "total_payments": total_payments,
                "total_amount": total_amount,
                "status_counts": status_counts
            }
            
        except Exception as e:
            logging.error(f"Error getting payment statistics: {e}")
            return {
                "total_payments": 0,
                "total_amount": 0,
                "status_counts": {}
            }
    
    def search_payment_histories(self, query: str, page: int = 1, per_page: int = 100) -> Dict:
        """Cari riwayat pembayaran berdasarkan query"""
        try:
            all_histories = self.get_all_payment_histories()
            query_lower = query.lower().strip()
            
            # Filter histories based on query
            filtered_histories = []
            for history in all_histories:
                # Search in payment_id, username, full_name
                if (query_lower in history.get('payment_id', '').lower() or
                    query_lower in history.get('username', '').lower() or
                    query_lower in history.get('full_name', '').lower() or
                    query_lower in str(history.get('user_id', ''))):
                    filtered_histories.append(history)
            
            # Apply pagination to filtered results
            total_count = len(filtered_histories)
            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            
            paginated_histories = filtered_histories[start_index:end_index]
            total_pages = (total_count + per_page - 1) // per_page
            
            return {
                "histories": paginated_histories,
                "current_page": page,
                "total_pages": total_pages,
                "total_count": total_count,
                "per_page": per_page,
                "has_next": page < total_pages,
                "has_prev": page > 1,
                "query": query
            }
            
        except Exception as e:
            logging.error(f"Error searching payment histories: {e}")
            return {
                "histories": [],
                "current_page": 1,
                "total_pages": 0,
                "total_count": 0,
                "per_page": per_page,
                "has_next": False,
                "has_prev": False,
                "query": query
            }
