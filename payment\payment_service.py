import aiohttp
import asyncio
import logging
import json
import time
import random
from datetime import datetime
from typing import List, Dict, Optional
from .config_payment import (
    PAYMENT_TOKEN,
    PAYMENT_API_KEY,
    PAYMENT_MERCHANT_ID,
    PAYMENT_BASE_URL,
    PAYMENT_MUTASI_ENDPOINT,
    PAYMENT_ORKUT_TOKEN,     # Token Orkut untuk key refresh
    PAYMENT_ORKUT_USERNAME,  # Username Orkut
    PENDING_PAYMENTS_DB,
    TRANSACTIONS_DB,
    PROCESSED_MUTATIONS_DB,
    PREMIUM_PRICE,
    # Legacy aliases
    ORDERKUOTA_API_KEY,
    ORDERKUOTA_MERCHANT_CODE,
    ORDERKUOTA_QRIS_URL
)

# Global variable untuk tracking pending payments (anti-collision)
pending_amounts = {}

# Global variable untuk tracking processed mutations (in-memory cache)
processed_mutations_cache = set()

class PaymentService:
    def __init__(self):
        # New payment API configuration
        self.token = PAYMENT_TOKEN  # TokenKey untuk header
        self.orkut_token = PAYMENT_ORKUT_TOKEN  # Token Orkut untuk key refresh
        self.orkut_username = PAYMENT_ORKUT_USERNAME  # Username Orkut
        self.api_key = PAYMENT_API_KEY
        self.merchant_id = PAYMENT_MERCHANT_ID
        self.base_url = PAYMENT_BASE_URL
        self.mutasi_endpoint = PAYMENT_MUTASI_ENDPOINT

        # Legacy compatibility
        self.merchant_code = ORDERKUOTA_MERCHANT_CODE

        self.last_check_time = None
        self._load_processed_mutations_cache()

        # Key refresh system (CRITICAL untuk API berfungsi)
        self.current_merchant_id = None
        self.current_key = None
        self.key_expires_at = None
        self.key_refresh_lock = asyncio.Lock()

    async def _refresh_api_key(self) -> bool:
        """
        CRITICAL: Refresh API key dari /api/key-orkut
        WAJIB dipanggil sebelum akses /api/mutasi-orkut atau /api/qr-orkut
        Key expires dalam 1 menit!
        """
        async with self.key_refresh_lock:
            try:
                # Check if key masih valid (buffer 10 detik)
                if (self.key_expires_at and
                    datetime.now().timestamp() < (self.key_expires_at - 10)):
                    return True  # Key masih valid

                url = f"{self.base_url}/api/key-orkut"

                # Payload untuk key-orkut endpoint dengan format yang BENAR (underscore!)
                payload = {
                    "username_orkut": self.orkut_username,  # WARstoreUP
                    "token_orkut": self.orkut_token         # 2528455:NJ7MwxG2PgDurdWmoyYsl68Svk3qbaFc
                }

                headers = {
                    'Content-Type': 'application/json',
                    'TokenKey': self.token  # f0f7444a811741d9289ccbae9db9a481de0a1581a5b7d99dec93437549cf26c6
                }

                # Silent key refresh

                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=payload, headers=headers) as response:
                        if response.status == 200:
                            try:
                                data = await response.json()

                                if data.get('success') == True:
                                    self.current_merchant_id = data.get('merchantId')
                                    self.current_key = data.get('key')
                                    # Key expires dalam 1 menit
                                    self.key_expires_at = datetime.now().timestamp() + 60

                                    # Silent success

                                    return True
                                else:
                                    logging.error(f"❌ Key refresh failed: {data}")
                                    return False

                            except Exception as e:
                                logging.error(f"❌ Error parsing key refresh response: {e}")
                                return False
                        else:
                            text = await response.text()
                            logging.error(f"❌ Key refresh HTTP error {response.status}: {text}")
                            return False

            except Exception as e:
                logging.error(f"❌ Exception in key refresh: {e}")
                return False
        
    def generate_unique_amount(self, user_id: int, base_price: int = None) -> int:
        """Generate unique payment amount (anti-collision)"""
        global pending_amounts

        if base_price is None:
            base_price = PREMIUM_PRICE

        max_attempts = 50

        for attempt in range(max_attempts):
            # Generate random suffix 1-999
            random_suffix = random.randint(1, 999)
            unique_amount = base_price + random_suffix
            amount_key = str(unique_amount)

            # Check if amount already used by another user
            if amount_key not in pending_amounts:
                # Lock this amount for this user
                pending_amounts[amount_key] = {
                    "user_id": user_id,
                    "created_at": datetime.now().isoformat(),
                    "amount": unique_amount
                }

                # Amount generated successfully
                return unique_amount
        
        # If 50 attempts failed (very rare)
        raise Exception("Cannot generate unique amount, all slots busy")
    
    def create_pending_payment(self, payment_id: str, user_id: int, amount: int, qris_message_id: int):
        """Create pending payment record"""
        try:
            payment_data = {
                "payment_id": payment_id,
                "user_id": user_id,
                "amount": amount,
                "qris_message_id": qris_message_id,
                "active_message_ids": [],  # List of "Pembayaran Aktif" message IDs
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "pending"
            }
            
            # Read existing pending payments
            pending_payments = self.get_pending_payments()
            
            # Add new payment
            pending_payments.append(payment_data)
            
            # Save to file
            with open(PENDING_PAYMENTS_DB, 'w', encoding='utf-8') as f:
                for payment in pending_payments:
                    f.write(json.dumps(payment) + '\n')
            
            # Payment created successfully
            
        except Exception as e:
            logging.error(f"Error creating pending payment: {e}")
    
    def get_pending_payments(self) -> List[Dict]:
        """Get all pending payments"""
        try:
            pending_payments = []
            
            try:
                with open(PENDING_PAYMENTS_DB, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            payment = json.loads(line)
                            if payment.get('status') == 'pending':
                                pending_payments.append(payment)
            except FileNotFoundError:
                pass
            
            return pending_payments
            
        except Exception as e:
            logging.error(f"Error getting pending payments: {e}")
            return []
    
    def remove_pending_payment(self, payment_id: str):
        """Remove pending payment after successful payment"""
        try:
            pending_payments = self.get_pending_payments()
            
            # Filter out the completed payment
            updated_payments = [p for p in pending_payments if p['payment_id'] != payment_id]
            
            # Save updated list
            with open(PENDING_PAYMENTS_DB, 'w', encoding='utf-8') as f:
                for payment in updated_payments:
                    f.write(json.dumps(payment) + '\n')
            
            # logging.info(f"Removed pending payment: {payment_id}")  # Disabled untuk mengurangi noise
            
        except Exception as e:
            logging.error(f"Error removing pending payment: {e}")

    def add_active_message_id(self, payment_id: str, message_id: int):
        """Add active message ID to pending payment"""
        try:
            pending_payments = self.get_pending_payments()

            # Find and update the payment
            for payment in pending_payments:
                if payment['payment_id'] == payment_id:
                    # Initialize list if not exists
                    if 'active_message_ids' not in payment:
                        payment['active_message_ids'] = []

                    # Add new message ID
                    payment['active_message_ids'].append(message_id)
                    break

            # Save updated list
            with open(PENDING_PAYMENTS_DB, 'w', encoding='utf-8') as f:
                for payment in pending_payments:
                    f.write(json.dumps(payment) + '\n')

        except Exception as e:
            logging.error(f"Error adding active message ID: {e}")

    def cleanup_amount_lock(self, amount: int):
        """Cleanup amount lock from global pending_amounts"""
        global pending_amounts
        amount_key = str(amount)
        if amount_key in pending_amounts:
            del pending_amounts[amount_key]
            # Amount lock cleaned up
    
    def save_transaction(self, payment_data: Dict, mutasi_data: Dict):
        """Save completed transaction"""
        try:
            transaction = {
                "payment_id": payment_data['payment_id'],
                "user_id": payment_data['user_id'],
                "amount": payment_data['amount'],
                "paid_amount": int(mutasi_data['amount']),
                "payment_method": mutasi_data['brand_name'],
                "issuer_reff": mutasi_data['issuer_reff'],
                "buyer_reff": mutasi_data['buyer_reff'],
                "payment_date": mutasi_data['date'],
                "created_at": payment_data['created_at'],
                "completed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "completed"
            }
            
            # Append to transactions file
            with open(TRANSACTIONS_DB, 'a', encoding='utf-8') as f:
                f.write(json.dumps(transaction) + '\n')
            
            # logging.info(f"Saved transaction: {payment_data['payment_id']}")  # Disabled untuk mengurangi noise
            
        except Exception as e:
            logging.error(f"Error saving transaction: {e}")
    
    async def check_new_payments(self) -> List[Dict]:
        """Check for new payments from Payment API"""
        try:
            # Try to refresh API key, but fallback to original if fails
            key_refreshed = await self._refresh_api_key()

            url = f"{self.base_url}{self.mutasi_endpoint}"

            if key_refreshed and self.current_merchant_id and self.current_key:
                # Use refreshed credentials
                payload = {
                    "merchantId": self.current_merchant_id,
                    "key": self.current_key
                }
                # Using refreshed credentials
            else:
                # Fallback to original credentials
                payload = {
                    "merchantId": self.merchant_id,
                    "key": self.api_key
                }
                logging.warning("⚠️ Using fallback credentials (key refresh failed)")

            # Headers dengan TokenKey authentication
            headers = {
                'Content-Type': 'application/json',
                'TokenKey': self.token
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        try:
                            # Try normal JSON parsing first
                            data = await response.json()
                        except Exception as json_error:
                            # Manual JSON parsing if content-type is wrong
                            try:
                                text_content = await response.text()
                                import json
                                data = json.loads(text_content)
                                # logging.info("✅ Manual JSON parsing successful in check_new_payments")  # Disabled untuk mengurangi noise
                            except Exception as manual_error:
                                logging.error(f"Failed to parse JSON: {json_error} | Manual: {manual_error}")
                                return []
                        
                        if data.get('status') == True:
                            # Try different possible data field names
                            mutasi_list = []
                            possible_data_fields = ['data', 'mutasi', 'transactions', 'result', 'payload']

                            for field in possible_data_fields:
                                if field in data and isinstance(data[field], list):
                                    mutasi_list = data[field]
                                    break

                            if not mutasi_list:
                                # Only log first occurrence of no data
                                if not hasattr(self, '_no_data_logged'):
                                    logging.warning(f"❌ No payment data found (will check silently)")
                                    self._no_data_logged = True
                                return []

                            # Filter new payments since last check
                            new_payments = []
                            current_time = datetime.now()
                            
                            for mutasi in mutasi_list:
                                # Parse payment date
                                try:
                                    payment_time = datetime.strptime(mutasi['date'], "%Y-%m-%d %H:%M:%S")
                                except:
                                    continue  # Skip if date parsing fails
                                
                                # Only process payments from last 10 minutes if this is not first check
                                if self.last_check_time:
                                    time_diff = (current_time - payment_time).total_seconds()
                                    if time_diff > 600:  # 10 minutes
                                        continue
                                
                                # Only process credit transactions
                                if mutasi.get('type') == 'CR':
                                    new_payments.append(mutasi)
                            
                            self.last_check_time = current_time

                            # Silent processing - only log payment matches

                            return new_payments
                        else:
                            logging.error(f"❌ API Error: {data}")
                            return []
                    else:
                        # logging.error(f"HTTP Error: {response.status}")  # Disabled - API unavailable
                        return []
                        
        except Exception:
            # logging.error(f"Error checking payments: {e}")  # Disabled - API unavailable
            return []
    
    async def process_payment_match(self, mutasi: Dict) -> Optional[Dict]:
        """Process payment matching with pending payments"""
        try:
            paid_amount = int(mutasi['amount'])
            pending_payments = self.get_pending_payments()
            
            # Find matching pending payment
            for payment in pending_payments:
                if payment['amount'] == paid_amount:
                    # Match found!
                    # logging.info(f"Payment match found: {payment['payment_id']} - Rp {paid_amount}")  # Disabled untuk mengurangi noise
                    
                    # Save transaction
                    self.save_transaction(payment, mutasi)
                    
                    # Remove from pending
                    self.remove_pending_payment(payment['payment_id'])
                    
                    # Cleanup amount lock
                    self.cleanup_amount_lock(paid_amount)
                    
                    return payment
            
            # No match found
            # logging.info(f"No match for payment: Rp {paid_amount}")  # Disabled untuk mengurangi noise
            return None
            
        except Exception as e:
            logging.error(f"Error processing payment match: {e}")
            return None

    def _load_processed_mutations_cache(self):
        """Load processed mutations into memory cache"""
        global processed_mutations_cache
        try:
            with open(PROCESSED_MUTATIONS_DB, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        processed_mutations_cache.add(line)
            logging.debug(f"Loaded {len(processed_mutations_cache)} processed mutations to cache")
        except FileNotFoundError:
            logging.debug("No processed mutations file found, starting fresh")
        except Exception as e:
            logging.error(f"Error loading processed mutations cache: {e}")

    def is_mutation_processed(self, mutasi: Dict) -> bool:
        """Check if mutation has already been processed"""
        global processed_mutations_cache
        try:
            mutation_id = self._get_mutation_id(mutasi)

            is_processed = mutation_id in processed_mutations_cache
            if is_processed:
                logging.debug(f"Mutation already processed: {mutation_id}")
            else:
                logging.debug(f"New mutation: {mutation_id}")

            return is_processed

        except Exception as e:
            logging.error(f"Error checking processed mutation: {e}")
            return False

    def mark_mutation_processed(self, mutasi: Dict):
        """Mark mutation as processed"""
        global processed_mutations_cache
        try:
            mutation_id = self._get_mutation_id(mutasi)

            # Add to memory cache first (immediate effect)
            processed_mutations_cache.add(mutation_id)

            # Append to processed mutations file
            with open(PROCESSED_MUTATIONS_DB, 'a', encoding='utf-8') as f:
                f.write(mutation_id + '\n')

            logging.debug(f"Marked mutation as processed: {mutation_id}")

        except Exception as e:
            logging.error(f"Error marking mutation processed: {e}")

    def _get_mutation_id(self, mutasi: Dict) -> str:
        """Generate unique ID for mutation"""
        # Use combination of amount, date, issuer_reff, and buyer_reff
        return f"{mutasi['amount']}_{mutasi['date']}_{mutasi.get('issuer_reff', '')}_{mutasi.get('buyer_reff', '')}"
