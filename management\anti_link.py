import re
from aiogram import Router, types
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError
from utils.text_cleaner import extract_clean_text_from_message, extract_links_from_formatted_text, ultimate_text_cleaner

router = Router()

# Regex pattern untuk mendeteksi berbagai format URL/link
URL_PATTERNS = [
    # HTTP/HTTPS URLs
    r'https?://[^\s]+',
    # www.domain.com
    r'www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^\s]*',
    # domain.com (tanpa www)
    r'(?<![@\w])[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s]*)?(?!\w)',
    # t.me links (TETAP dihapus - ini link Telegram)
    r't\.me/[^\s]+',
    # bit.ly, tinyurl, dll
    r'(?:bit\.ly|tinyurl\.com|short\.link|goo\.gl|ow\.ly)/[^\s]+',
    # REMOVED: @username mentions (JANGAN dihapus - biarkan mention biasa)
    # REMOVED: IP addresses (JANGAN dihapus - bukan link berbahaya)
]

# Compile regex patterns untuk performa lebih baik
COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE) for pattern in URL_PATTERNS]

# Bot mention detection pattern - 1 kata diakhiri dengan bot/Bot/BOT (bisa diikuti angka/underscore)
BOT_MENTION_PATTERN = re.compile(r'@[a-zA-Z0-9_]+(?:bot|Bot|BOT)(?:[0-9_]*)?(?=\s|$|[^\w])', re.IGNORECASE)

def contains_bot_mention(text: str) -> bool:
    """
    Deteksi mention bot dengan kriteria spesifik:
    - Diawali @
    - 1 kata (tanpa spasi di tengah)
    - Diakhiri bot/Bot/BOT

    Args:
        text (str): Text yang akan dicek

    Returns:
        bool: True jika mengandung mention bot, False jika tidak
    """
    if not text:
        return False

    # Clean text dari formatting untuk deteksi yang akurat
    cleaned_text = ultimate_text_cleaner(text)

    # Cek dengan pattern bot mention
    return bool(BOT_MENTION_PATTERN.search(cleaned_text))

def contains_link(text: str) -> bool:
    """
    Mengecek apakah text mengandung link/URL dengan enhanced detection

    Args:
        text (str): Text yang akan dicek

    Returns:
        bool: True jika mengandung link, False jika tidak
    """
    if not text:
        return False

    # Extract links dari formatted text (preserve URLs dari markdown/HTML links)
    cleaned_text = extract_links_from_formatted_text(text)

    # Apply unicode normalization
    from utils.text_cleaner import normalize_unicode_text
    cleaned_text = normalize_unicode_text(cleaned_text)

    # Cek email dulu - jika ada email, skip deteksi domain
    email_pattern = r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
    if re.search(email_pattern, cleaned_text, re.IGNORECASE):
        # Jika ada email, hanya cek pattern selain domain
        # Pattern index: 0=http, 1=www, 2=domain, 3=t.me, 4=shortener
        patterns_to_check = [0, 1, 3, 4]  # Skip domain pattern (index 2)
    else:
        # Jika tidak ada email, cek semua pattern
        patterns_to_check = range(len(COMPILED_PATTERNS))

    # Cek dengan pattern yang dipilih pada cleaned text
    for i in patterns_to_check:
        if COMPILED_PATTERNS[i].search(cleaned_text):
            return True

    return False

async def is_group_admin(message: types.Message, user_id: int) -> bool:
    """
    Mengecek apakah user adalah admin di grup
    
    Args:
        message (types.Message): Message object untuk akses bot
        user_id (int): ID user yang akan dicek
        
    Returns:
        bool: True jika user adalah admin, False jika tidak
    """
    try:
        chat_member = await message.bot.get_chat_member(message.chat.id, user_id)
        return chat_member.status in ["administrator", "creator"]
    except Exception as e:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False

def extract_all_text_from_message_enhanced(message: types.Message) -> str:
    """
    Enhanced text extraction dengan comprehensive cleaning

    Args:
        message (types.Message): Message object

    Returns:
        str: Single cleaned text dari semua sumber
    """
    # Gunakan enhanced text extraction dari utils
    return extract_clean_text_from_message(message)

def extract_all_text_from_message(message: types.Message) -> list:
    """
    Legacy function untuk backward compatibility
    Mengekstrak semua text dari message (termasuk caption, forward, dll)

    Args:
        message (types.Message): Message object

    Returns:
        list: List berisi semua text yang ditemukan (untuk compatibility)
    """
    # Gunakan enhanced extraction dan return sebagai list untuk compatibility
    enhanced_text = extract_all_text_from_message_enhanced(message)
    return [enhanced_text] if enhanced_text else []

async def delete_message_safely(message: types.Message) -> bool:
    """
    Menghapus message dengan error handling
    
    Args:
        message (types.Message): Message yang akan dihapus
        
    Returns:
        bool: True jika berhasil dihapus, False jika gagal
    """
    try:
        await message.delete()
        return True
    except TelegramBadRequest as e:
        if "message to delete not found" in str(e).lower():
            # logging.warning(f"Message already deleted: {e}")  # Disabled untuk mengurangi noise
            pass
        elif "not enough rights" in str(e).lower():
            # Silent error - tidak log untuk menjaga terminal rapi
            pass
        else:
            # Silent error - tidak log untuk menjaga terminal rapi
            pass
        return False
    except TelegramForbiddenError as e:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False
    except Exception as e:
        # Silent error - tidak log untuk menjaga terminal rapi
        return False

from aiogram import F

@router.message(F.chat.type.in_(["group", "supergroup"]) & (F.text | F.caption))
async def anti_link_cv_spam_handler(message: types.Message):
    """
    Handler gabungan untuk anti-link, anti-bot mention, anti-CV, anti-spam, dan anti-keywords di grup
    Prioritas: Link > Bot Mention > CV > Spam > Keywords

    Keywords yang diblokir: set, sets, data, file, files
    Hanya admin grup yang bisa kirim pesan dengan kata-kata tersebut
    """
    # Skip jika tidak ada text sama sekali
    all_texts = extract_all_text_from_message(message)
    if not all_texts:
        return

    # Cek apakah pengirim adalah admin grup
    user_id = message.from_user.id
    is_admin = await is_group_admin(message, user_id)

    # PRIORITAS 1: Cek Link (Enhanced Detection)
    has_link = False

    # Cek text utama
    if message.text and contains_link(message.text):
        has_link = True

    # Cek caption
    if not has_link and message.caption and contains_link(message.caption):
        has_link = True

    # Cek reply message jika ada
    if not has_link and message.reply_to_message:
        if message.reply_to_message.text and contains_link(message.reply_to_message.text):
            has_link = True
        elif message.reply_to_message.caption and contains_link(message.reply_to_message.caption):
            has_link = True

    if has_link:
        # Jika admin, biarkan link
        if is_admin:
            return

        # Jika bukan admin, hapus pesan dengan link
        await delete_message_safely(message)
        return  # Stop processing jika sudah hapus link

    # PRIORITAS 2: Cek Bot Mention (NEW)
    has_bot_mention = False

    # Cek text utama
    if message.text and contains_bot_mention(message.text):
        has_bot_mention = True

    # Cek caption
    if not has_bot_mention and message.caption and contains_bot_mention(message.caption):
        has_bot_mention = True

    # Cek reply message jika ada
    if not has_bot_mention and message.reply_to_message:
        if message.reply_to_message.text and contains_bot_mention(message.reply_to_message.text):
            has_bot_mention = True
        elif message.reply_to_message.caption and contains_bot_mention(message.reply_to_message.caption):
            has_bot_mention = True

    if has_bot_mention:
        # Jika admin, biarkan mention bot
        if is_admin:
            return

        # Jika bukan admin, hapus pesan dengan mention bot
        await delete_message_safely(message)
        return  # Stop processing jika sudah hapus bot mention

    # PRIORITAS 3: Cek CV (jika tidak ada link dan bot mention)
    try:
        from management.auto_delete_cv import cv_deleter

        # Jika admin, biarkan CV
        if is_admin:
            return

        # Cek CV dengan enhanced text extraction
        enhanced_text = extract_all_text_from_message_enhanced(message)
        has_cv = cv_deleter.contains_cv_word_enhanced(enhanced_text) if enhanced_text else False

        if has_cv:
            # Hapus pesan CV
            import asyncio
            await asyncio.sleep(0.5)  # Delay 0.5 detik
            success = await delete_message_safely(message)
            if success:
                cv_deleter.deleted_count += 1
            return

    except Exception:
        # Silent error
        pass

    # PRIORITAS 4: Cek Spam (jika tidak ada link, bot mention, dan CV)
    try:
        from management.anti_spam import check_duplicate_spam

        # Cek dan handle duplicate spam
        # Function ini sudah handle admin immunity di dalamnya
        spam_deleted = await check_duplicate_spam(message)
        if spam_deleted:
            return  # Stop processing jika sudah hapus spam

    except Exception:
        # Silent error - jangan crash handler utama
        pass

    # PRIORITAS 5: Cek Keywords (jika tidak ada link, bot mention, CV, dan spam)
    try:
        from management.anti_pesan import contains_blocked_keywords

        # Jika admin, biarkan keywords
        if is_admin:
            return

        # Cek keywords dengan enhanced text extraction
        enhanced_text = extract_all_text_from_message_enhanced(message)
        has_keywords = contains_blocked_keywords(enhanced_text) if enhanced_text else False

        if has_keywords:
            # Hapus pesan dengan keywords terlarang
            success = await delete_message_safely(message)
            return

    except Exception:
        # Silent error - jangan crash handler utama
        pass
